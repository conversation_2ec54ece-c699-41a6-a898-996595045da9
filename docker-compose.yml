services:
  app:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - redis
    volumes:
      - .:/gojiberri
    environment:
      - REDIS_URL=redis://redis:6379/0
    command: >
      bash -c "
      poetry run uvicorn main:app --host 0.0.0.0 --port 8000 &
      poetry run celery -A gojiberri.celery_worker.celery_app worker --loglevel=info &
      wait
      "
      
  redis:
    image: redis:7
    volumes:
      - redisdata:/data
    command: ["redis-server", "--appendonly", "yes"]
    ports:
      - "6379:6379"
      
  flower:
    image: mher/flower
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      
  test:
    build:
      context: .
      target: development
      args:
        INSTALL_TEST_DEPS: "true" 
    volumes:
      - .:/gojiberri
    environment:
      - PYTHONPATH=/gojiberri
      - DISPLAY=:99
      - CHROME_BIN=/usr/bin/chromium
      - CHROME_PATH=/usr/lib/chromium/

volumes:
  redisdata:
