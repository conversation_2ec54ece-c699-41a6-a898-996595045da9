# gojiberri

Neurology Clinic App

## Building and Running the App

You will need to do this the first time you run the app, or if you make any changes to the Dockerfile, docker_compose, poetry.toml files

1. docker compose up --build

   This will:

   a) build the app image

   b) start the app container (running both <PERSON><PERSON><PERSON> and the Celery worker)

   c) start the redis container (as task queue broker)

2. Once it's running, open your browser to http://localhost:8000/

## Stopping the App

1. Ctrl + C
2. docker compose down

## Restarting the App after making code changes

If you only change the python backend and UI code, you don't need to rebuild the Docker image. You can just restart by running

1. docker compose restart

To see the logs like you would with a full rebuild: 2) docker compose logs -f

## Docker Image Builds

### Build Production Image

```bash
make docker-build-prod
```

Builds the Docker image used to run the production application using docker-compose.

### Build Test Image

```bash
make docker-build-test
```

Builds the Docker image used specifically for running tests in development .

## Testing and Coverage

### Run All Tests

Run all tests in the gojiberri/ui/ directory:

```bash
make tests
```

This also cleans up screenshot artifacts after running tests.

### Run with Coverage

Run tests with coverage report:

```bash
make coverage
```

Outputs line-by-line coverage stats and removes screenshot directories.

### Run Specific Test File

Run a specific test file:

```bash
make test-file FILE=path/to/test_file.py

```

Example:

```bash
make test-file FILE=gojiberri/ui/tests/test_layout/test_workflow.py
```

### Clean Test Artifacts

Remove screenshots, caches, and other temporary test files:

```bash
make clean-test
```

## Code Quality Checks

Formats, lints, and type-checks the codebase using Black, Ruff, and Mypy

```bash
make lint
```

## Reference

- [Gojiberri Documentation](https://docs.google.com/document/d/1aPUgWYDJnOMw33PU8y1X6wQ3pNgocK0205LgW5JyhoQ/edit?usp=drive_link)
