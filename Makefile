.PHONY: run test test-file coverage coverage-file clean docker-build-prod docker-build-test clean-test cleanup lint help

SERVICE=test
CLEAN_CMD=find /gojiberri \( -type d \( -name '__pycache__' -o -name '.pytest_cache' -o -name 'screenshots' \) -exec rm -rf {} + -o -type f -name '.coverage' -delete \) 2>/dev/null || true

# Help message to show available targets
help:
	@echo "Makefile commands:"
	@echo "  lint                Run code formatting checks (black), linting (ruff), and type checking (mypy)"
	@echo "  test                Run all tests using pytest"
	@echo "  test-file           Run a specific test file. Usage: make test-file FILE=tests/your_test.py"
	@echo "  coverage            Run tests with coverage and report the missing lines"
	@echo "  clean-test          Clean test artifacts"
	@echo "  clean               Stop containers and clean all volumes + artifacts"
	@echo "  docker-build-prod   Build Docker image for production"
	@echo "  docker-build-test   Build Docker image for testing"
	@echo "  run                 Run the production app (docker-compose up app redis flower)"
	@echo "  cleanup             Clean up cached files and artifacts"

# Build Docker images for production
docker-build-prod:
	docker-compose build app

# Build Docker images for testing
docker-build-test:
	docker-compose build test

# Run the production app (docker-compose up)
run:
	docker-compose up app redis flower

# Run all tests using pytest
test:
	docker-compose run --rm test poetry run pytest -v -W ignore::DeprecationWarning gojiberri/ui/tests/
	$(MAKE) cleanup

# Run a specific test file. Example usage: make test-file FILE=tests/your_test.py
test-file:
	docker-compose run --rm test poetry run pytest -v -W ignore::DeprecationWarning $(FILE)
	$(MAKE) cleanup

# Run coverage on all tests and report missing coverage
coverage:
	docker-compose run --rm test poetry run pytest --cov=gojiberri --cov-report=term-missing --cov-config=.coveragerc  -W ignore::DeprecationWarning gojiberri/ui/tests/
	$(MAKE) cleanup

# Lint code with Black, Ruff, and Mypy
lint:
	# Run Black to format python code
	poetry run black .
	
	# Run Mypy for type checking
	poetry run mypy .

	# Run Ruff to lint the code
	poetry run ruff check --fix .
	
	

# Clean test artifacts
clean-test: cleanup

# Stop Docker containers and clean all volumes + artifacts
clean:
	docker-compose down -v
	$(MAKE) cleanup

# Shared cleanup routine for removing cache files and artifacts
cleanup:
	docker-compose run --rm --user root $(SERVICE) sh -c "$(CLEAN_CMD)"
