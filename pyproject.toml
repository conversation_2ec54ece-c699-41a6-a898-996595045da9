[tool.poetry]
name = "gojiberri"
version = "0.1.0"
description = "Neurology clinic app with FastAPI and Celery"
authors = ["<PERSON> <<EMAIL>>"]
packages = [
  { include = "gojiberri", from = "." }
]

[tool.poetry.dependencies]
python = ">=3.11,<3.14"
fastapi = "0.115.12"
nicegui = "2.16.0"
uvicorn = "0.34.2"
sqlalchemy = "^2.0"
psycopg2-binary = "^2.9"
python-dotenv = "1.1.0"
pydantic = "2.11.4"
celery = "^5.3"
redis = "^5.0"
requests = "2.32.3"
pytesseract = "^0.3.10"
Pillow = "^10.2"
PyMuPDF = "^1.23.9"
llama-cpp-python = "*"
flower = "^2.0.1"
aiofiles = "24.1.0"
aiohappyeyeballs = "2.6.1"
aiohttp = "3.11.18"
aiosignal = "1.3.2"
annotated-types = "0.7.0"
anyio = "4.9.0"
attrs = "25.3.0"
bidict = "0.23.1"
certifi = "2025.4.26"
charset-normalizer = "3.4.2"
click = "8.1.8"
coverage = "7.8.0"
docutils = "0.21.2"
frozenlist = "1.6.0"
h11 = "0.16.0"
httpcore = "1.0.9"
httptools = "0.6.4"
httpx = "0.28.1"
idna = "3.10"
ifaddr = "0.2.0"
iniconfig = "2.1.0"
itsdangerous = "2.2.0"
jinja2 = "3.1.6"
markdown2 = "2.5.3"
markupsafe = "3.0.2"
multidict = "6.4.3"
orjson = "3.10.18"
outcome = "1.3.0.post0"
packaging = "25.0"
pluggy = "1.5.0"
propcache = "0.3.1"
pscript = "0.7.7"
pydantic-core = "2.33.2"
pygments = "2.19.1"
pysocks = "1.7.1"
pytest = "8.3.5"
pytest-cov = "6.1.1"
python-engineio = "4.12.0"
python-multipart = "0.0.20"
python-socketio = "5.13.0"
pyyaml = "6.0.2"
selenium = "4.32.0"
simple-websocket = "1.1.0"
sniffio = "1.3.1"
sortedcontainers = "2.4.0"
starlette = "0.46.2"
trio = "0.30.0"
trio-websocket = "0.12.2"
typing-inspection = "0.4.0"
typing-extensions = "4.13.2"
urllib3 = "2.4.0"
uvloop = "0.21.0"
vbuild = "0.8.2"
wait-for2 = "0.3.2"
watchfiles = "1.0.5"
webdriver-manager = "4.0.2"
websocket-client = "1.8.0"
websockets = "15.0.1"
wsproto = "1.2.0"
yarl = "1.20.0"
aiosqlite = "^0.21.0"

[tool.poetry.group.dev.dependencies]
black = "^25.1.0"
ruff = "^0.11.9"
mypy = "^1.15.0"
pytest-asyncio = "^1.0.0"

