# Stage 1: Build Common Base
FROM python:3.12 AS base

# Install common dependencies (curl, build-essential, etc.)
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    cmake \
    libopenblas-dev \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
ENV POETRY_VERSION=1.8.2
RUN curl -sSL https://install.python-poetry.org | python3 -
ENV PATH="/root/.local/bin:$PATH"

# Stage 2: Development / Build
FROM base AS development

# Build argument to optionally include test dependencies
ARG INSTALL_TEST_DEPS=false

# Optionally install test-only dependencies 
RUN if [ "$INSTALL_TEST_DEPS" = "true" ]; then \
    apt-get update && apt-get install -y \
    chromium \
    chromium-driver \
    && rm -rf /var/lib/apt/lists/*; \
    fi

# Set development-specific environment variables (only for development)
ENV DISPLAY=:99
ENV CHROME_BIN=/usr/bin/chromium
ENV CHROME_PATH=/usr/lib/chromium/

# Set working directory
WORKDIR /gojiberri

# Copy the project files
COPY . .

# Install project dependencies using Poetry
RUN poetry install --no-interaction --no-ansi

# Stage 3: Production Runtime
FROM base AS production

# Set working directory
WORKDIR /gojiberri

# Copy only the necessary files from the development stage
COPY --from=development /gojiberri /gojiberri
COPY --from=development /root/.cache /root/.cache

# Set environment variables
ENV PYTHONPATH=/gojiberri

# Default command (can be overridden if needed)
CMD ["poetry", "run", "python", "main.py"]
