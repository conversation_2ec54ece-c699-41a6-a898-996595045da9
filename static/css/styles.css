body {
    font-family: "Noto Sans", Arial, Helvetica, sans-serif;
}
.sidebar-item {
    transition: background-color 0.3s;
}
.sidebar-item:hover {
    background-color: #f3f4f6;
}
.nicegui-content {
    padding: 0 !important;
}


/* 
 * static/css/loading.css
 * CSS styles for the loading component
 */

 @keyframes spinner-animation {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
}

.loading-overlay.full-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
}

.loading-overlay.container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
}

.loading-card {
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background-color: white;
    border-radius: 0.5rem;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.spinner-container {
    animation: spinner-animation 1.5s linear infinite;
}

.spinner-icon {
    color: #3B82F6;
}

.spinner-icon.sm {
    font-size: 24px;
}

.spinner-icon.md {
    font-size: 32px;
}

.spinner-icon.lg {
    font-size: 48px;
}

.spinner-icon.xl {
    font-size: 64px;
}

.loading-message {
    color: #374151;
    font-weight: 500;
}

.hidden {
    display: none;
}

@keyframes spinner-animation {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
.custom-spinner {
    animation: spinner-animation 1.5s linear infinite;
}

/* text area styles on transcription review section */
.q-textarea.q-field--labeled .q-field__native {
    min-height: 560px !important;
    max-height: 100% !important;
    height: 100% !important;
    flex-grow: 1;
    padding-top: 1px;
}
.q-field__control {
    color: rgb(0, 0, 0);
    height: 56px;
    max-width: 100%;
    outline: none;
}