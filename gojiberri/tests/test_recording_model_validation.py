"""
Tests for Recording Pydantic model validation.
Verifies that the enhanced validation logic works correctly.
"""

import pytest
from datetime import datetime, timezone, timedelta
from pydantic import ValidationError

from gojiberri.models.recording.recording import Recording


class TestRecordingModelValidation:
    """Test suite for Recording model Pydantic validation."""

    def test_valid_recording_creation(self):
        """Test creating a valid recording with all required fields."""
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "*************-4321-4321-************",
            "file_path": "recordings/test.webm",
            "duration_milliseconds": 5000.0,
            "end_time": datetime.now(timezone.utc),
        }

        recording = Recording(**recording_data)

        assert recording.recording_id == recording_data["recording_id"]
        assert recording.session_id == recording_data["session_id"]
        assert recording.file_path == recording_data["file_path"]
        assert (
            recording.duration_milliseconds == recording_data["duration_milliseconds"]
        )

    def test_invalid_recording_id_format(self):
        """Test that invalid recording ID format raises ValidationError."""
        recording_data = {
            "recording_id": "invalid-uuid-format",
            "session_id": "*************-4321-4321-************",
        }

        with pytest.raises(ValidationError) as exc_info:
            Recording(**recording_data)

        assert "recording_id" in str(exc_info.value)

    def test_invalid_session_id_format(self):
        """Test that invalid session ID format raises ValidationError."""
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "invalid-session-id",
        }

        with pytest.raises(ValidationError) as exc_info:
            Recording(**recording_data)

        assert "session_id" in str(exc_info.value)

    def test_file_path_validation_security_check(self):
        """Test that file path with security issues raises ValidationError."""
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "*************-4321-4321-************",
            "file_path": "../../../etc/passwd",  # Path traversal attempt
        }

        with pytest.raises(ValidationError) as exc_info:
            Recording(**recording_data)

        assert "security patterns" in str(exc_info.value)

    def test_file_path_validation_invalid_extension(self):
        """Test that file path with invalid extension raises ValidationError."""
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "*************-4321-4321-************",
            "file_path": "recordings/test.txt",  # Invalid extension
        }

        with pytest.raises(ValidationError) as exc_info:
            Recording(**recording_data)

        assert "Invalid file extension" in str(exc_info.value)

    def test_file_path_validation_max_length(self):
        """Test that file path exceeding max length raises ValidationError."""
        long_path = "recordings/" + "a" * 500 + ".webm"  # Exceeds 512 chars
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "*************-4321-4321-************",
            "file_path": long_path,
        }

        with pytest.raises(ValidationError) as exc_info:
            Recording(**recording_data)

        assert "ensure this value has at most 512 characters" in str(exc_info.value)

    def test_duration_validation_negative_value(self):
        """Test that negative duration raises ValidationError."""
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "*************-4321-4321-************",
            "duration_milliseconds": -1000.0,
        }

        with pytest.raises(ValidationError) as exc_info:
            Recording(**recording_data)

        assert "greater than or equal to 0" in str(exc_info.value)

    def test_duration_validation_too_short(self):
        """Test that duration less than 1 second raises ValidationError."""
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "*************-4321-4321-************",
            "duration_milliseconds": 500.0,  # Less than 1 second
        }

        with pytest.raises(ValidationError) as exc_info:
            Recording(**recording_data)

        assert "at least 1 second" in str(exc_info.value)

    def test_duration_validation_too_long(self):
        """Test that duration exceeding maximum raises ValidationError."""
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "*************-4321-4321-************",
            "duration_milliseconds": 90000000.0,  # More than 24 hours
        }

        with pytest.raises(ValidationError) as exc_info:
            Recording(**recording_data)

        assert "less than or equal to" in str(exc_info.value)

    def test_end_time_validation_future_date(self):
        """Test that end time in the future raises ValidationError."""
        future_time = datetime.now(timezone.utc) + timedelta(hours=1)
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "*************-4321-4321-************",
            "end_time": future_time,
        }

        with pytest.raises(ValidationError) as exc_info:
            Recording(**recording_data)

        assert "cannot be in the future" in str(exc_info.value)

    def test_valid_file_extensions(self):
        """Test that all valid file extensions are accepted."""
        valid_extensions = [".webm", ".mp3", ".wav", ".ogg", ".m4a"]

        for ext in valid_extensions:
            recording_data = {
                "recording_id": "12345678-1234-1234-1234-123456789012",
                "session_id": "*************-4321-4321-************",
                "file_path": f"recordings/test{ext}",
                "duration_milliseconds": 5000.0,
            }

            # Should not raise ValidationError
            recording = Recording(**recording_data)
            assert recording.file_path == f"recordings/test{ext}"

    def test_optional_fields_default_values(self):
        """Test that optional fields have correct default values."""
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "*************-4321-4321-************",
        }

        recording = Recording(**recording_data)

        assert recording.file_path is None
        assert recording.duration_milliseconds is None
        assert recording.end_time is None
        assert recording.is_active is True

    def test_to_dict_method(self):
        """Test that to_dict method works correctly."""
        recording_data = {
            "recording_id": "12345678-1234-1234-1234-123456789012",
            "session_id": "*************-4321-4321-************",
            "file_path": "recordings/test.webm",
            "duration_milliseconds": 5000.0,
        }

        recording = Recording(**recording_data)
        result_dict = recording.to_dict()

        assert isinstance(result_dict, dict)
        assert result_dict["recording_id"] == recording_data["recording_id"]
        assert result_dict["session_id"] == recording_data["session_id"]
        assert result_dict["file_path"] == recording_data["file_path"]
        assert (
            result_dict["duration_milliseconds"]
            == recording_data["duration_milliseconds"]
        )
