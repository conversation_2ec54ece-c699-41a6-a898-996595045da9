import unittest
from datetime import datetime, timezone

from fastapi.testclient import TestClient
from gojiberri.app import app
from models.enums import StepType, StepStatus
from gojiberri.celery_worker import celery_app

celery_app.conf.update(
    task_always_eager=True,
    task_eager_propagates=True,  # Optional: lets exceptions bubble up
)


class SessionApiTestCase(unittest.TestCase):
    def setUp(self):
        self.client = TestClient(app)

    def test_create_session(self):
        print("test_create_session")
        response = self.client.post(
            "/sessions", json={"patient_name": "Test User", "patient_dob": "1990-01-01"}
        )
        assert response.status_code == 200
        data = response.json()
        assert "session_id" in data

    def test_list_sessions(self):
        response = self.client.get("/sessions")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_get_session_by_id(self):
        # First, create a session
        create_response = self.client.post(
            "/sessions",
            json={"patient_name": "Test Fetch", "patient_dob": "1980-06-15"},
        )
        session_id = create_response.json()["session_id"]

        # Now try to fetch it
        get_response = self.client.get(f"/sessions/{session_id}")
        assert get_response.status_code == 200
        session = get_response.json()
        assert session["session_id"] == session_id
        assert session["patient_name"] == "Test Fetch"

    def test_get_session_status_by_id(self):
        # First, create a session
        create_response = self.client.post(
            "/sessions",
            json={"patient_name": "Test Fetch", "patient_dob": "1980-06-15"},
        )
        session_id = create_response.json()["session_id"]

        # Now try to fetch it
        get_response = self.client.get(f"/sessions/{session_id}/status")
        assert get_response.status_code == 200
        session = get_response.json()
        assert session["session_id"] == session_id
        assert session["current_step"]["step"] == StepType.RECORDING.value
        assert session["current_step"]["step_status"] == StepStatus.AWAITING_USER_INPUT

    def test_complete_recording(self):
        # First, create a session
        create_response = self.client.post(
            "/sessions",
            json={"patient_name": "Test Fetch", "patient_dob": "1980-06-15"},
        )
        session_id = create_response.json()["session_id"]
        f = open("TestRecording.m4a", "rb")  # Keep the file open
        try:
            files = {"file": ("TestRecording.m4a", f, "audio/x-m4a")}
            data = {
                "duration_seconds": "12.34",
                "end_time": datetime.now(timezone.utc).isoformat(),
            }

            response = self.client.post(
                f"/sessions/{session_id}/recording", files=files, data=data
            )
            assert response.status_code == 200
            # assert response.json()["success"] == True
        finally:
            f.close()  # Close the file explicitly
