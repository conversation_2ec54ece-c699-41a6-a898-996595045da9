# TODO - This entire file is a mock implementation and will be replaced with real services
import random
import time
from datetime import datetime, timezone
from typing import Any, Dict
from gojiberri.models.enums import StepType


# Optimized progress and results tracking with single storage
progress_store = {}  # session_id -> {step_type: {start_time, percentage, status}}
results_store = {}  # session_id -> {step_type: {result_data, generated_at, available}}


def calculate_progress_with_results(
    session_id: str, step_type: StepType
) -> Dict[str, Any]:
    """Calculate progress with automatic result generation when completed."""
    if session_id not in progress_store:
        progress_store[session_id] = {}

    if step_type not in progress_store[session_id]:
        progress_store[session_id][step_type] = {
            "start_time": time.time(),
            "percentage": 0,
            "status": "in_progress",
        }

    progress = progress_store[session_id][step_type]
    elapsed = time.time() - progress["start_time"]

    if progress["percentage"] < 100:
        # Optimized progress calculation
        increment = (
            random.randint(2, 8)
            if progress["percentage"] < 50
            else random.randint(1, 4)
        )
        time_progress = min(int(elapsed / 90.0 * 100), 100)
        progress["percentage"] = min(
            100, max(progress["percentage"] + increment, time_progress)
        )

        # Auto-generate results when progress reaches 100%
        if progress["percentage"] >= 100:
            progress["status"] = "completed"
            _auto_generate_results(session_id, step_type)

    return progress


def get_progress_status(session_id: str, step_type: StepType) -> Dict[str, Any]:
    """Get current progress status without modifying it."""
    if session_id not in progress_store or step_type not in progress_store[session_id]:
        return {"percentage": 0, "status": "not_started"}
    return progress_store[session_id][step_type]


def _auto_generate_results(session_id: str, step_type: StepType):
    """Auto-generate results when step completes - called by progress calculation."""
    if session_id not in results_store:
        results_store[session_id] = {}

    if step_type not in results_store[session_id]:
        result_data = _generate_step_results(session_id, step_type)
        results_store[session_id][step_type] = {
            "result_data": result_data,
            "generated_at": datetime.now(timezone.utc),
            "available": True,
        }


def _generate_step_results(session_id: str, step_type: StepType) -> Dict[str, Any]:
    """Generate mock results for different step types."""
    if step_type == StepType.TRANSCRIPTION:
        return _generate_transcription_results(session_id)
    else:
        return {"message": f"Results generation not implemented for {step_type.value}"}


def _generate_transcription_results(session_id: str) -> Dict[str, Any]:
    """Generate realistic mock transcription results."""
    return {
        "transcription": f"""Patient: I've been having some issues over the past few weeks that I'm concerned about.

Doctor: I see. Can you tell me more about what you've been experiencing? When did these symptoms first start?

Patient: It started about two weeks ago. I've been feeling more tired than usual, and I've noticed some changes in my sleep patterns.

Doctor: That's important information. Let's discuss this in more detail and see how we can help you feel better.

[Mock transcription data for session {session_id}]
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Confidence: 95%"""
    }
