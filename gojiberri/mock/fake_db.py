from datetime import datetime, timezone
from typing import Dict

from gojiberri.models.recording.recording import Recording
from gojiberri.models.session.session import Session

workflow_db = {}  # Simulated in-memory DB for session state

# Create mock sessions
mock_sessions: Dict[str, Session] = {}

# mock_sessions["61d1e99d-723a-44f1-b707-be4bef12da1d"] = create_new_session(
#     "<PERSON> Nguyen", date(1990, 2, 15)
# )
# mock_sessions["6e4f61d9-8847-4ce4-b3fa-e5fe444f1805"] = create_new_session(
#     "<PERSON> Chen", date(1985, 7, 22)
# )
# mock_sessions["c84c6a9e-0355-4969-bca2-ef82deef3eed"] = create_new_session(
#     "<PERSON> Rivera", date(1975, 9, 3)
# )

# mock_sessions["61d1e99d-723a-44f1-b707-be4bef12da1d"].history.append(
#     StepProgress(
#         step=StepType.RECORDING,
#         step_status=StepStatus.COMPLETED,
#         start_time=datetime.now(),
#         last_updated=datetime.now(),
#     )
# )
# mock_sessions["61d1e99d-723a-44f1-b707-be4bef12da1d"].history.append(
#     StepProgress(
#         step=StepType.TRANSCRIPTION,
#         step_status=StepStatus.IN_PROGRESS,
#         start_time=datetime.now(),
#         last_updated=datetime.now(),
#     )
# )
# mock_sessions["61d1e99d-723a-44f1-b707-be4bef12da1d"].current_step = mock_sessions[
#     "61d1e99d-723a-44f1-b707-be4bef12da1d"
# ].history[1]

# mock_sessions["6e4f61d9-8847-4ce4-b3fa-e5fe444f1805"].history.append(
#     StepProgress(
#         step=StepType.RECORDING,
#         step_status=StepStatus.COMPLETED,
#         start_time=datetime.now(),
#         last_updated=datetime.now(),
#     )
# )
# mock_sessions["6e4f61d9-8847-4ce4-b3fa-e5fe444f1805"].history.append(
#     StepProgress(
#         step=StepType.TRANSCRIPTION,
#         step_status=StepStatus.COMPLETED,
#         start_time=datetime.now(),
#         last_updated=datetime.now(),
#     )
# )
# mock_sessions["6e4f61d9-8847-4ce4-b3fa-e5fe444f1805"].history.append(
#     StepProgress(
#         step=StepType.DOCUMENT_UPLOAD_OCR,
#         step_status=StepStatus.AWAITING_REVIEW,
#         start_time=datetime.now(),
#         last_updated=datetime.now(),
#     )
# )
# mock_sessions["6e4f61d9-8847-4ce4-b3fa-e5fe444f1805"].current_step = mock_sessions[
#     "6e4f61d9-8847-4ce4-b3fa-e5fe444f1805"
# ].history[2]

workflow_db["Session"] = mock_sessions

mock_recordings: Dict[str, Recording] = {}


# row = db.fetch_one("SELECT * FROM sessions WHERE session_id = ?", (session_id,))
# session = Session.model_validate(dict(row))


def update_session(session: Session) -> None:
    """
    Updates the given session in the mock DB and sets the last_updated timestamp.
    """
    session.last_updated = datetime.now(timezone.utc)
    mock_sessions[str(session.session_id)] = session
