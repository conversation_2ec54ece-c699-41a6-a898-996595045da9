# config_loader.py - Backend Configuration Loader
"""Configuration loader for backend services that reads from config.yaml."""

import yaml
from typing import Dict, Any
from pathlib import Path

from gojiberri.models.enums import WorkflowType


class ConfigLoader:
    """Centralized configuration loader for backend services."""

    _instance = None
    _config = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigLoader, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._config is None:
            self._load_config()

    def _load_config(self):
        """Load configuration from YAML file."""
        try:
            config_path = Path(__file__).parent / "config.yaml"
            with open(config_path, "r") as file:
                self._config = yaml.safe_load(file)
        except Exception as e:
            print(f"⚠️ Error loading config: {e}")
            self._config = self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Provide default configuration if YAML file is not available."""
        return {
            "database": {
                "echo": False,
                "future": True,
                "pool_pre_ping": True,
                "pool_recycle": 3600,
                "max_overflow": 20,
            },
            "recording": {
                "validations": {
                    "max_recording_duration_ms": 86400000,
                    "min_duration_ms": 500,
                    "min_file_size_bytes": 512,
                    "valid_audio_extensions": [".webm", ".mp3", ".wav", ".ogg", ".m4a"],
                },
            },
            "workflow": {
                "validations": {
                    "allowed_workflow_types": [WorkflowType.DOCUMENT_GENERATION.value],
                },
            },
            "logging": {
                "logger": "file",
                "log_level": "DEBUG",
                "console_enabled": True,
                "file_logging_enabled": True,
                "rotation_when": "midnight",
                "rotation_interval": 1,
                "time_backup_count": 7,
                "max_log_file_size_mb": 10,
                "size_backup_count": 5,
            },
        }

    def get(self, key_path: str, default=None):
        """
        Get configuration value using dot notation.

        Args:
            key_path: Dot-separated path to the configuration value
            default: Default value if key is not found

        Returns:
            Configuration value or default
        """
        try:
            keys = key_path.split(".")
            value = self._config

            for key in keys:
                value = value[key]

            return value
        except (KeyError, TypeError):
            return default

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get entire configuration section.

        Args:
            section: Configuration section name

        Returns:
            Configuration section dictionary
        """
        return self._config.get(section, {})


# Global configuration instance
config = ConfigLoader()
