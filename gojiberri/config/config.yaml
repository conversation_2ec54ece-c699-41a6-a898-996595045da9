# config.yaml - Backend Configuration
# Service-based configuration structure for better organization and maintainability

# Database Configuration (keep existing structure)
database:
  # Connection settings
  dev_database_url: "sqlite+aiosqlite:///./gojiberri_app.db"  # Default SQLite database URL
  echo: true  # Set to true for SQL query logging during development
  # Connection pool settings
  pool_pre_ping: true      # Validate connections before use
  pool_recycle: 3600       # Recycle connections every hour (seconds)
  max_overflow: 20         # Allow up to 20 overflow connections

# Recording service - consolidate all recording configs
recording:

  validations:
    max_recording_duration_ms: 86400000  # Move from validation.max_recording_duration_ms
    valid_audio_extensions:  # Move from validation.valid_audio_extensions
      - ".webm"
      - ".mp3"
      - ".wav"
      - ".ogg"
      - ".m4a"


# Workflow service
workflow:
  validations:
    allowed_workflow_types:  # Move from validation.allowed_workflow_types
      - "document_generation"
      
# Consolidated logging configuration
logging:  # Move all root-level logging configs here
  logger: "file"
  log_level: "DEBUG"
  console_enabled: true
  file_logging_enabled: true
  rotation_when: "midnight"
  rotation_interval: 1
  time_backup_count: 7
  max_log_file_size_mb: 10
  size_backup_count: 5