# Celery Task Settings
task_acks_late = True
task_reject_on_worker_lost = True  # If the worker dies while running a task, requeue the task for another worker to retry.
task_time_limit = 7200
task_soft_time_limit = 6900
result_expires = 86400
task_serializer = "json"
result_serializer = "json"
accept_content = ["json"]
broker_transport_options = {"visibility_timeout": 86400}
worker_prefetch_multiplier = 1  # Only allow workers to prefetch 1 task at a time
