from typing import List
from pydantic import BaseModel

from gojiberri.models.enums import StepType, StepStatus


class StepProgressResponse(BaseModel):
    """Generic response model for workflow step progress."""

    session_id: str
    step_type: StepType
    percentage: int  # 0-100
    status: StepStatus


class StepResponse(BaseModel):
    """API response model"""

    step_name: str
    icon: str
    label: str
    status: str


class WorkflowStepsResponse(BaseModel):
    """Response model for workflow steps configuration."""

    steps: List[StepResponse]
