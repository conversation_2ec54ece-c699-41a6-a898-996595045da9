"""
Shared enums for Gojiberri application.
Centralized location for all enums to prevent circular imports.
"""

from enum import Enum


class StepType(str, Enum):
    RECORDING = "recording"
    TRANSCRIPTION = "transcription"
    DOCUMENT_UPLOAD_OCR = "document_upload_ocr"
    DEIDENTIFICATION = "deidentification"
    DOCUMENT_GENERATION = "document_generation"


class StepStatus(str, Enum):
    NOT_STARTED = "not_started"
    AWAITING_USER_INPUT = "awaiting_user_input"
    IN_PROGRESS = "in_progress"
    AWAITING_REVIEW = "awaiting_review"
    COMPLETED = "completed"
    SKIPPED = "skipped"
    ERROR = "error"


class SessionStatus(str, Enum):
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ERROR = "error"


class WorkflowType(str, Enum):
    DOCUMENT_GENERATION = "document_generation"


class RecordingStatus(str, Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ERROR = "error"
