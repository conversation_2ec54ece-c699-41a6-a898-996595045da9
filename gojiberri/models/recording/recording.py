# gojiberri/models/recording/recording.py
"""
Pydantic model for Recording data with comprehensive validation.

This module defines the Pydantic model for a recording, which is used for API
data validation and serialization. It includes comprehensive field validation,
custom validators, and methods for converting between the Pydantic model and
the SQLAlchemy model.
"""

from __future__ import annotations
from datetime import datetime, timezone
from typing import Optional, Dict, Any, Self

from pydantic import BaseModel, Field, field_validator

from gojiberri.config.config_loader import config
from gojiberri.database.models.recording_model import (
    Recording as SQLAlchemyRecording,
)


class Recording(BaseModel):
    """
    Pydantic model representing a recording with comprehensive validation.

    This model is used for request and response validation, and for clear
    data structuring within the application logic. It includes comprehensive
    field validation, custom validators, and conversion methods to and from
    the SQLAlchemy model.
    """

    recording_id: str = Field(
        ...,
        description="Unique identifier for the recording.",
        pattern=r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
    )
    session_id: str = Field(
        ...,
        description="Identifier for the session this recording belongs to.",
        pattern=r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
    )
    file_path: Optional[str] = Field(
        None, description="The path to the recording file.", max_length=512
    )
    duration_milliseconds: Optional[float] = Field(
        None,
        description="The duration of the recording in milliseconds.",
        ge=0.0,  # Must be non-negative
        le=config.get(
            "recording.validations.max_recording_duration_ms", 86400000.0
        ),  # Max 24 hours in milliseconds by default
    )
    end_time: Optional[datetime] = Field(
        None, description="The time the recording ended."
    )
    created_at: Optional[datetime] = Field(
        None, description="The time the recording was created."
    )
    updated_at: Optional[datetime] = Field(
        None, description="The time the recording was last updated."
    )
    is_active: bool = Field(True, description="Whether the recording is active.")

    @field_validator("file_path")
    def validate_file_path(cls, v):
        """Validate file path format for security and consistency."""
        if v is None:
            return v

        # Basic security checks
        if ".." in v or v.startswith("/"):
            raise ValueError("File path contains invalid security patterns")

        # Check for valid file extension
        valid_extensions = config.get(
            "recording.validations.valid_audio_extensions", [".webm"]
        )
        file_lower = v.lower()

        if not any(file_lower.endswith(ext) for ext in valid_extensions):
            raise ValueError(
                f"Invalid file extension. Allowed: {', '.join(valid_extensions)}"
            )

        return v.strip()

    @field_validator("duration_milliseconds")
    def validate_duration(cls, v):
        """Validate recording duration is reasonable."""
        if v is None:
            return v

        # Additional business logic validation beyond Field constraints
        if v < 1000:  # Less than 1 second
            raise ValueError("Recording duration must be at least 1 second (1000ms)")

        # Check against config-defined maximum if available
        max_duration = config.get(
            "recording.validations.max_recording_duration_ms", 86400000
        )  # 24 hours default
        if v > max_duration:
            raise ValueError(
                f"Recording duration exceeds maximum allowed ({max_duration}ms)"
            )

        return v

    @field_validator("end_time")
    def validate_end_time(cls, v):
        """Validate end time is not in the future."""
        if v is None:
            return v

        # Ensure end time is not in the future (with small tolerance for clock skew)
        now = datetime.now(timezone.utc)
        if v > now:
            raise ValueError("Recording end time cannot be in the future")

        return v

    def to_sqlalchemy(self) -> SQLAlchemyRecording:
        """
        Converts the Pydantic model to a SQLAlchemy model.

        This method handles the mapping of fields from the Pydantic model to the
        SQLAlchemy model, including the conversion of `duration_milliseconds` to
        `duration`.

        Returns:
            SQLAlchemyRecording: The SQLAlchemy model instance.
        """
        return SQLAlchemyRecording(
            recording_id=self.recording_id,
            session_id=self.session_id,
            file_path=self.file_path,
            duration=self.duration_milliseconds,
            end_time=self.end_time,
            created_at=self.created_at,
            updated_at=self.updated_at,
            is_active=self.is_active,
        )

    @classmethod
    def from_sqlalchemy(cls, db_obj: SQLAlchemyRecording) -> Optional[Self]:
        """
        Converts a SQLAlchemy model to a Pydantic model.

        This class method handles the mapping of fields from the SQLAlchemy model
        to the Pydantic model, including the conversion of `duration` to
        `duration_milliseconds`.

        Args:
            db_obj (SQLAlchemyRecording): The SQLAlchemy model instance.

        Returns:
           Optional[Self]: The Pydantic model instance or None
        """
        if db_obj is None:
            return None

        return cls(
            recording_id=db_obj.recording_id,
            session_id=db_obj.session_id,
            file_path=db_obj.file_path,
            duration_milliseconds=db_obj.duration,
            end_time=db_obj.end_time,
            created_at=db_obj.created_at,
            updated_at=db_obj.updated_at,
            is_active=db_obj.is_active,
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Converts the Pydantic model to a dictionary.

        This method is used to serialize the model to a dictionary, which can
        then be converted to JSON for API responses.

        Returns:
            Dict[str, Any]: The model as a dictionary.
        """
        return self.model_dump(mode="json")
