from typing import Any, Dict, Optional

from pydantic import BaseModel
from datetime import date
from gojiberri.models.session.session import Session
from gojiberri.models.steps.step import StepProgress
from gojiberri.models.enums import StepType

from gojiberri.models.enums import WorkflowType


class CreateSessionRequest(BaseModel):
    patient_name: str
    patient_dob: date
    workflow_type: Optional[str] = WorkflowType.DOCUMENT_GENERATION.value


class CreateSessionResponse(BaseModel):
    session_id: str


class GetSessionResponse(Session):
    pass


class GetSessionStatusResponse(BaseModel):
    session_id: str
    status: str
    current_step: Optional[StepProgress]


class StepResultResponse(BaseModel):
    """Response model for step results - consistent with StepProgressResponse pattern."""

    session_id: str
    step_type: StepType
    available: bool
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
