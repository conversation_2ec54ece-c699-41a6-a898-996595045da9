from pydantic import BaseModel, field_validator, Field
from typing import Any, Dict, List, Optional, Self
from datetime import datetime, date, timezone
import re
from gojiberri.config.config_loader import config
from gojiberri.models.enums import (
    WorkflowType,
    SessionStatus,
)


from gojiberri.database.models.session_model import Session as SQLAlchemySession
from gojiberri.models.steps.step import StepProgress


class Session(BaseModel):
    session_id: str = Field(
        ..., pattern=r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
    )
    patient_name: str = Field(..., min_length=1, max_length=255)
    patient_dob: date
    session_start: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    session_status: SessionStatus = SessionStatus.IN_PROGRESS
    workflow_type: Optional[WorkflowType] = WorkflowType.DOCUMENT_GENERATION
    current_step: Optional[StepProgress] = None
    history: List[StepProgress] = Field(default_factory=list)
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    @field_validator("patient_name")
    def validate_patient_name(cls, v):
        if not re.match(r"^[a-zA-Z\s\-'.]+$", v):
            raise ValueError("Patient name contains invalid characters")
        return v

    @field_validator("patient_dob")
    def validate_patient_dob(cls, v):
        if v > date.today():
            raise ValueError("Patient date of birth cannot be in the future")
        return v

    @field_validator("workflow_type")
    def validate_workflow_type(cls, v):
        allowed_types = config.get(
            "workflow.validations.allowed_workflow_types",
            [WorkflowType.DOCUMENT_GENERATION.value],
        )

        if v.value not in allowed_types:
            raise ValueError(f"Invalid workflow type: {v}")
        return v

    def to_sqlalchemy(self) -> SQLAlchemySession:
        # Convert history List[StepProgress] to JSON format for database storage
        history_json = None
        if self.history:
            history_json = {
                "step_history": [
                    {
                        "step_type": step.step.value,
                        "step_start_time": (
                            step.start_time.isoformat() if step.start_time else None
                        ),
                        "step_end_time": None,  # Will be set when step is completed
                        "status_history": [
                            {
                                "status": step.step_status.value,
                                "timestamp": (
                                    step.last_updated.isoformat()
                                    if step.last_updated
                                    else None
                                ),
                            }
                        ],
                    }
                    for step in self.history
                ]
            }

        return SQLAlchemySession(
            session_id=self.session_id,
            patient_name=self.patient_name,
            patient_dob=self.patient_dob.isoformat(),
            session_start=self.session_start,
            session_status=self.session_status,
            workflow_type=self.workflow_type,
            current_step_type=self.current_step.step if self.current_step else None,
            current_status=self.current_step.step_status if self.current_step else None,
            updated_at=self.last_updated,
            history=history_json,
        )

    @classmethod
    def from_sqlalchemy(cls, db_obj: SQLAlchemySession) -> Optional[Self]:
        # Convert JSON history back to List[StepProgress]
        if db_obj is None:
            return None

        history = []
        if db_obj.history and "step_history" in db_obj.history:
            for step_data in db_obj.history["step_history"]:
                # Get the latest status from status_history
                status_history = step_data.get("status_history", [])
                latest_status = None
                latest_timestamp = None

                if status_history:
                    latest_entry = status_history[-1]
                    latest_status = latest_entry.get("status")
                    latest_timestamp = latest_entry.get("timestamp")

                # Create StepProgress object with proper enum conversion
                from gojiberri.models.enums import StepType, StepStatus

                step_type_str = step_data.get("step_type")
                status_str = latest_status if latest_status else "not_started"

                step_progress = StepProgress(
                    step=(
                        StepType(step_type_str) if step_type_str else StepType.RECORDING
                    ),
                    step_status=StepStatus(status_str),
                    start_time=(
                        datetime.fromisoformat(step_data.get("step_start_time"))
                        if step_data.get("step_start_time")
                        else None
                    ),
                    last_updated=(
                        datetime.fromisoformat(latest_timestamp)
                        if latest_timestamp
                        else None
                    ),
                )
                history.append(step_progress)

        return cls(
            session_id=db_obj.session_id,
            patient_name=db_obj.patient_name,
            patient_dob=date.fromisoformat(db_obj.patient_dob),
            session_start=db_obj.session_start,
            session_status=db_obj.session_status,
            workflow_type=db_obj.workflow_type,
            current_step=(
                StepProgress(
                    step=db_obj.current_step_type,
                    step_status=db_obj.current_status,
                )
                if db_obj.current_step_type
                else None
            ),
            history=history,
            last_updated=db_obj.updated_at,
        )

    def to_dict(self) -> Dict[str, Any]:
        return self.model_dump(mode="json")
