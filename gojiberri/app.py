# app.py
"""
FastAPI application entry point with database lifecycle management.
Configures database initialization on startup and graceful shutdown cleanup.
Registers API routers for sessions and workflow management endpoints.
"""

# app.py

from contextlib import asynccontextmanager
from fastapi import FastAPI

from gojiberri.api import sessions, workflow
from gojiberri.database.database_config import initialize_database


@asynccontextmanager
async def lifespan(app: FastAPI):
    try:
        await initialize_database()
        print("✅ Database initialized and ready")
        yield
    except Exception as e:
        print(f"❌ Application startup failed: {e}")
        raise
    finally:
        print("🔄 Gojiberri application shutting down...")
        print("✅ Shutdown complete")


app = FastAPI(lifespan=lifespan)
app.include_router(sessions.router, prefix="/sessions")
app.include_router(workflow.router, prefix="/workflow")
