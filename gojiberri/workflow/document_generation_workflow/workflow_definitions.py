from gojiberri.models.enums import StepStatus, StepType

STEP_BEHAVIOR = {
    StepType.RECORDING: {
        "requires_user_start": True,
        "requires_user_review": False,
        "background_task": None,
    },
    StepType.TRANSCRIPTION: {
        "requires_user_start": False,
        "requires_user_review": True,
        "background_task": "transcribe_audio",
    },
    StepType.DOCUMENT_UPLOAD_OCR: {
        "requires_user_start": True,
        "requires_user_review": True,
        "background_task": None,
    },
    StepType.DEIDENTIFICATION: {
        "requires_user_start": False,
        "requires_user_review": True,
        "background_task": "deidentify_text",
    },
    StepType.DOCUMENT_GENERATION: {
        "requires_user_start": False,
        "requires_user_review": False,
        "background_task": "generate_document",
    },
}

INITIAL_STEP_STATUS = {
    StepType.RECORDING: StepStatus.AWAITING_USER_INPUT,
    StepType.TRANSCRIPTION: StepStatus.NOT_STARTED,
    StepType.DOCUMENT_UPLOAD_OCR: StepStatus.NOT_STARTED,
    StepType.DEIDENTIFICATION: StepStatus.NOT_STARTED,
    StepType.DOCUMENT_GENERATION: StepStatus.NOT_STARTED,
}
