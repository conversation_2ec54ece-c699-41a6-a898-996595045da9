import asyncio
import os
import time
from pathlib import Path

from gojiberri.celery_worker import celery_app
from gojiberri.database.services.session_service import SessionService
from gojiberri.models.session.session import Session
from gojiberri.models.enums import StepType
from gojiberri.utils.logger import debug, warning
from gojiberri.workflow.document_generation_workflow.engine import advance_step


def ensure_documents_dir():
    if not os.path.exists("documents"):
        os.makedirs("documents")


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 3},
)
def run_transcription(self, session_id: str):
    """Synchronous Celery task that uses asyncio for database operations."""

    async def async_transcription_work():
        # All async operations happen inside this function
        session_service = SessionService()
        session = await session_service.get_session(session_id)

        if not session:
            warning(f"⚠️ Session {session_id} not found in database")
            return False

        debug(f"Transcribing session {session_id}...")

        # Transcription work
        ensure_documents_dir()
        time.sleep(20)

        filepath = f"documents/{session_id}_transcript.txt"
        with open(filepath, "w") as f:
            f.write("Sample transcription text")

        # Advance workflow step
        await advance_step(session, StepType.TRANSCRIPTION)
        debug(f"Finished transcribing session {session_id}...")

    # Run async work in sync context
    try:
        asyncio.run(async_transcription_work())
    except Exception:
        raise


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 3},
)
def run_ocr(self, session_id: str):
    # workflow_db[session_id] = WorkflowState.OCR_IN_PROGRESS
    print(f"OCR for session {session_id}...")
    Path(f"documents/session_{session_id}_ocr.txt").write_text("OCR content")
    # workflow_db[session_id] = WorkflowState.AWAITING_OCR_REVIEW


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 3},
)
def run_deid(self, session_id: str):
    # workflow_db[session_id] = WorkflowState.DEID_IN_PROGRESS
    print(f"De-identifying session {session_id}...")
    Path(f"documents/session_{session_id}_deid.txt").write_text("De-ID content")
    # workflow_db[session_id] = WorkflowState.AWAITING_DEID_REVIEW


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 3},
)
def run_generation(self, session_id: str):
    # workflow_db[session_id] = WorkflowState.GENERATION_IN_PROGRESS
    print(f"Generating document for session {session_id}...")
    # Simulate document generation
    Path(f"documents/session_{session_id}.txt").write_text("Final generated document")
    # workflow_db[session_id] = WorkflowState.AWAITING_GENERATION_REVIEW


CELERY_TASKS = {
    "transcribe_audio": run_transcription,
    "deidentify_text": run_deid,
    "generate_document": run_generation,
}


def _start_task(session: Session, task_name: str) -> None:
    task = CELERY_TASKS.get(task_name)
    if not task:
        raise ValueError(f"No such task: {task_name}")
    task.delay(str(session.session_id))
