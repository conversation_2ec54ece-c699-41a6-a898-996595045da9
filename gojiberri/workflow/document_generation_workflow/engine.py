from datetime import datetime, timezone
from gojiberri.models.session.session import (
    SessionStatus,
)
from gojiberri.models.enums import StepType, StepStatus

# from gojiberri.models.session.session_factory import add_step_progress_to_history
from gojiberri.database.services.session_service import SessionService
from gojiberri.workflow.base.workflow_definitions import WORKFLOW_TEMPLATES
from gojiberri.workflow.document_generation_workflow.workflow_definitions import (
    STEP_BEHAVIOR,
)
from gojiberri.models.session.session import Session
from gojiberri.utils.logger import debug, error


async def start_workflow(session: Session) -> None:
    """Start workflow asynchronously with proper database integration."""
    debug(f"START WORKFLOW: {session.session_id}")
    session.session_status = SessionStatus.IN_PROGRESS
    session.session_start = datetime.now(timezone.utc)
    await _move_to_next_step(session)


async def advance_step(session: Session, advancing_step: StepType) -> None:

    debug(
        f"🔍 advance_step called: session={session.session_id}, step={advancing_step.value}"
    )
    current_step_type, current_status = await SessionService().get_current_step(
        session.session_id
    )
    debug(f"🔍 Current step from JSON: {current_step_type}, status: {current_status}")
    if not current_step_type:
        raise ValueError("No active step to advance")

    if current_status == StepStatus.COMPLETED:
        return

    if current_step_type != advancing_step:
        raise ValueError("Step mismatch.")

    behavior = STEP_BEHAVIOR[current_step_type]
    new_status = None

    if current_status == StepStatus.AWAITING_USER_INPUT:
        if behavior["background_task"]:
            new_status = StepStatus.IN_PROGRESS
            from gojiberri.workflow.document_generation_workflow.tasks import (
                _start_task,
            )

            _start_task(session, behavior["background_task"])
        else:
            new_status = StepStatus.COMPLETED
    elif current_status == StepStatus.IN_PROGRESS:
        if behavior["requires_user_review"]:
            new_status = StepStatus.AWAITING_REVIEW
        else:
            new_status = StepStatus.COMPLETED
    elif current_status == StepStatus.AWAITING_REVIEW:
        new_status = StepStatus.COMPLETED
    else:
        raise RuntimeError(
            f"Unhandled state transition for step {current_step_type} in status {current_status}"
        )

    session_service = SessionService()

    await session_service.update_step_status_json(
        session.session_id, current_step_type, new_status
    )

    if new_status == StepStatus.COMPLETED:
        updated_session = await session_service.get_session(session.session_id)
        debug(f"🔍 Retrieved updated session, history: {updated_session.history}")
        await _move_to_next_step(updated_session)
    else:
        debug("🔍 Step not completed, staying on current step")


async def _start_new_step(session: Session, step_type: StepType) -> None:
    behavior = STEP_BEHAVIOR[step_type]
    initial_status = (
        StepStatus.AWAITING_USER_INPUT
        if behavior["requires_user_start"]
        else StepStatus.IN_PROGRESS
    )

    session_service = SessionService()
    debug(
        f"🔍 About to call add_step_to_workflow with initial_status: {initial_status.value} and step_type: {step_type.value}"
    )
    try:
        await session_service.add_step_to_workflow(
            session.session_id, step_type, initial_status
        )
        debug("🔍 add_step_to_workflow completed")
    except Exception as e:
        error(f"Error in add_step_to_workflow: {e}")

    if initial_status == StepStatus.IN_PROGRESS and behavior["background_task"]:
        from gojiberri.workflow.document_generation_workflow.tasks import _start_task

        _start_task(session, behavior["background_task"])


async def _move_to_next_step(session: Session) -> None:
    debug(f"🔍 _move_to_next_step called for session: {session.session_id}")
    debug(f"🔍 Session history: {session.history}")

    workflow_template = WORKFLOW_TEMPLATES.get(session.workflow_type.value)
    if not workflow_template:
        raise ValueError(f"Unknown workflow type: {session.workflow_type}")

    completed_steps = []
    # Use the history field which contains List[StepProgress]
    for step_progress in session.history:
        if step_progress.step_status == StepStatus.COMPLETED:
            completed_steps.append(step_progress.step.value)
    debug(f"🔍 Found completed steps: {completed_steps}")

    for step in workflow_template:
        if step.value not in completed_steps:
            debug(f"🔍 Starting new step: {step.value}")
            await _start_new_step(session, step)
            return

    debug("🔍 All steps completed, marking session as complete")

    session.current_step_type = None
    session.current_status = None
    session.session_status = SessionStatus.COMPLETED

    session_service = SessionService()
    await session_service.update_session(session.session_id, session)
