# Generic workflow runner
from gojiberri.models.session.session import Session
from gojiberri.models.enums import WorkflowType


async def start_workflow(session: Session = None):
    if session.workflow_type == WorkflowType.DOCUMENT_GENERATION.value:
        from gojiberri.workflow.document_generation_workflow.engine import (
            start_workflow,
        )

        await start_workflow(session=session)
    else:
        raise ValueError(f"Unknown workflow type: {session.workflow_type}")
