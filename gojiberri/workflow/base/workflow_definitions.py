from gojiberri.models.enums import StepType
from gojiberri.models.enums import WorkflowType


WORKFLOW_TEMPLATES = {
    WorkflowType.DOCUMENT_GENERATION.value: [
        StepType.RECORDING,
        StepType.TRANSCRIPTION,
        StepType.DOCUMENT_UPLOAD_OCR,
        StepType.DEIDENTIFICATION,
        StepType.DOCUMENT_GENERATION,
    ]
}

STEP_UI_CONFIG = {
    StepType.RECORDING: {"icon": "mic", "label": "Recording"},
    StepType.TRANSCRIPTION: {"icon": "notes", "label": "Transcription"},
    StepType.DOCUMENT_UPLOAD_OCR: {"icon": "arrow_upward", "label": "Document Upload"},
    StepType.DEIDENTIFICATION: {"icon": "shield", "label": "De-identification"},
    StepType.DOCUMENT_GENERATION: {"icon": "note_add", "label": "Document Generation"},
}
