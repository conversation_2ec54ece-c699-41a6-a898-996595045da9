"""
Workflow Business Logic Service

Handles workflow-related business logic including step status determination,
workflow progression, and step configuration management.
"""

from gojiberri.models.enums import StepStatus, StepType
from gojiberri.workflow.document_generation_workflow.workflow_definitions import (
    INITIAL_STEP_STATUS,
)


class StepService:
    """Service for workflow business logic operations"""

    @staticmethod
    def determine_initial_step_status(
        step_type: StepType, step_index: int
    ) -> StepStatus:
        """
        Determine appropriate initial status for a workflow step.
        Args:
            step_type (StepType): The type of the step.
            step_index (int): The index of the step in the workflow.
        """
        if step_index == 0:
            return INITIAL_STEP_STATUS.get(step_type, StepStatus.NOT_STARTED)
        return StepStatus.NOT_STARTED
