import os
from celery import Celery

# Celery App Initialization

redis_url = os.getenv(
    "REDIS_URL", "redis://redis:6379/0"
)  # default for Docker. Allows us to debug

celery_app = Celery("gojiberri", broker=redis_url, backend=redis_url)

celery_app.config_from_object("gojiberri.celeryconfig")

# Auto-discover tasks from workflow modules
celery_app.autodiscover_tasks(["gojiberri.workflow.document_generation_workflow"])
