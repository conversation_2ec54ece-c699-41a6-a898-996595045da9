# gojiberri/api/sessions.py
"""
API endpoints for start/resume/approve workflow + get status/list of sessions + workflow steps + results
| API File      | Endpoint                                          | Purpose                            |
| ------------- | --------------------------------------------------| ---------------------------------- |
| `sessions.py` | `POST /sessions`                                  | Create new session                 |
|               | `POST /sessions/{id}/start`                       | Start a new workflow               |
|               | `POST /sessions/{id}/approve`                     | Approve current stage and continue |
|               | `GET /sessions/{id}/status`                       | Get current workflow state         |
|               | `GET /sessions`                                   | List sessions                      |
|               | `GET /sessions/{id}`                              | Get full session info              |
|               | `GET /sessions/{id}/step/{step_type}/progress`    | Get step progress                  |
|               | `GET /sessions/{id}/step/{step_type}/results`     | Get step results                   |
"""

from datetime import datetime
from fastapi import APIRouter, HTTPException, UploadFile, File, Form

from gojiberri.database.services.recording_service import RecordingService
from gojiberri.database.services.session_service import SessionService
from gojiberri.mock.progress_service import (
    _generate_step_results,
    calculate_progress_with_results,
)
from gojiberri.models.recording.recording_io import CompleteRecordingResponse
from gojiberri.models.enums import StepType, WorkflowType
from gojiberri.models.session.session_io import (
    CreateSessionResponse,
    CreateSessionRequest,
    GetSessionResponse,
    GetSessionStatusResponse,
    StepResultResponse,
)
from gojiberri.models.steps.step_io import StepProgressResponse
from gojiberri.utils.logger import error
from gojiberri.workflow.base.engine import start_workflow
from gojiberri.workflow.document_generation_workflow.engine import advance_step

router = APIRouter()


@router.get("", response_model=list[GetSessionResponse])
async def list_sessions(limit: int = 50, offset: int = 0):
    """List all sessions with pagination."""
    try:
        session_service = SessionService()
        pydantic_sessions = await session_service.list_sessions(
            limit=limit, offset=offset
        )

        # Convert to response format
        sessions = [
            GetSessionResponse(**session.to_dict()) for session in pydantic_sessions
        ]

        return sessions

    except Exception as e:
        error(f"🔴 Error listing sessions: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to list sessions: {str(e)}"
        )


@router.get("/{session_id}", response_model=GetSessionResponse)
async def get_session(session_id: str):
    """Get session details by ID."""
    try:
        session_service = SessionService()
        session_data = await session_service.get_session(session_id)

        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found")

        return session_data

    except HTTPException:
        raise
    except Exception as e:
        error(f"🔴 Error getting session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get session: {str(e)}")


@router.get("/{session_id}/status", response_model=GetSessionStatusResponse)
async def get_session_status(session_id: str):
    """Get session status and current workflow step."""
    try:
        session_service = SessionService()
        session_data = await session_service.get_session(session_id)

        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found")

        return GetSessionStatusResponse(
            session_id=session_data.session_id,
            status=session_data.session_status,
            current_step=session_data.current_step,
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"🔴 Error Getting Session Status: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get session status: {str(e)}"
        )


@router.post("", response_model=CreateSessionResponse)
async def create_session(request: CreateSessionRequest):
    """Create a new recording session."""
    try:
        session_service = SessionService()

        session_data = {
            "patient_name": request.patient_name,
            "patient_dob": str(request.patient_dob),
            "workflow_type": request.workflow_type
            or WorkflowType.DOCUMENT_GENERATION.value,
        }

        new_session = await session_service.create_session(session_data)

        await start_workflow(new_session)

        return CreateSessionResponse(session_id=new_session.session_id)

    except ValueError as e:
        error(f"🔴 Error creating sessions: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        error(f"🔴 Error creating sessions: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create session: {str(e)}"
        )


@router.post("/{session_id}/recording", response_model=CompleteRecordingResponse)
async def complete_recording(
    session_id: str,
    file: UploadFile = File(...),
    duration_milliseconds: float = Form(...),
    end_time: datetime = Form(...),
):
    """Complete a recording for a session."""
    try:
        # Verify session exists
        session_service = SessionService()
        session = await session_service.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Create recording
        recording_service = RecordingService()

        recording_data = {
            "session_id": session_id,
            "file_path": f"recordings/{session_id}_{file.filename}",
            "duration_milliseconds": duration_milliseconds,
            "end_time": end_time,
        }

        # TODO: Save uploaded file to storage will be handled in the RecordingService
        await recording_service.complete_recording(session_id, recording_data)

        await advance_step(session, StepType.RECORDING)

        return CompleteRecordingResponse(success=True)

    except HTTPException:
        raise
    except ValueError as e:
        error(f"🔴 Error completing Recording - B: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        error(f"🔴 Error completing Recording - B: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to complete recording: {str(e)}"
        )


@router.get(
    "/{session_id}/step/{step_type}/progress", response_model=StepProgressResponse
)
async def get_step_progress(session_id: str, step_type: str):
    """Get progress for any workflow step."""

    # TODO: PERFORMANCE ISSUE - Database Polling Bottleneck
    # Frontend polls this endpoint every 2 seconds during long transcriptions (4-10+ hours),
    # causing 7K-18K unnecessary database hits per session. Celery tasks can't share progress
    # state across processes, forcing database access for every progress check.

    try:
        step_enum = StepType(step_type)
        progress_data = calculate_progress_with_results(session_id, step_enum)

        return StepProgressResponse(
            session_id=session_id,
            step_type=step_enum,
            percentage=progress_data["percentage"],
            status=progress_data["status"],
        )

    except ValueError as e:
        error(f"🔴 Error getting step progress: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid step type: {step_type}")
    except Exception as e:
        error(f"🔴 Error getting step progress: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get step progress: {str(e)}"
        )


@router.get("/{session_id}/step/{step_type}/results", response_model=StepResultResponse)
async def get_step_results(session_id: str, step_type: str):
    """Get results for any workflow step."""
    try:
        # Verify session exists
        session_service = SessionService()
        session = await session_service.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        try:
            step_enum = StepType(step_type)
        except ValueError:
            raise HTTPException(
                status_code=400, detail=f"Invalid step type: {step_type}"
            )

        # TODO: Get results using mock service (replace with real implementation later)
        result_data = _generate_step_results(session_id, step_type)

        return StepResultResponse(
            session_id=session_id,
            step_type=step_enum,
            available=True,
            result_data=result_data,
        )

    except ValueError as e:
        error(f"🔴 Error getting step results: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid step type: {step_type}")
    except Exception as e:
        error(f"🔴 Error getting step results: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get step results: {str(e)}"
        )
