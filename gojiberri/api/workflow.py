"""
Workflow API endpoints for managing workflow step configurations.

Provides workflow steps with UI metadata (icons, labels, status) for frontend rendering.
Main endpoint: GET /workflow/steps - Returns workflow step configuration for UI display.


| API File      | Endpoint                     | Purpose                            |
| ------------- | -----------------------------| ---------------------------------- |
| `workflow.py` | `GET /workflow/steps`        | Get workflow steps configuration   |


"""

from fastapi import APIRouter, HTTPException

from gojiberri.models.steps.step_io import StepResponse, WorkflowStepsResponse
from gojiberri.services.steps.step_service import StepService
from gojiberri.workflow.base.workflow_definitions import (
    STEP_UI_CONFIG,
    WORKFLOW_TEMPLATES,
)
from gojiberri.models.enums import WorkflowType

router = APIRouter()


@router.get("/steps", response_model=WorkflowStepsResponse)
def get_workflow_steps(
    workflow_type: WorkflowType = WorkflowType.DOCUMENT_GENERATION.value,
):
    """
    Get workflow steps with UI configuration and initial status.

    Returns list of workflow steps with step_name, icon, label, and status
    for frontend workflow display components.
    """
    workflow_steps = WORKFLOW_TEMPLATES.get(workflow_type)
    if not workflow_steps:
        raise HTTPException(status_code=404, detail="unknown workflow type")

    api_steps = []
    for step_index, step_type in enumerate(workflow_steps):
        config = STEP_UI_CONFIG[step_type]
        initial_status = StepService.determine_initial_step_status(
            step_type=step_type, step_index=step_index
        )

        step = StepResponse(
            step_name=step_type.value,
            icon=config["icon"],
            label=config["label"],
            status=initial_status.value,
        )
        api_steps.append(step)

    return WorkflowStepsResponse(steps=api_steps)
