# database/models/base.py
"""
Database base model providing common fields and functionality.

This module defines the base class for all database models in the Gojiberri
neurology tool application. It provides standardized ID generation, timestamps,
and basic model operations used across all entities.

Key Features:
- UUID-based primary keys for distributed system compatibility
- Automatic timestamp management for created_at and updated_at
- Soft delete capability with is_active flag
- Basic dictionary conversion for API responses

Dependencies:
- SQLAlchemy: ORM framework for database operations
- UUID: Standard library for unique identifier generation
- datetime: Standard library for timestamp management
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict
from sqlalchemy import String, DateTime, Boolean
from sqlalchemy.orm import Mapped, mapped_column, DeclarativeBase


class Base(DeclarativeBase):
    """
    SQLAlchemy declarative base class for all database models.

    This serves as the foundation for all ORM models in the application,
    providing the declarative mapping configuration required by SQLAlchemy.
    """

    pass


class BaseModel(Base):
    """
    Base model providing common fields and functionality for all database tables.

    This abstract base class implements standard patterns used across all
    database entities in the Gojiberri application:

    - UUID primary keys for scalable, distributed-friendly identification
    - Automatic timestamp tracking for audit and synchronization
    - Soft delete capability for data preservation
    - Dictionary conversion for clean API responses

    All concrete models should inherit from this class to ensure
    consistent data management patterns across the application.

    Database Columns:
        id: UUID primary key, automatically generated
        created_at: Timestamp when record was created, auto-set
        updated_at: Timestamp when record was last modified, auto-updated
        is_active: Boolean flag for soft delete functionality
    """

    __abstract__ = True

    id: Mapped[str] = mapped_column(
        String(36),
        primary_key=True,
        default=lambda: str(uuid.uuid4()),
        doc="UUID primary key for distributed system compatibility",
    )

    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        doc="UTC timestamp when record was created",
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        doc="UTC timestamp when record was last updated",
    )

    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        index=True,
        doc="Soft delete flag - False indicates deleted records",
    )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert model instance to dictionary for API responses.

        Transforms all column values into a flat dictionary structure
        suitable for JSON serialization and API responses. Handles
        datetime objects by converting to ISO format strings.

        Returns:
            Dict[str, Any]: Dictionary representation with column names as keys

        Example:
            {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "created_at": "2024-01-15T10:30:00.123456",
                "updated_at": "2024-01-15T15:45:30.789012",
                "is_active": True
            }
        """
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            else:
                result[column.name] = value
        return result

    def __repr__(self) -> str:
        """
        Provide string representation for debugging and logging.

        Returns:
            str: Class name with ID for easy identification in logs

        Example:
            "<Session(id=123e4567-e89b-12d3-a456-426614174000)>"
        """
        return f"<{self.__class__.__name__}(id={self.id})>"
