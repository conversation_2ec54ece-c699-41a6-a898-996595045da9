# database/models/recording_model.py
"""
Recording model for audio recording storage and session association.

This module defines the Recording model used to store audio recording
information and link recordings to their parent sessions. It provides
essential recording metadata while maintaining a focused interface
that supports the current API requirements.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import String, Float, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column

from .base import BaseModel


class Recording(BaseModel):
    """
    Core recording model representing audio captures within patient sessions.

    This model stores essential information about audio recordings including
    file location, timing, and status. It maintains a clean one-to-one
    relationship with sessions through bidirectional foreign key references.

    Database Schema:
        recording_id: External identifier used by API (separate from internal id)
        session_id: Foreign key linking to parent session (one-to-one)
        file_path: Location of audio file for retrieval and processing
        duration: Recording length in seconds for workflow planning
        end_time: When recording ended (UTC, optional)
        recording_status: Current processing state for workflow tracking

    Relationships:
        session: One-to-one relationship with Session entity

    Usage Examples:
        # Create new recording
        recording = Recording(
            session_id="session-123",
            file_path="recordings/recording_session-123.webm",
            duration=245.7,
        )


    """

    __tablename__ = "recordings"

    # Core recording identification
    recording_id: Mapped[str] = mapped_column(
        String(36),
        unique=True,
        index=True,
        default=lambda: str(__import__("uuid").uuid4()),
        doc="External recording identifier used by API clients",
    )

    session_id: Mapped[str] = mapped_column(
        String(36),
        ForeignKey("sessions.session_id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        unique=True,  # One-to-one: each session has exactly one recording
        doc="Foreign key linking recording to parent session (one-to-one relationship)",
    )

    # File location and audio metadata
    file_path: Mapped[Optional[str]] = mapped_column(
        String(512),
        nullable=True,
        doc="Storage path for audio file retrieval and processing",
    )

    duration: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=False,
        doc="Recording length in milliseconds for workflow timing calculations",
    )

    end_time: Mapped[Optional[datetime]] = mapped_column(
        nullable=True,
        doc="UTC timestamp when recording ended (optional for workflow tracking)",
    )

    

    def __repr__(self) -> str:
        """
        Provide detailed string representation for debugging and logging.
        """
        return f"<Recording(recording_id={self.recording_id}, session_id={self.session_id})>"
