# database/models/session_model.py
"""
Session model for neurology recording sessions with step history tracking.
Supports complete workflow progression tracking and step switching functionality.
"""

from datetime import datetime, timezone
from typing import Optional
from sqlalchemy import String, DateTime, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy.ext.mutable import MutableDict

from gojiberri.models.enums import StepType, StepStatus, SessionStatus, WorkflowType

from .base import BaseModel


class Session(BaseModel):
    __tablename__ = "sessions"

    session_id: Mapped[str] = mapped_column(String(36), unique=True, index=True)
    patient_name: Mapped[str] = mapped_column(String(255), nullable=False)
    patient_dob: Mapped[str] = mapped_column(String(10), nullable=False)
    session_status: Mapped[SessionStatus] = mapped_column(
        SQLEnum(SessionStatus), default=SessionStatus.IN_PROGRESS, index=True
    )
    current_step_type: Mapped[Optional[StepType]] = mapped_column(
        SQLEnum(StepType), nullable=True, index=True
    )
    current_status: Mapped[Optional[StepStatus]] = mapped_column(
        SQLEnum(StepStatus), nullable=True, index=True
    )
    history: Mapped[Optional[dict]] = mapped_column(
        MutableDict.as_mutable(JSON), nullable=True, name="step_history"
    )

    workflow_type: Mapped[Optional[WorkflowType]] = mapped_column(
        String(50), default=WorkflowType.DOCUMENT_GENERATION.value
    )
    session_start: Mapped[Optional[datetime]] = mapped_column(
        DateTime, default=lambda: datetime.now(timezone.utc)
    )
    session_end: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
