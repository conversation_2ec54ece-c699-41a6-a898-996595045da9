# database/services/recording_service.py
"""
Enhanced recording service with Pydantic-first validation architecture.
Uses Pydantic model validation for comprehensive field validation and data integrity.
Provides detailed error messages for missing or invalid data.
"""

from typing import List, Optional, Dict, Any
from uuid import uuid4
from pydantic import ValidationError

from gojiberri.database.database_config import DatabaseConfig, get_database_config
from gojiberri.database.repositories.recording_repository import RecordingRepository
from gojiberri.database.services.session_service import SessionService
from gojiberri.models.recording.recording import Recording as PydanticRecording


class RecordingService:
    """
    Service layer for recording management with Pydantic-first validation.

    Uses Pydantic model validation for comprehensive field validation and data integrity.
    Validates all required fields and foreign key relationships according to
    database schema constraints before attempting database operations.
    """

    def __init__(self, database_config: DatabaseConfig = None):
        """
        Initialize service with database configuration.
        Uses shared global instance by default for optimal performance. Inject custom config for testing.
        """
        self._db_config = database_config or get_database_config()

    async def create_recording(self, recording_data: Dict[str, Any]):
        """
        Create new recording with Pydantic validation.

        Args:
            recording_data: Dictionary containing recording creation data
                Required fields:
                    - session_id: str - Parent session identifier (must exist)
                Optional fields:
                    - file_path: str - Audio file storage location (max 512 chars)
                    - duration_milliseconds: float - Recording length in milliseconds (> 0)
                    - end_time: datetime - When recording ended

        Raises:
            ValueError: If Pydantic validation fails or session doesn't exist
            DatabaseError: If recording creation fails due to database constraints
        """

        # Verify session exists before creating Pydantic model
        session_id = recording_data.get("session_id")
        if session_id:
            session_service = SessionService(self._db_config)
            await session_service.verify_session_exists(session_id)

        try:
            # Use Pydantic validation instead of manual validation
            pydantic_recording = PydanticRecording(**recording_data)
        except ValidationError as e:
            raise ValueError(f"Recording validation failed: {e.errors()}")

        # Convert to SQLAlchemy and save
        sqlalchemy_obj = pydantic_recording.to_sqlalchemy()

        async with self._db_config.get_session() as db_session:
            recording_repo = RecordingRepository(db_session)
            await recording_repo.create_recording(sqlalchemy_obj)

    async def get_recording(self, recording_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve recording by ID with ID validation.

        Args:
            recording_id: External recording identifier (must be valid UUID format)

        Returns:
            Optional[Dict[str, Any]]: API-compatible recording data or None if not found

        Raises:
            ValueError: If recording_id format is invalid
        """

        async with self._db_config.get_session() as db_session:
            recording_repo = RecordingRepository(db_session)

            recording = await recording_repo.get_recording_by_id(recording_id)
            if not recording:
                return None

            return PydanticRecording.from_sqlalchemy(recording)

    async def get_recordings_by_session(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Retrieve all recordings for a specific session with session validation.

        Args:
            session_id: Session identifier (must be valid UUID format)

        Returns:
            List[Dict[str, Any]]: List of API-compatible recording objects

        Raises:
            ValueError: If session_id format is invalid
        """
        if not self._validate_session_id(session_id):
            raise ValueError("Invalid session ID format")

        async with self._db_config.get_session() as db_session:
            recording_repo = RecordingRepository(db_session)

            recordings = await recording_repo.get_recordings_by_session(session_id)
            return [
                PydanticRecording.from_sqlalchemy(recording) for recording in recordings
            ]

    async def complete_recording(
        self,
        session_id: str,
        recording_data: Dict[str, Any],
        audio_data: Optional[bytes] = None,
    ):
        """
        Complete a recording for a session with Pydantic validation.

        Args:
            session_id: Session identifier for recording association
            recording_data: Recording metadata and file information
            audio_data: Optional binary audio data (currently unused)

        Returns:
            Dict[str, Any]: Completion status and recording information

        Raises:
            ValueError: If Pydantic validation fails or session doesn't exist
        """
        # Set session_id and recording_id if not provided
        recording_data["session_id"] = session_id
        if "recording_id" not in recording_data:
            recording_data["recording_id"] = str(uuid4())

        # Use the create_recording method which now has Pydantic validation
        await self.create_recording(recording_data)
