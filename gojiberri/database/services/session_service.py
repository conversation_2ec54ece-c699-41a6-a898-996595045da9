# database/services/session_service.py
"""
Enhanced session service with intelligent step history management.
Automatically handles step history creation and updates in a single transaction.
"""

from typing import Optional, Dict, Any
from datetime import datetime, timezone
from uuid import uuid4
from pydantic import ValidationError
from sqlalchemy.orm.attributes import flag_modified

from gojiberri.database.database_config import DatabaseConfig, get_database_config
from gojiberri.database.repositories.session_repository import SessionRepository
from gojiberri.models.session.session import Session
from gojiberri.utils.logger import debug
from gojiberri.models.enums import StepType, StepStatus


class SessionService:
    """Enhanced session service with Pydantic-first architecture."""

    def __init__(self, database_config: DatabaseConfig = None):
        """
        Initialize service with database configuration.
        Uses shared global instance by default for optimal performance. Inject custom config for testing.
        """
        self._db_config = database_config or get_database_config()

    async def create_session(self, session_data: Dict[str, Any]) -> Session:
        """Create new recording session with Pydantic validation."""
        session_data["session_id"] = str(uuid4())

        try:
            pydantic_session = Session(**session_data)
        except ValidationError as e:
            raise ValueError(f"Validation failed: {e.errors()}")

        sqlalchemy_obj = pydantic_session.to_sqlalchemy()

        async with self._db_config.get_session() as db_session:
            session_repo = SessionRepository(db_session)
            await session_repo.create_session(sqlalchemy_obj)
            return pydantic_session

    async def get_session(self, session_id: str) -> Optional[Session]:
        """Retrieve session by ID."""
        async with self._db_config.get_session() as db_session:
            session_repo = SessionRepository(db_session)
            db_session_obj = await session_repo.get_session_by_id(session_id)
            if not db_session_obj:
                return None
            return Session.from_sqlalchemy(db_session_obj)

    async def list_sessions(self, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """List sessions with pagination."""
        async with self._db_config.get_session() as db_session:
            session_repo = SessionRepository(db_session)
            db_sessions = await session_repo.list_sessions(limit=limit + offset)

            paginated_sessions = (
                db_sessions[offset : offset + limit]
                if offset > 0
                else db_sessions[:limit]
            )

            return [Session.from_sqlalchemy(s) for s in paginated_sessions]

    async def update_session(
        self,
        session_id: str,
        session_updates: Dict[str, Any],
    ) -> Optional[Session]:
        """
        Update session with Pydantic-based workflow management.
        """
        async with self._db_config.get_session() as db_session:
            session_repo = SessionRepository(db_session)
            db_session_obj = await session_repo.get_session_by_id(session_id)
            if not db_session_obj:
                return None

            pydantic_session = Session.from_sqlalchemy(db_session_obj)
            updated_session = pydantic_session.model_copy(
                update=session_updates, deep=True
            )

            db_session_obj = await session_repo.update_session(
                updated_session.to_sqlalchemy()
            )
            return Session.from_sqlalchemy(db_session_obj)

    async def add_step_to_workflow(
        self, session_id: str, step_type: StepType, initial_status: StepStatus
    ):
        async with self._db_config.get_session() as db_session:
            session_repo = SessionRepository(db_session)

            # 1. Get SQLAlchemy object (HAS id)
            db_session_obj = await session_repo.get_session_by_id(session_id)
            if not db_session_obj:
                raise ValueError("Session not found")

            # 2. Work directly with SQLAlchemy object (preserves id)
            now = datetime.now(timezone.utc)
            new_step = self._build_step_json(step_type, now, initial_status)

            # Initialize history if None
            if db_session_obj.history is None:
                db_session_obj.history = {"step_history": []}

            # Modify SQLAlchemy object directly
            db_session_obj.history["step_history"].append(new_step)

            # Update denormalized fields
            db_session_obj.current_step_type = step_type
            db_session_obj.current_status = initial_status
            db_session_obj.updated_at = now

            flag_modified(db_session_obj, "history")

            # 3. Merge with original object (preserves id)
            await session_repo.update_session(db_session_obj)

    async def update_step_status_json(
        self, session_id: str, step_type: StepType, new_status: StepStatus
    ) -> None:
        """Update step status in workflow JSON using direct SQLAlchemy manipulation."""
        debug(
            f"🔍 update_step_status_json called: session={session_id}, step={step_type.value}, status={new_status.value}"
        )

        async with self._db_config.get_session() as db_session:
            session_repo = SessionRepository(db_session)
            db_session_obj = await session_repo.get_session_by_id(session_id)
            if not db_session_obj:
                raise ValueError("Session not found")

            debug(f"🔍 Retrieved session object with id: {db_session_obj.id}")
            debug(f"🔍 Current history: {db_session_obj.history}")

            # Initialize history if None
            if db_session_obj.history is None:
                db_session_obj.history = {"step_history": []}

            now = datetime.now(timezone.utc)

            # Find and update step directly in SQLAlchemy object
            step_found = False
            for i, step in enumerate(db_session_obj.history["step_history"]):
                if step["step_type"] == step_type.value:
                    debug(f"🔍 Found step at index {i}")
                    self._add_status_to_step(step, new_status, now)
                    if new_status == StepStatus.COMPLETED:
                        step["step_end_time"] = now.isoformat()
                    step_found = True
                    break

            if not step_found:
                debug("🔍 Step not found, creating new step")
                new_step = self._build_step_json(step_type, now, new_status)
                db_session_obj.history["step_history"].append(new_step)

            # Update denormalized fields directly on SQLAlchemy object
            db_session_obj.current_step_type = step_type
            db_session_obj.current_status = new_status
            db_session_obj.updated_at = now

            flag_modified(db_session_obj, "history")

            debug(f"🔍 About to save session with id: {db_session_obj.id}")

            # Update with original SQLAlchemy object (preserves primary key)
            await session_repo.update_session(db_session_obj)
            debug("🔍 Session saved successfully")

    async def get_current_step(self, session_id: str) -> tuple:
        """Get current step and status from JSON (for UI polling)."""
        async with self._db_config.get_session() as db_session:
            session_repo = SessionRepository(db_session)
            db_session_obj = await session_repo.get_session_by_id(session_id)
            if not db_session_obj:
                raise ValueError("Session not found")

            pydantic_session = Session.from_sqlalchemy(db_session_obj)
            return (
                pydantic_session.current_step.step,
                pydantic_session.current_step.step_status,
            )

    def _build_step_json(
        self, step_type: StepType, start_time: datetime, status: StepStatus
    ) -> dict:
        """Build JSON structure for a single step."""
        return {
            "step_type": step_type.value,
            "step_start_time": start_time.isoformat(),
            "step_end_time": None,
            "status_history": [
                {"status": status.value, "timestamp": start_time.isoformat()}
            ],
        }

    def _add_status_to_step(
        self, step_json: dict, status: StepStatus, timestamp: datetime
    ) -> dict:
        """Add status change to step's status_history."""
        step_json["status_history"].append(
            {"status": status.value, "timestamp": timestamp.isoformat()}
        )
        return step_json

    async def verify_session_exists(self, session_id: str) -> None:
        """
        Verify that the referenced session exists in the database.

        Args:
            session_id: Session ID to verify

        Raises:
            ValueError: If session doesn't exist
        """
        async with self._db_config.get_session() as db_session:
            session_repo = SessionRepository(db_session)
            session = await session_repo.get_session_by_id(session_id)
            if not session:
                raise ValueError(f"Session not found: {session_id}")
