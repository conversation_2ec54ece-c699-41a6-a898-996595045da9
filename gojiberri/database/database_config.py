"""
Database configuration management for the Gojiberri neurology tool.

This module provides centralized database configuration supporting both
development (SQLite) and production (PostgreSQL) environments. It implements
async database operations with proper connection pooling and transaction
management for optimal performance.

Key Features:
- Lazy initialization: Database connections created only when needed
- Singleton pattern: Single shared connection pool for optimal performance
- Environment-based database selection (SQLite for dev, PostgreSQL for prod)
- Async database operations with SQLAlchemy async engine
- Automatic connection pooling and session management
- Transaction-safe operations with proper error handling
- Context manager pattern for clean resource management

Performance Benefits:
- Fast application startup (no database connection during import)
- Resource efficient (connections created only when actually needed)
- Single shared connection pool across all services
- Optimal memory usage and connection management

Testing Support:
- Easy dependency injection for testing
- No database requirement for importing modules
- Clean separation of concerns
"""

import asyncio
import os
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional
from sqlalchemy import text

from gojiberri.config.config_loader import config
from gojiberri.utils.logger import debug, log_exception


class DatabaseConfig:
    """
    Centralized database configuration with lazy initialization and environment-based selection.

    This class manages database connection configuration for both development
    and production environments. It uses lazy initialization to create database
    connections only when actually needed, improving startup performance and
    reliability.

    The configuration implements proper connection pooling, transaction
    management, and error handling patterns required for production
    database operations.

    Attributes:
        database_url: Connection string for the configured database
        engine: SQLAlchemy async engine for database operations (created lazily)
        async_session_factory: Factory for creating database sessions (created lazily)

    Environment Variables:
        DATABASE_URL: Full database connection string for production
                     If not set, defaults to SQLite for development

    Usage Example:
        # Get configuration instance (no database connection yet)
        db_config = get_database_config()

        # Database connection created here, on first use
        async with db_config.get_session() as session:
            result = await session.execute(select(Session))
    """

    def __init__(self):
        """
        Initialize database configuration without creating database connections.

        Database engine and connection pool are created lazily on first use,
        improving application startup time and reliability.
        """
        self.database_url = None
        self.engine = None
        self.async_session_factory = None
        self._initialized = False
        self._init_lock: Optional[asyncio.Lock] = None

    async def _ensure_initialized(self):
        """
        Initialize database engine and connection pool on first use.
        Thread-safe initialization using asyncio.Lock to prevent race conditions.
        """
        #  Sets up database connection only when first needed, not at startup
        if self._initialized:
            return

        # Create lock if it doesn't exist
        if self._init_lock is None:
            self._init_lock = asyncio.Lock()

        # Use async lock to ensure only one coroutine initializes at a time
        async with self._init_lock:
            # Double-check pattern: another coroutine might have initialized while we waited
            if self._initialized:
                return

            # Retrieve environment-specific database connection string
            # Handles automatic detection of dev/staging/prod environments
            self.database_url = self._get_database_url()

            # Load database configuration from external config file
            # Allows environment-specific tuning without code changes
            db_config_data = config.get_section("database")

            # Create async database engine with production-optimized settings
            self.engine = create_async_engine(
                self.database_url,
                # Enable SQL logging for debugging (disabled in production for performance)
                echo=db_config_data.get("echo", False),
                # Test connection health before each use to prevent stale connection errors
                pool_pre_ping=db_config_data.get("pool_pre_ping", True),
                # Recycle connections after 1 hour to prevent database timeout issues
                pool_recycle=db_config_data.get("pool_recycle", 3600),
                # Allow 20 extra connections during traffic spikes beyond base pool size
                max_overflow=db_config_data.get("max_overflow", 20),
            )

            # Create session factory for generating database sessions
            # expire_on_commit=False keeps objects accessible after commit without re-querying
            # Improves performance for immediate post-commit data access
            self.async_session_factory = async_sessionmaker(
                self.engine, class_=AsyncSession, expire_on_commit=False
            )

            self._initialized = True

    def _get_database_url(self) -> str:
        """
        Determine database URL based on environment configuration.

        Checks for DATABASE_URL environment variable to determine if
        running in production. If not found, defaults to SQLite for
        development with async support.

        Returns:
            str: Database connection URL for SQLAlchemy engine

        Database Selection Logic:
            - Production: Uses DATABASE_URL environment variable
            - Development: Uses SQLite with aiosqlite async driver

        Example URLs:
            # Production PostgreSQL
            postgresql+asyncpg://user:pass@host:port/dbname

            # Development SQLite
            sqlite+aiosqlite:///./gojiberri_app.db
        """
        db_url = os.getenv("DATABASE_URL")

        if db_url:
            # Production environment with explicit database URL
            # Ensure async driver is used for PostgreSQL
            if db_url.startswith("postgresql://"):
                db_url = db_url.replace("postgresql://", "postgresql+asyncpg://", 1)
            return db_url
        else:
            # Development environment - get URL from config
            db_config_data = config.get_section("database")
            return db_config_data.get(
                "dev_database_url", "sqlite+aiosqlite:///./gojiberri_app.db"
            )

    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Provide database session with automatic transaction management and lazy initialization.

        Creates an async database session with proper transaction handling,
        including automatic commit on success and rollback on error.
        The session is automatically closed after use to prevent
        connection leaks.

        Database connections are created lazily on first call to this method,
        ensuring optimal startup performance and resource usage.

        This context manager ensures proper resource cleanup and
        transaction integrity for all database operations.

        Yields:
            AsyncSession: Active database session for operations

        Transaction Behavior:
            - Automatically commits successful operations
            - Automatically rolls back on exceptions
            - Always closes session to prevent connection leaks

        Example:
            async with db_config.get_session() as session:
                # Database operations here
                result = await session.execute(select(User))
                # Automatic commit on success

        Error Handling:
            # Exception causes automatic rollback
            try:
                async with db_config.get_session() as session:
                    # Operations that might fail
                    pass
            except Exception as e:
                # Session already rolled back automatically
                handle_error(e)

        Raises:
            Exception: If database connection fails during initialization
                      or if database operations encounter errors
        """
        # Initialize database engine only when first session is requested
        await self._ensure_initialized()

        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()


# Module-level singleton management
# This approach provides optimal performance with lazy initialization
_db_config_instance = None


def get_database_config() -> DatabaseConfig:
    """
    Get database configuration singleton with lazy initialization.

    Returns the same DatabaseConfig instance across all calls, ensuring
    a single shared connection pool for optimal performance. The instance
    is created only when first requested, not during module import.

    This factory function provides:
    - Singleton pattern for optimal resource usage
    - Lazy initialization for fast application startup
    - Thread-safe instance creation
    - Easy testing through dependency injection

    Returns:
        DatabaseConfig: Shared database configuration instance

    Usage Examples:
        # Production usage (creates shared instance)
        db_config = get_database_config()

        # Service initialization
        session_service = SessionService()  # Uses shared config

        # Testing usage (inject custom config)
        test_config = DatabaseConfig()
        session_service = SessionService(database_config=test_config)

    Performance Notes:
        - First call creates the DatabaseConfig instance
        - Subsequent calls return the same instance (no overhead)
        - Database connections created only when get_session() is called
        - Single connection pool shared across all services
    """
    global _db_config_instance
    if _db_config_instance is None:
        _db_config_instance = DatabaseConfig()
    return _db_config_instance


async def initialize_database() -> None:
    """
    Initialize database tables and perform startup configuration.

    Creates all database tables defined in SQLAlchemy models and performs
    any necessary startup configuration. This function is idempotent and
    safe to run multiple times - existing tables are not affected.

    Uses the shared database configuration to ensure consistency with
    the rest of the application.

    The initialization process includes:
    - Creating all database tables from model definitions
    - Verifying database connectivity
    - Logging initialization status for monitoring

    Raises:
        Exception: If database connection fails or table creation encounters errors

    Dependencies:
        - Requires all SQLAlchemy model imports to be available
        - Needs database connection to be properly configured

    Usage:
        # Called automatically on app startup
        await initialize_database()

    Note:
        This function uses SQLAlchemy's metadata.create_all() which only
        creates tables that don't already exist. Existing tables and data
        are preserved during initialization.
    """
    try:
        # Import all models to ensure they're registered with SQLAlchemy metadata
        from gojiberri.database.models.base import Base

        # Get shared database configuration
        db_config = get_database_config()

        # Ensure database engine is initialized
        await db_config._ensure_initialized()

        # Create all tables defined in the models
        async with db_config.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        debug("✅ Database tables created successfully")

        # Verify database connectivity by performing a simple query
        async with db_config.get_session() as session:
            # Test connection with a simple query
            result = await session.execute(text("SELECT 1"))
            result.fetchone()

        debug("✅ Database connectivity verified")

    except Exception as e:
        log_exception(e, "❌ Database initialization failed")
        raise
