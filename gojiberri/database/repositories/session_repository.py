# database/repositories/session_repository.py
"""
Session repository providing core database operations for session management.

This module implements the data access layer for Session entities, providing
essential CRUD operations required by the current API. It maintains a clean
interface focused on the operations actually used by the application.

"""

from typing import List, Optional
from sqlalchemy import select, desc
from sqlalchemy.ext.asyncio import AsyncSession

from gojiberri.database.models.session_model import (
    Session as SessionTable,
)


class SessionRepository:
    """
    Repository for session database operations with focused functionality.

    This class provides essential database operations for Session entities,
    implementing only the operations currently required by the API layer.
    Each method handles a specific database interaction pattern while
    maintaining consistency and reliability.

    The repository operates within async database sessions provided by
    the calling service layer, ensuring proper transaction management
    and connection pooling.

    Attributes:
        db_session: Active database session for transaction management

    Usage Example:
        async with get_db_session() as session:
            repo = SessionRepository(session)
            new_session = await repo.create_session({
                "patient_name": "John Doe",
                "patient_dob": "1985-03-15",
                "workflow_type": "document_generation"
            })
    """

    def __init__(self, db_session: AsyncSession):
        """
        Initialize repository with active database session.

        Args:
            db_session: Active SQLAlchemy async session for database operations
        """
        self.db_session = db_session

    async def create_session(self, session_data: SessionTable) -> None:
        """
        Create new session with patient information and workflow configuration.

        Args:
            session_data: SessionTable object to create
        """
        self.db_session.add(session_data)

    async def get_session_by_id(self, session_id: str) -> Optional[SessionTable]:
        """
        Retrieve session by ID.

        Fetches complete session information. Only returns
        active sessions (is_active=True).

        Args:
            session_id: External session identifier used by API

        Returns:
            Optional[Session]: Session entity, or None if not found

        Example:
            session = await repo.get_session_by_id("abc123")
            if session:
                print(f"Found session for {session.patient_name}")
        """
        query = (
            select(SessionTable)
            .where(SessionTable.session_id == session_id)
            .where(SessionTable.is_active)
        )

        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()

    async def list_sessions(self, limit: int = 50) -> List[SessionTable]:
        """
        List sessions ordered by creation date with basic pagination.

        Retrieves sessions. Returns only active sessions ordered by most recent first.
        Includes basic pagination to prevent overwhelming response sizes.

        Args:
            limit: Maximum number of sessions to return (default: 50)

        Returns:
            List[Session]: List of session entities

        Example:
            recent_sessions = await repo.list_sessions(limit=10)
            for session in recent_sessions:
                print(f"{session.patient_name} - {session.session_status}")
        """
        query = (
            select(SessionTable)
            .where(SessionTable.is_active)
            .order_by(desc(SessionTable.created_at))
            .limit(limit)
        )

        result = await self.db_session.execute(query)
        return result.scalars().all()

    async def update_session(self, session: SessionTable) -> SessionTable:
        """
        Update an existing session.

        Args:
            session: The session entity to update.

        Returns:
            The updated session entity.
        """
        merged_session = await self.db_session.merge(session)
        return merged_session
