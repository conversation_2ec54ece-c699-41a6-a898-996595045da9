# database/repositories/recording_repository.py
"""
Recording repository providing core database operations for recording management.

This module implements the data access layer for Recording entities, providing
essential CRUD operations required by the current API. It maintains a focused
interface supporting only the operations actually used by the application.

"""

from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from gojiberri.database.models.recording_model import Recording as RecordingTable


class RecordingRepository:
    """
    Repository for recording database operations with focused functionality.

    This class provides essential database operations for Recording entities,
    implementing only the operations currently required by the API layer.
    Each method handles a specific database interaction pattern while
    maintaining consistency and reliability.

    The repository operates within async database sessions provided by
    the calling service layer, ensuring proper transaction management
    and connection pooling.

    Attributes:
        db_session: Active database session for transaction management

    Usage Example:
        async with get_db_session() as session:
            repo = RecordingRepository(session)
            recording = await repo.create_recording({
                "session_id": "session-123",
                "file_path": "recordings/audio.webm",
                "duration": 245.7
            })
    """

    def __init__(self, db_session: AsyncSession):
        """
        Initialize repository with active database session.

        Args:
            db_session: Active SQLAlchemy async session for database operations
        """
        self.db_session = db_session

    async def create_recording(self, recording_data: RecordingTable):
        """
        Create new recording with file location and timing information.

        Creates a new recording entity associated with a session, setting
        appropriate file location and timing metadata for downstream
        processing workflows.

        Args:
            recording_data: Recording SQLAlchemy model containing recording creation parameters
                Required keys:
                    - session_id: str - Parent session identifier
                Optional keys:
                    - file_path: str - Audio file storage location
                    - duration: float - Recording length in seconds
                    - end_time: datetime - When recording ended

        Raises:
            ValueError: If required recording data is missing
            IntegrityError: If recording creation violates database constraints

        Example:
           await repo.create_recording({
                "session_id": "abc123",
                "file_path": "recordings/session_abc123.webm",
                "duration": 180.5,
            })
        """

        self.db_session.add(recording_data)

    async def get_recording_by_id(self, recording_id: str) -> Optional[RecordingTable]:
        """
        Retrieve recording by ID with related session information loaded.

        Fetches complete recording information including associated session
        using eager loading to minimize database round trips. Only returns
        active recordings (is_active=True).

        Args:
            recording_id: External recording identifier used by API

        Returns:
            Optional[Recording]: Recording entity with session loaded, or None if not found

        Example:
            recording = await repo.get_recording_by_id("xyz789")
            if recording:
                print(f"Recording for session: {recording.session.patient_name}")
                print(f"Duration: {recording.duration} seconds")
        """
        query = (
            select(RecordingTable)
            .options(selectinload(RecordingTable.session))
            .where(RecordingTable.recording_id == recording_id)
            .where(RecordingTable.is_active)
        )

        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()

    async def get_recordings_by_session(self, session_id: str) -> List[RecordingTable]:
        """
        Retrieve all recordings for a specific session.

        Fetches all recordings associated with a session for complete
        session audio information. Returns recordings ordered by creation
        date for consistent processing order.

        Args:
            session_id: Session identifier to find recordings for

        Returns:
            List[Recording]: List of recording entities for the session

        Example:
            recordings = await repo.get_recordings_by_session("abc123")
            total_duration = sum(r.duration or 0 for r in recordings)
            print(f"Session has {len(recordings)} recordings, {total_duration}s total")
        """
        query = (
            select(RecordingTable)
            .where(RecordingTable.session_id == session_id)
            .where(RecordingTable.is_active)
            .order_by(RecordingTable.created_at)
        )

        result = await self.db_session.execute(query)
        return result.scalars().all()
