// Simplified Vue Component for Audio Recording with Essential Transmission
export default {
    name: 'AudioRecorder',
    template: '<div style="display: none;"></div>',
    
    props: {
        config: {
            type: Object,
            default: () => ({
            })
        }
    },
    
    data() {
        return {
            // Recording state
            isRecording: false,
            isProcessing: false,

            // Permission state
            hasPermission: false,
            permissionRequested: false,
            permissionJustRequested: false,

            // Media objects
            mediaRecorder: null,
            stream: null,
            audioBlob: null,
            audioChunks: [],

            // Accurate timing system
            startTime: null,
            endTime: null,
            pauseStartTime: null,
            totalPausedDuration: 0,

            // Simplified chunked transmission state
            chunkTransmissionTimer: null,
            transmissionSessionId: null,
            isTransmissionActive: false,
            
            // Essential tracking (5 core areas)
            chunkSequence: {
                lastSentIndex: -1,           // 1. Chunk sequencing
                totalExpectedChunks: 0,      // 2. Completion detection
                sentChunks: new Set(),       // 3. Basic duplicate handling
                totalBytesSent: 0,           // 4. Simple validation (size)
                errors: []                   // 5. Clear error reporting
            },
            
            // Basic connection status
            connectionStatus: 'connected',
            
            // Configuration
            chunkSizeMs: 5000,
            audioQuality: {},
            debugEnabled: false,
            mimeType: "audio/webm",
            sendIntervalMs: 5000
        }
    },
    
    computed: {
        recordingState() {
            return {
                isRecording: this.isRecording,
                hasPermission: this.hasPermission,
                hasAudio: !!this.audioBlob,
                chunkCount: this.audioChunks.length,
                currentDuration: this.getCurrentRecordingDuration(),
                transmissionStatus: {
                    lastSentIndex: this.chunkSequence.lastSentIndex,
                    totalBytesSent: this.chunkSequence.totalBytesSent,
                    errors: this.chunkSequence.errors.length,
                    connectionStatus: this.connectionStatus
                }
            };
        }
    },
    
    watch: {
        isRecording(newVal, oldVal) {
            if (newVal && !oldVal) {
                this.startSimpleChunkedTransmission();
            } else if (!newVal && oldVal) {
                this.stopSimpleChunkedTransmission();
            }
        }
    },
    
    mounted() {
        console.log('🔧 Vue mounted - checking props:');
        console.log('🔧 this.$props:', this.$props);
        console.log('🔧 this.config:', this.config);
        console.log('🔧 All component data:', this.$data);
        this.initializeComponent();
        this.checkInitialPermissionState();
    },
    
    beforeUnmount() {
        this.cleanup();
    },
    
    methods: {
        // Duration calculation methods
        getCurrentRecordingDuration() {
            if (!this.startTime) return 0;

            const now = Date.now();
            const totalElapsed = now - this.startTime;

            let currentPauseDuration = this.totalPausedDuration;
            if (this.mediaRecorder?.state === 'paused' && this.pauseStartTime) {
                currentPauseDuration += (now - this.pauseStartTime);
            }

            return Math.max(0, totalElapsed - currentPauseDuration);
        },

        getActualRecordingDuration() {
            if (!this.startTime) return 0;

            const endTime = this.endTime || Date.now();
            const totalElapsed = endTime - this.startTime;
            return Math.max(0, totalElapsed - this.totalPausedDuration);
        },

        resetTimestamps() {
            this.startTime = null;
            this.endTime = null;
            this.pauseStartTime = null;
            this.totalPausedDuration = 0;
        },

        initializeComponent() {
            this.chunkSizeMs = this.config.recording.chunk_size_ms;
            this.audioQuality = this.config.audio.quality;
            this.debugEnabled = this.config.debug.enabled;
            this.mimeType = this.getSupportedWebMType();
            this.sendIntervalMs = this.config.chunked_transmission?.send_interval_ms || 5000;

            console.log('🔧 DEBUG Vue received format config:', this.config.audio.format);
            console.log('🔧 DEBUG Vue received supported_codecs:', this.config.audio.supported_codecs);
            
            if (this.debugEnabled) {
                console.log('🔧 Simplified Audio Recorder initialized:', {
                    chunkSize: this.chunkSizeMs,
                    mimeType: this.mimeType,
                    sendInterval: this.sendIntervalMs
                });
            }
        },
        
        getSupportedWebMType() {
            const webmTypes = this.config.audio.supported_codecs;
            for (const type of webmTypes) {
                if (MediaRecorder && MediaRecorder.isTypeSupported(type)) {
                    console.log(`🟢 Using WebM format: ${type}`);
                    return type;
                }
            }
            console.warn('⚠️ No WebM audio format supported');
            return this.config.audio.format;
        },
        
        async checkInitialPermissionState() {
            try {
                if (navigator.permissions && navigator.permissions.query) {
                    const permissionStatus = await navigator.permissions.query({ name: 'microphone' });
                    this.hasPermission = permissionStatus.state === 'granted';
                    this.permissionRequested = permissionStatus.state !== 'prompt';
                    
                    if (permissionStatus.state === 'granted') {
                        this.emitEvent('permission_granted');
                    } else if (permissionStatus.state === 'denied') {
                        this.emitEvent('permission_denied', { 
                            error: 'Microphone access denied. Please enable microphone permissions.' 
                        });
                    }
                    
                    permissionStatus.onchange = () => {
                        this.hasPermission = permissionStatus.state === 'granted';
                        this.permissionJustRequested = false;
                        if (permissionStatus.state === 'granted') {
                            this.emitEvent('permission_granted');
                        } else if (permissionStatus.state === 'denied') {
                            this.emitEvent('permission_denied', { 
                                error: 'Microphone access denied via settings.' 
                            });
                        }
                    };
                }
            } catch (error) {
                console.log('🔧 Permission API not supported');
            }
        },
        
        async requestMicrophonePermission() {
            try {
                this.permissionRequested = true;
                this.permissionJustRequested = true;
                console.log('🔵 Requesting microphone permission...');
                
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: this.audioQuality
                });
                
                this.hasPermission = true;
                stream.getTracks().forEach(track => track.stop());
                console.log('🟢 Microphone permission granted');
                this.emitEvent('permission_granted');
                return { success: true, message: 'Microphone permission granted' };
                
            } catch (error) {
                this.hasPermission = false;
                this.permissionJustRequested = false;
                const errorMsg = this.getPermissionErrorMessage(error);
                console.error('🔴 Microphone permission error:', error);
                this.emitEvent('permission_denied', { error: errorMsg });
                return { success: false, error: errorMsg };
            }
        },
        
        async ensurePermission() {
            if (!this.permissionRequested) {
                const result = await this.requestMicrophonePermission();
                return { hasPermission: result.success, popupShown: true };
            }
            
            return { hasPermission: this.hasPermission, popupShown: false };
        },
        
        // Simplified chunked transmission (5 essential areas)
        startSimpleChunkedTransmission() {
            if (this.isTransmissionActive) {
                console.log('🟡 Chunked transmission already active');
                return;
            }
            
            this.transmissionSessionId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            this.isTransmissionActive = true;
            
            // Reset essential tracking
            this.chunkSequence = {
                lastSentIndex: -1,
                totalExpectedChunks: 0,
                sentChunks: new Set(),
                totalBytesSent: 0,
                errors: []
            };
            
            console.log(`🔵 Starting simplified chunked transmission (session: ${this.transmissionSessionId})`);
            
            this.emitEvent('chunked_transmission_start', {
                sessionId: this.transmissionSessionId,
                sendInterval: this.sendIntervalMs
            });
            
            this.chunkTransmissionTimer = setInterval(() => {
                this.sendAvailableChunks();
            }, this.sendIntervalMs);
        },
        
        stopSimpleChunkedTransmission() {
            if (!this.isTransmissionActive) return;
            
            console.log('🔵 Stopping simplified chunked transmission...');
            
            if (this.chunkTransmissionTimer) {
                clearInterval(this.chunkTransmissionTimer);
                this.chunkTransmissionTimer = null;
            }
            
            this.sendRemainingChunks();
            this.isTransmissionActive = false;
            console.log('🟢 Simplified chunked transmission stopped');
        },
        
        async sendAvailableChunks() {
            if (!this.isTransmissionActive || this.audioChunks.length === 0) return;
            
            // Check connection status
            if (!this.checkConnectionStatus()) {
                this.chunkSequence.errors.push('Connection lost during transmission');
                return;
            }
            
            const availableChunks = this.audioChunks.length - 1 - this.chunkSequence.lastSentIndex;
            if (availableChunks <= 0) {
                return;
            }
            
            console.log(`🔵 Sending ${availableChunks} available chunks...`);
            
            try {
                for (let i = this.chunkSequence.lastSentIndex + 1; i < this.audioChunks.length; i++) {
                    await this.sendChunkSimple(i);
                    this.chunkSequence.lastSentIndex = i;
                }
            } catch (error) {
                console.error('🔴 Error sending available chunks:', error);
                this.chunkSequence.errors.push(`Send error: ${error.message}`);
                this.emitEvent('chunked_transmission_error', { 
                    error: error.message,
                    sessionId: this.transmissionSessionId
                });
            }
        },
        
        async sendRemainingChunks() {
            if (this.audioChunks.length === 0) {
                console.log('🟡 No remaining chunks to send');
                this.finalizeTransmission();
                return;
            }
            
            const remainingChunks = this.audioChunks.length - 1 - this.chunkSequence.lastSentIndex;
            console.log(`🔵 Sending ${remainingChunks} remaining chunks...`);
            
            try {
                for (let i = this.chunkSequence.lastSentIndex + 1; i < this.audioChunks.length; i++) {
                    await this.sendChunkSimple(i);
                    this.chunkSequence.lastSentIndex = i;
                }
                this.finalizeTransmission();
            } catch (error) {
                console.error('🔴 Error sending remaining chunks:', error);
                this.chunkSequence.errors.push(`Final send error: ${error.message}`);
                this.emitEvent('chunked_transmission_error', { 
                    error: error.message,
                    sessionId: this.transmissionSessionId
                });
            }
        },
        
        async sendChunkSimple(chunkIndex) {
            if (chunkIndex >= this.audioChunks.length) return;
            
            const chunk = this.audioChunks[chunkIndex];
            const TRANSMISSION_CHUNK_SIZE = this.config.chunked_transmission?.chunk_size || 64 * 1024;
            
            const base64Data = await this.convertBlobToBase64(chunk);
            const totalTransmissionChunks = Math.ceil(base64Data.length / TRANSMISSION_CHUNK_SIZE);
            
            console.log(`🔧 Sending audio chunk ${chunkIndex} as ${totalTransmissionChunks} transmission chunks`);
            
            for (let transmissionIndex = 0; transmissionIndex < totalTransmissionChunks; transmissionIndex++) {
                const start = transmissionIndex * TRANSMISSION_CHUNK_SIZE;
                const end = Math.min(start + TRANSMISSION_CHUNK_SIZE, base64Data.length);
                const transmissionChunk = base64Data.substring(start, end);
                
                // 3. Basic duplicate handling - check if already sent
                const chunkKey = `${chunkIndex}_${transmissionIndex}`;
                if (this.chunkSequence.sentChunks.has(chunkKey)) {
                    console.log(`🟡 Skipping duplicate chunk: ${chunkKey}`);
                    continue;
                }
                
                this.emitEvent('chunked_audio_data', {
                    sessionId: this.transmissionSessionId,
                    audioChunkIndex: chunkIndex,                    // 1. Chunk sequencing
                    transmissionChunkIndex: transmissionIndex,
                    totalTransmissionChunks: totalTransmissionChunks,
                    base64Data: transmissionChunk,
                    chunkSize: end - start,                         // 4. Simple validation (size)
                    originalChunkSize: chunk.size,
                    isLastTransmissionChunk: transmissionIndex === totalTransmissionChunks - 1,
                    timestamp: Date.now()
                });
                
                // Track sent chunks
                this.chunkSequence.sentChunks.add(chunkKey);
                this.chunkSequence.totalBytesSent += (end - start);
                
                if (transmissionIndex < totalTransmissionChunks - 1) {
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
            }
            
            console.log(`🟢 Audio chunk ${chunkIndex} sent successfully (${chunk.size} bytes)`);
        },
        
        finalizeTransmission() {
            this.chunkSequence.totalExpectedChunks = this.audioChunks.length;

            // Use accurate timestamp-based duration
            const accurateDuration = this.getActualRecordingDuration();

            console.log(`🔵 Finalizing transmission: ${this.audioChunks.length} audio chunks, ${this.chunkSequence.totalBytesSent} bytes sent`);

            const transmissionReport = {
                sessionId: this.transmissionSessionId,
                totalAudioChunks: this.audioChunks.length,
                totalBytesSent: this.chunkSequence.totalBytesSent,
                estimatedDuration: accurateDuration,
                success: this.chunkSequence.errors.length === 0,
                errors: this.chunkSequence.errors,
                connectionStatus: this.connectionStatus
            };

            this.emitEvent('chunked_transmission_complete', transmissionReport);

            console.log('🟢 Chunked transmission finalized with accurate duration');
        },
        
        // Basic connection status check
        checkConnectionStatus() {
            // Simple check 
            if (!navigator.onLine) {
                this.connectionStatus = 'offline';
                return false;
            }
            
            this.connectionStatus = 'connected';
            return true;
        },
        
        // Standard recording methods
        async startRecording() {
            try {
                console.log('🔵 Starting recording...');
                
                if (!MediaRecorder.isTypeSupported(this.mimeType)) {
                    const errorMsg = `WebM recording not supported: ${this.mimeType}`;
                    this.emitEvent('recording_error', { error: errorMsg });
                    return { success: false, error: errorMsg };
                }
                
                const permissionResult = await this.ensurePermission();
                
                if (permissionResult.popupShown) {
                    const message = permissionResult.hasPermission 
                        ? 'Microphone permission granted. Please click Start Recording again to begin.'
                        : 'Microphone permission required. Please allow access and try again.';
                    
                    console.log('🟡 Permission popup shown - stopping recording attempt');
                    this.emitEvent('permission_popup_shown', { 
                        message: message,
                        permissionGranted: permissionResult.hasPermission
                    });
                    
                    return { 
                        success: false, 
                        error: message,
                        requiresUserAction: true,
                        permissionPopupShown: true
                    };
                }
                
                if (!permissionResult.hasPermission) {
                    const errorMsg = 'Microphone permission denied';
                    this.emitEvent('recording_error', { error: errorMsg });
                    return { success: false, error: errorMsg };
                }
                
                this.stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: this.audioQuality
                });
                
                this.resetRecordingState();
                this.createMediaRecorder();
                this.mediaRecorder.start(this.chunkSizeMs);
                
                console.log(`🟢 Recording started with ${this.chunkSizeMs}ms chunks`);
                
                return { 
                    success: true, 
                    message: 'Recording started with simplified chunked transmission',
                    chunkSizeMs: this.chunkSizeMs,
                    sendInterval: this.sendIntervalMs
                };
                
            } catch (error) {
                const errorMsg = this.getRecordingErrorMessage(error);
                this.emitEvent('recording_error', { error: errorMsg });
                return { success: false, error: errorMsg };
            }
        },
        
        async stopRecording() {
            console.log('🔵 Stopping recording...');
            
            return new Promise((resolve, reject) => {
                const cleanupAndResolve = (result) => {
                    console.log('🟢 stopRecording completed:', result);
                    resolve(result);
                };
                
                const cleanupAndReject = (error) => {
                    console.error('🔴 stopRecording failed:', error);
                    this.cleanup();
                    reject(error);
                };
                
                try {
                    if (!this.mediaRecorder) {
                        console.log('🟡 No mediaRecorder');
                        this.isRecording = false;

                        // Use accurate duration instead of 0
                        const finalDuration = this.getActualRecordingDuration();
                        this.endTime = Date.now();

                        this.emitEvent('recording_stopped', {
                            duration: finalDuration,
                            chunkCount: 0
                        });
                        cleanupAndResolve({ success: true, message: 'No active recording', duration: finalDuration });
                        return;
                    }
                    
                    if (this.mediaRecorder.state === 'recording') {
                        this.mediaRecorder.stop();
                        this.stopMediaStream();
                    } else if (this.mediaRecorder.state === 'paused') {
                        this.mediaRecorder.stop();
                        this.stopMediaStream();
                    }
                    
                    this.mediaRecorder.onstop = () => {
                        this.isRecording = false;
                        console.log('🟡 MediaRecorder stopped, calculating final duration...');

                        setTimeout(() => {
                            // Set end time and handle final pause if needed
                            this.endTime = Date.now();
                            if (this.pauseStartTime) {
                                this.totalPausedDuration += (this.endTime - this.pauseStartTime);
                                this.pauseStartTime = null;
                            }

                            // Use accurate duration calculation
                            const finalDuration = this.getActualRecordingDuration();

                            this.emitEvent('recording_stopped', {
                                duration: finalDuration,
                                chunkCount: this.audioChunks.length,
                                totalBytesSent: this.chunkSequence.totalBytesSent,
                                realTimeTransmission: true
                            });

                            cleanupAndResolve({
                                success: true,
                                message: 'Recording completed with accurate duration',
                                duration: finalDuration,
                                chunkCount: this.audioChunks.length,
                                totalBytesSent: this.chunkSequence.totalBytesSent,
                                realTimeTransmission: true
                            });
                        }, 100);
                    };
                    
                } catch (error) {
                    console.error('🔴 stopRecording outer error:', error);
                    const errorMsg = `Stop recording error: ${error.message}`;
                    this.emitEvent('recording_error', { error: errorMsg });
                    cleanupAndReject({ success: false, error: errorMsg });
                }
            });
        },
        
        // Utility methods
        resetRecordingState() {
            this.audioChunks = [];
            this.audioBlob = null;
            this.clearAudioUrl();
            this.permissionJustRequested = false;

            // Reset timestamps
            this.resetTimestamps();

            // Reset essential tracking
            this.chunkSequence = {
                lastSentIndex: -1,
                totalExpectedChunks: 0,
                sentChunks: new Set(),
                totalBytesSent: 0,
                errors: []
            };
        },
        
        createMediaRecorder() {
            this.mediaRecorder = new MediaRecorder(this.stream, {
                mimeType: this.mimeType
            });
            
            console.log(`🔵 MediaRecorder created: ${this.mimeType}, chunk size: ${this.chunkSizeMs}ms`);
            this.setupMediaRecorderEvents();
        },
        
        setupMediaRecorderEvents() {
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                    console.log(`🔧 Audio chunk ${this.audioChunks.length}: ${event.data.size} bytes`);
                }
            };
            
            this.mediaRecorder.onstart = () => {
                this.isRecording = true;
                this.startTime = Date.now(); // Start timestamp tracking
                console.log('🟢 Recording started');
                this.emitEvent('recording_started', {
                    chunkSizeMs: this.chunkSizeMs,
                    sendInterval: this.sendIntervalMs,
                    realTimeTransmission: true
                });
            };
            
            this.mediaRecorder.onstop = () => {
                this.isRecording = false;
                console.log(`🟡 Recording stopped: ${this.audioChunks.length} chunks`);
            };
            
            this.mediaRecorder.onpause = () => {
                console.log('🟡 Recording paused');
                this.emitEvent('recording_paused');
            };
            
            this.mediaRecorder.onresume = () => {
                console.log('🟢 Recording resumed');
                this.emitEvent('recording_resumed');
            };
            
            this.mediaRecorder.onerror = (event) => {
                const errorMsg = `Recording error: ${event.error?.message || 'Unknown error'}`;
                console.error('🔴 MediaRecorder error:', event.error);
                this.emitEvent('recording_error', { error: errorMsg });
            };
        },
        
        // Pause/Resume methods
        async pauseRecording() {
            console.log('🔵 Pausing recording...');

            return new Promise((resolve) => {
                try {
                    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
                        this.pauseStartTime = Date.now(); // Track pause start
                        this.mediaRecorder.pause();
                        console.log('🟡 Recording paused');
                        resolve({ success: true, message: 'Recording paused' });
                    } else {
                        resolve({ success: false, error: 'Cannot pause - not recording' });
                    }
                } catch (error) {
                    console.error('🔴 Pause error:', error);
                    resolve({ success: false, error: `Pause error: ${error.message}` });
                }
            });
        },
        
        async resumeRecording() {
            console.log('🔵 Resuming recording...');

            return new Promise((resolve) => {
                try {
                    if (this.mediaRecorder && this.mediaRecorder.state === 'paused') {
                        // Calculate and accumulate pause duration
                        if (this.pauseStartTime) {
                            this.totalPausedDuration += (Date.now() - this.pauseStartTime);
                            this.pauseStartTime = null;
                        }

                        this.mediaRecorder.resume();
                        console.log('🟢 Recording resumed');
                        resolve({ success: true, message: 'Recording resumed' });
                    } else {
                        resolve({ success: false, error: 'Cannot resume - not paused' });
                    }
                } catch (error) {
                    console.error('🔴 Resume error:', error);
                    resolve({ success: false, error: `Resume error: ${error.message}` });
                }
            });
        },
        
        // Utility methods
        async convertBlobToBase64(blob) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    try {
                        const base64 = reader.result.split(',')[1];
                        resolve(base64);
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });
        },
        
        stopMediaStream() {
            if (this.stream) {
                console.log('🔵 Stopping media stream');
                this.stream.getTracks().forEach(track => track.stop());
                this.stream = null;
            }
        },
        
        clearAudioUrl() {
            if (this.audioUrl) {
                URL.revokeObjectURL(this.audioUrl);
                this.audioUrl = null;
            }
        },
        
        cleanup() {
            console.log('🔵 Cleaning up...');
            this.isRecording = false;
            this.stopSimpleChunkedTransmission();
            this.stopMediaStream();
            this.clearAudioUrl();
            
            if (this.mediaRecorder) {
                this.mediaRecorder = null;
            }
            
            this.audioChunks = [];
            this.audioBlob = null;
            this.permissionJustRequested = false;
            
            // Reset essential tracking
            this.chunkSequence = {
                lastSentIndex: -1,
                totalExpectedChunks: 0,
                sentChunks: new Set(),
                totalBytesSent: 0,
                errors: []
            };
        },
        
        // Error handling methods
        getPermissionErrorMessage(error) {
            const errorMessages = {
                'NotAllowedError': 'Microphone access denied. Please allow microphone access.',
                'NotFoundError': 'No microphone found. Please connect a microphone.',
                'NotSupportedError': 'Recording not supported. Please use Chrome, Firefox, or Edge.',
                'NotReadableError': 'Microphone in use. Please close other applications.',
                'SecurityError': 'Security restrictions. Please use HTTPS or localhost.'
            };
            return errorMessages[error.name] || `Microphone error: ${error.message}`;
        },
        
        getRecordingErrorMessage(error) {
            const errorMessages = {
                'NotAllowedError': 'Microphone access denied during recording.',
                'NotFoundError': 'Microphone not found during recording.',
                'NotReadableError': 'Microphone in use by another application.'
            };
            return errorMessages[error.name] || `Recording failed: ${error.message}`;
        },
        
        emitEvent(eventName, data = {}) {
            this.$emit(eventName, data);
        },
        
        debugState() {
            const state = this.recordingState;
            console.log('🔧 Simplified Audio Recorder Debug State:', state);
            return state;
        },
        
        forceCleanup() {
            console.log('🔧 Force cleanup');
            this.cleanup();
            return { success: true, message: 'Simplified cleanup completed' };
        }
    }
};