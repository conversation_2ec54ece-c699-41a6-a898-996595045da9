"""
Patient information input component.
"""

from typing import Dict
from nicegui import ui

from gojiberri.ui.config import ICONS


def create_patient_info_section(container) -> Dict[str, ui.input]:
    """
    Create the patient info section with DOB and Name inputs.

    Args:
        container: UI container to place the inputs in

    Returns:
        Dictionary with name and DOB input references
    """
    inputs = {}

    with container:
        # DOB row
        with ui.element().classes("flex items-center gap-4 w-full"):
            ui.icon(ICONS["calendar_icon"]).classes("text-gray-600 text-3xl")

            inputs["dob"] = (
                ui.input(label="Patient D.O.B")
                .classes("test-input")
                .props('type="date" "')
                .props('color="black" ')
            )

        # Name row
        with ui.element().classes("flex items-center gap-4 w-full mt-0"):
            ui.icon(ICONS["person_icon"]).classes("text-gray-600 text-3xl")

            inputs["name"] = (
                ui.input(placeholder="Patient Name")
                .classes("flex-1 placeholder:text-3xl p-0 test-input")
                .props('color="black" ')
            )

    return inputs
