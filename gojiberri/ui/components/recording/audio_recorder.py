# components/recording/audio_recorder.py
"""
Simplified Audio Recorder with Essential Chunk Transmission

This module provides audio recording with streamlined chunk transmission focusing on:
1. Chunk sequencing - Keep chunks in correct order
2. Completion detection - Know when all chunks are received
3. Basic duplicate handling - Prevent duplicate processing
4. Simple validation - Basic size checks only
5. Clear error reporting - Simple error messages

Designed for guaranteed audio transmission via NiceGUI events.
"""

import base64
import asyncio
import time
from typing import Callable, Optional, Dict, Any, Set
from dataclasses import dataclass, field
from nicegui import events, ui
from gojiberri.utils.logger import error, log_exception, warning, debug
from gojiberri.ui.config.config_loader import config


@dataclass
class TransmissionState:
    """Essential state tracking for chunk transmission."""

    # 1. Chunk sequencing
    expected_chunks: Dict[int, int] = field(
        default_factory=dict
    )  # audio_chunk_index -> total_transmission_chunks

    # 2. Completion detection
    received_chunks: Dict[int, bytes] = field(
        default_factory=dict
    )  # audio_chunk_index -> assembled_data
    total_expected_audio_chunks: int = 0

    # 3. Basic duplicate handling
    processed_transmission_chunks: Set[str] = field(
        default_factory=set
    )  # "audio_index_transmission_index"

    # 4. Simple validation (size tracking)
    total_bytes_received: int = 0
    chunk_sizes: Dict[int, int] = field(
        default_factory=dict
    )  # audio_chunk_index -> size

    # 5. Clear error reporting
    errors: list = field(default_factory=list)
    missing_chunks: Set[int] = field(default_factory=set)

    # Basic connection/timing
    transmission_start_time: Optional[float] = None
    session_id: Optional[str] = None


class AudioRecorder(ui.element, component="audio_recorder.js"):
    """Simplified Vue.js audio recorder with essential chunk transmission."""

    def __init__(
        self,
        *,
        on_audio_ready: Optional[Callable[[bytes], None]] = None,
        on_recording_started: Optional[Callable[[], None]] = None,
        on_recording_stopped: Optional[Callable[[Dict[str, Any]], None]] = None,
        on_recording_paused: Optional[Callable[[], None]] = None,
        on_recording_resumed: Optional[Callable[[], None]] = None,
        on_recording_error: Optional[Callable[[str], None]] = None,
        on_permission_granted: Optional[Callable[[], None]] = None,
        on_permission_denied: Optional[Callable[[str], None]] = None,
    ) -> None:

        # Load configuration
        self.recording_config = config.get_section("recording")
        self.js_config = self._prepare_vue_config()

        super().__init__()

        # Pass JS config to Vue
        if hasattr(self, "_props"):
            self._props = {"config": self.js_config}

        # Audio data and metadata
        self.recording_data: bytes = b""
        self.recording_metadata: Dict[str, Any] = {}

        # Component state
        self.is_recording: bool = False
        self.is_paused: bool = False
        self.has_permission: bool = False
        self.permission_requested: bool = False
        self.permission_error_message: str = ""
        self.is_destroyed: bool = False
        self.recording_session_active: bool = False
        self.is_processing: bool = False
        self.processing_aborted: bool = False

        # Simplified transmission state
        self._transmission_state = TransmissionState()
        self._is_receiving_chunks = False

        # Configuration values
        self.min_duration_ms = config.get("recording.min_duration_ms", 500)
        self.min_file_size_bytes = config.get("recording.min_file_size_bytes", 512)

        # Event callbacks
        self._on_audio_ready = on_audio_ready
        self._on_recording_started = on_recording_started
        self._on_recording_stopped = on_recording_stopped
        self._on_recording_paused = on_recording_paused
        self._on_recording_resumed = on_recording_resumed
        self._on_recording_error = on_recording_error
        self._on_permission_granted = on_permission_granted
        self._on_permission_denied = on_permission_denied

        self._setup_event_handlers()

    def _prepare_vue_config(self) -> Dict[str, Any]:
        """Prepare simplified configuration for Vue component."""
        try:
            debug("🔧 DEBUG: Sending format to Vue:")
            return {
                "audio": {
                    "format": self.recording_config.get("audio", {}).get(
                        "format", "audio/webm"
                    ),
                    "supported_codecs": self.recording_config.get("audio", {}).get(
                        "supported_codecs",
                        [
                            "audio/webm;codecs=opus",
                            "audio/webm;codecs=vorbis",
                            "audio/webm",
                        ],
                    ),
                    "quality": self.recording_config.get("audio", {}).get(
                        "quality",
                        {
                            "echo_cancellation": True,
                            "noise_suppression": True,
                            "auto_gain_control": True,
                        },
                    ),
                },
                "recording": {
                    "chunk_size_ms": self.recording_config.get("chunk_size_ms", 5000),
                    "min_duration_ms": self.recording_config.get(
                        "min_duration_ms", 500
                    ),
                },
                "debug": {
                    "enabled": config.get("debug", False),
                },
                "chunked_transmission": {
                    "enabled": config.get("chunked_transmission.enabled", True),
                    "chunk_size": config.get(
                        "chunked_transmission.chunk_size", 64 * 1024
                    ),
                    "send_interval_ms": config.get(
                        "chunked_transmission.send_interval_ms", 5000
                    ),
                },
            }

        except Exception as e:
            log_exception(e, "Error preparing Vue configuration")
            return self._get_default_vue_config()

    def _get_default_vue_config(self) -> Dict[str, Any]:
        """Default configuration."""
        return {
            "audio": {
                "format": "audio/webm",
                "supported_codecs": [
                    "audio/webm;codecs=opus",
                    "audio/webm;codecs=vorbis",
                    "audio/webm",
                ],
                "quality": {
                    "echo_cancellation": True,
                    "noise_suppression": True,
                    "auto_gain_control": True,
                },
            },
            "recording": {"chunk_size_ms": 5000, "min_duration_ms": 500},
            "debug": {"enabled": False},
            "chunked_transmission": {
                "enabled": True,
                "chunk_size": 64 * 1024,
                "send_interval_ms": 5000,
            },
        }

    def _setup_event_handlers(self) -> None:
        """Simplified event handlers focusing on essential functionality."""

        # Chunked transmission handlers
        def handle_chunked_transmission_start(e: events.GenericEventArguments) -> None:
            """Handle start of chunked audio transmission."""
            if self.is_destroyed:
                return

            try:
                session_id = e.args.get("sessionId")
                send_interval = e.args.get("sendInterval", 5000)

                # Reset simplified state
                self._transmission_state = TransmissionState()
                self._transmission_state.session_id = session_id
                self._transmission_state.transmission_start_time = time.time()
                self._is_receiving_chunks = True

                debug(
                    f"Started chunked transmission: session={session_id}, interval={send_interval}ms"
                )

            except Exception as ex:
                error(f"Error handling chunked transmission start: {ex}")

        async def handle_chunked_audio_data(e: events.GenericEventArguments) -> None:
            """Handle individual chunked audio data with essential validation."""
            if self.is_destroyed or not self._is_receiving_chunks:
                return

            try:
                session_id = e.args.get("sessionId")
                if session_id != self._transmission_state.session_id:
                    warning(f"Received chunk for wrong session: {session_id}")
                    return

                # Extract chunk data
                audio_chunk_index = e.args.get("audioChunkIndex", -1)
                transmission_chunk_index = e.args.get("transmissionChunkIndex", -1)
                total_transmission_chunks = e.args.get("totalTransmissionChunks", 0)
                base64_data = e.args.get("base64Data", "")
                chunk_size = e.args.get("chunkSize", 0)
                original_chunk_size = e.args.get("originalChunkSize", 0)
                is_last_transmission_chunk = e.args.get(
                    "isLastTransmissionChunk", False
                )

                if audio_chunk_index < 0 or transmission_chunk_index < 0:
                    warning("Invalid chunk indices received")
                    return

                # 3. Basic duplicate handling
                chunk_key = f"{audio_chunk_index}_{transmission_chunk_index}"
                if chunk_key in self._transmission_state.processed_transmission_chunks:
                    debug(f"Ignoring duplicate transmission chunk: {chunk_key}")
                    return

                # Mark as processed
                self._transmission_state.processed_transmission_chunks.add(chunk_key)

                # 4. Simple validation - size check
                if chunk_size <= 0 or len(base64_data) == 0:
                    error(
                        f"Invalid chunk size or data: {chunk_size}, {len(base64_data)}"
                    )
                    self._transmission_state.errors.append(f"Invalid chunk {chunk_key}")
                    return

                # 1. Chunk sequencing - track expected chunks
                if audio_chunk_index not in self._transmission_state.expected_chunks:
                    self._transmission_state.expected_chunks[audio_chunk_index] = (
                        total_transmission_chunks
                    )

                # Store transmission chunk data temporarily
                if not hasattr(self, "_temp_transmission_chunks"):
                    self._temp_transmission_chunks = {}

                self._temp_transmission_chunks[chunk_key] = base64_data.encode("utf-8")
                self._transmission_state.total_bytes_received += chunk_size

                # Assemble audio chunk when all transmission chunks received
                if is_last_transmission_chunk:
                    await self._assemble_audio_chunk_simple(
                        audio_chunk_index,
                        total_transmission_chunks,
                        original_chunk_size,
                    )

            except Exception as ex:
                error(f"Error handling chunked audio data: {ex}")
                self._transmission_state.errors.append(f"Processing error: {str(ex)}")

        def handle_chunked_transmission_complete(
            e: events.GenericEventArguments,
        ) -> None:
            """Handle completion of chunked audio transmission."""
            if self.is_destroyed or not self._is_receiving_chunks:
                return

            try:
                session_id = e.args.get("sessionId")
                if session_id != self._transmission_state.session_id:
                    warning(f"Received completion for wrong session: {session_id}")
                    return

                total_audio_chunks = e.args.get("totalAudioChunks", 0)
                success = e.args.get("success", False)
                errors = e.args.get("errors", [])

                # 2. Completion detection
                self._transmission_state.total_expected_audio_chunks = (
                    total_audio_chunks
                )

                # Check if we have all chunks
                validation_result = self._validate_completion()

                if validation_result["is_complete"] and success:
                    # Assemble final audio data
                    self._assemble_final_audio_data(e)

                    debug(
                        f"Chunked transmission completed successfully: ==={len(self._transmission_state.received_chunks)}== chunks"
                    )

                    # Call audio ready callback
                    if self._on_audio_ready:
                        self._on_audio_ready(self.recording_data)
                else:
                    # 5. Clear error reporting
                    error_msg = f"Transmission incomplete: {validation_result.get('error', 'Unknown error')}"
                    if errors:
                        error_msg += f". JS errors: {errors}"

                    error(error_msg)
                    self._transmission_state.errors.append(error_msg)

                    if self._on_recording_error:
                        self._on_recording_error(error_msg)

                self._is_receiving_chunks = False

            except Exception as ex:
                error(f"Error handling chunked transmission completion: {ex}")

        def handle_chunked_transmission_error(e: events.GenericEventArguments) -> None:
            """Handle errors in chunked transmission."""
            if self.is_destroyed:
                return

            try:
                session_id = e.args.get("sessionId")
                error_msg = e.args.get("error", "Unknown chunked transmission error")

                error(
                    f"Chunked transmission error for session {session_id}: {error_msg}"
                )
                self._transmission_state.errors.append(error_msg)

                # Reset state
                self._is_receiving_chunks = False

                # Call error callback
                if self._on_recording_error:
                    self._on_recording_error(f"Transmission error: {error_msg}")

            except Exception as ex:
                error(f"Error handling chunked transmission error: {ex}")

        # Standard recording event handlers
        def handle_recording_started(e: events.GenericEventArguments) -> None:
            if self.is_destroyed:
                return
            self.is_recording = True
            self.is_paused = False
            self.recording_session_active = True
            self.processing_aborted = False

            debug("Recording started")
            if self._on_recording_started:
                self._on_recording_started()

        def handle_recording_stopped(e: events.GenericEventArguments) -> None:
            if self.is_destroyed:
                return
            duration = e.args.get("duration", 0)
            chunk_count = e.args.get("chunkCount", 0)
            total_bytes_sent = e.args.get("totalBytesSent", 0)
            real_time_transmission = e.args.get("realTimeTransmission", False)

            self.is_recording = False
            self.is_paused = False
            self.recording_session_active = False

            debug(
                f"Recording stopped: {duration}ms, {chunk_count} chunks, real-time: {real_time_transmission}"
            )

            if self._on_recording_stopped:
                self._on_recording_stopped(
                    {
                        "duration": duration,
                        "chunk_count": chunk_count,
                        "total_bytes_sent": total_bytes_sent,
                        "real_time_transmission": real_time_transmission,
                    }
                )

        def handle_recording_paused(e: events.GenericEventArguments) -> None:
            if self.is_destroyed:
                return
            self.is_paused = True
            if self._on_recording_paused:
                self._on_recording_paused()

        def handle_recording_resumed(e: events.GenericEventArguments) -> None:
            if self.is_destroyed:
                return
            self.is_paused = False
            if self._on_recording_resumed:
                self._on_recording_resumed()

        def handle_recording_error(e: events.GenericEventArguments) -> None:
            if self.is_destroyed:
                return
            error_msg = e.args.get("error", "Unknown recording error")
            self.is_recording = False
            self.is_paused = False
            self.recording_session_active = False
            self.processing_aborted = True
            error(f"Recording error: {error_msg}")
            if self._on_recording_error:
                self._on_recording_error(error_msg)

        def handle_permission_granted(e: events.GenericEventArguments) -> None:
            if self.is_destroyed:
                return
            self.has_permission = True
            self.permission_requested = True
            self.permission_error_message = ""
            if self._on_permission_granted:
                self._on_permission_granted()

        def handle_permission_denied(e: events.GenericEventArguments) -> None:
            if self.is_destroyed:
                return
            error_msg = e.args.get("error", "Microphone permission denied")
            self.has_permission = False
            self.permission_requested = True
            self.permission_error_message = error_msg
            error(f"Microphone permission denied: {error_msg}")
            if self._on_permission_denied:
                self._on_permission_denied(error_msg)

        # Fallback handler for traditional audio_ready
        def handle_audio_ready(e: events.GenericEventArguments) -> None:
            """Handle traditional audio_ready event (fallback)."""
            if self.is_destroyed:
                return

            # Only use this if chunked transmission is not active
            if self._is_receiving_chunks:
                debug("Ignoring traditional audio_ready - using chunked transmission")
                return

            try:
                audio_base64 = e.args["audioBlobBase64"]
                self.recording_data = base64.b64decode(audio_base64.encode())

                self.recording_metadata = {
                    "mime_type": e.args.get(
                        "mimeType", self.recording_config["audio"]["format"]
                    ),
                    "size": e.args.get("size", len(self.recording_data)),
                    "duration": e.args.get("estimatedDuration", 0),
                    "timestamp": e.args.get("timestamp", 0),
                    "chunk_count": e.args.get("chunkCount", 0),
                    "chunk_size_ms": e.args.get("chunkSizeMs", 0),
                    "chunked_transmission": False,
                }

                debug(
                    f"Traditional audio data received: {len(self.recording_data)} bytes"
                )

                if self._on_audio_ready:
                    self._on_audio_ready(self.recording_data)

            except Exception as ex:
                error_msg = f"Error processing traditional audio data: {str(ex)}"
                error(error_msg)
                if self._on_recording_error:
                    self._on_recording_error(error_msg)

        # Register event handlers
        self.on("chunked_transmission_start", handle_chunked_transmission_start)
        self.on("chunked_audio_data", handle_chunked_audio_data)
        self.on("chunked_transmission_complete", handle_chunked_transmission_complete)
        self.on("chunked_transmission_error", handle_chunked_transmission_error)
        self.on("audio_ready", handle_audio_ready)
        self.on("recording_started", handle_recording_started)
        self.on("recording_stopped", handle_recording_stopped)
        self.on("recording_paused", handle_recording_paused)
        self.on("recording_resumed", handle_recording_resumed)
        self.on("recording_error", handle_recording_error)
        self.on("permission_granted", handle_permission_granted)
        self.on("permission_denied", handle_permission_denied)

    async def _assemble_audio_chunk_simple(
        self,
        audio_chunk_index: int,
        total_transmission_chunks: int,
        original_chunk_size: int,
    ) -> None:
        """Assemble audio chunk with simple validation."""
        try:
            # Collect all transmission chunks for this audio chunk
            transmission_chunks = []
            for i in range(total_transmission_chunks):
                chunk_key = f"{audio_chunk_index}_{i}"
                if chunk_key in self._temp_transmission_chunks:
                    transmission_chunks.append(
                        self._temp_transmission_chunks[chunk_key]
                    )
                else:
                    error(f"Missing transmission chunk: {chunk_key}")
                    self._transmission_state.missing_chunks.add(audio_chunk_index)
                    self._transmission_state.errors.append(
                        f"Missing transmission chunk: {chunk_key}"
                    )
                    return

            # Combine transmission chunks
            combined_base64 = b"".join(transmission_chunks).decode("utf-8")

            # Decode to binary audio data
            audio_chunk_data = base64.b64decode(combined_base64)

            # 4. Simple validation - size check
            if original_chunk_size > 0 and len(audio_chunk_data) != original_chunk_size:
                warning(
                    f"Audio chunk size mismatch: expected {original_chunk_size}, got {len(audio_chunk_data)}"
                )

            # 1. Chunk sequencing - store in correct order
            self._transmission_state.received_chunks[audio_chunk_index] = (
                audio_chunk_data
            )
            self._transmission_state.chunk_sizes[audio_chunk_index] = len(
                audio_chunk_data
            )

            # Clean up transmission chunks to save memory
            for i in range(total_transmission_chunks):
                chunk_key = f"{audio_chunk_index}_{i}"
                if chunk_key in self._temp_transmission_chunks:
                    del self._temp_transmission_chunks[chunk_key]

            debug(f"Audio chunk {audio_chunk_index} assembled successfully")

        except Exception as e:
            log_exception(e, f"Error assembling audio chunk {audio_chunk_index}")
            self._transmission_state.errors.append(
                f"Assembly error for chunk {audio_chunk_index}: {str(e)}"
            )

    def _validate_completion(self) -> Dict[str, Any]:
        """Simple completion validation."""
        try:
            expected_count = self._transmission_state.total_expected_audio_chunks
            received_count = len(self._transmission_state.received_chunks)

            # 2. Completion detection
            is_complete = received_count == expected_count and expected_count > 0

            # 5. Clear error reporting
            missing_chunks = []
            if expected_count > 0:
                for i in range(expected_count):
                    if i not in self._transmission_state.received_chunks:
                        missing_chunks.append(i)
                        self._transmission_state.missing_chunks.add(i)

            return {
                "is_complete": is_complete,
                "expected_count": expected_count,
                "received_count": received_count,
                "missing_chunks": missing_chunks,
                "error": (
                    f"Missing {len(missing_chunks)} chunks" if missing_chunks else None
                ),
            }

        except Exception as e:
            log_exception(e, "Error validating completion")
            return {
                "is_complete": False,
                "error": f"Validation error: {str(e)}",
            }

    def _assemble_final_audio_data(self, e: events.GenericEventArguments) -> None:
        """Assemble final audio data with simple validation."""
        try:
            if not self._transmission_state.received_chunks:
                raise Exception("No audio chunks to assemble")

            debug(
                f"Assembling final audio from {len(self._transmission_state.received_chunks)} chunks..."
            )

            # 1. Chunk sequencing - sort chunks by index
            sorted_chunks = sorted(self._transmission_state.received_chunks.items())

            # Combine all audio chunk data
            audio_parts = []
            total_size = 0

            for chunk_index, chunk_data in sorted_chunks:
                audio_parts.append(chunk_data)
                total_size += len(chunk_data)

            # Combine all audio data
            self.recording_data = b"".join(audio_parts)

            # Calculate estimated duration
            estimated_duration = e.args.get("estimatedDuration", 0)

            # Set metadata
            self.recording_metadata = {
                "mime_type": self.js_config["audio"]["format"],
                "size": len(self.recording_data),
                "duration": estimated_duration,
                "timestamp": int(time.time() * 1000),
                "chunk_count": len(self._transmission_state.received_chunks),
                "chunk_size_ms": self.js_config["recording"]["chunk_size_ms"],
                "chunked_transmission": True,
                "total_bytes_received": self._transmission_state.total_bytes_received,
                "transmission_session_id": self._transmission_state.session_id,
                "simple_validation": {
                    "total_chunks": len(self._transmission_state.received_chunks),
                    "total_size": total_size,
                    "errors": len(self._transmission_state.errors),
                    "missing_chunks": len(self._transmission_state.missing_chunks),
                },
            }

            debug(
                f"Final audio assembled: {len(self.recording_data)} bytes, {estimated_duration}ms estimated duration"
            )

            # 4. Simple validation - final size check
            if total_size != len(self.recording_data):
                warning(
                    f"Final size validation warning: expected {total_size}, got {len(self.recording_data)}"
                )

        except Exception as e:
            log_exception(e, "Error assembling final audio data")
            raise

    # Recording methods with simplified error handling
    async def start_recording_async(self) -> Dict[str, Any]:
        """Start recording with simplified validation."""
        self.recording_data = b""
        self.recording_metadata = {}
        self.processing_aborted = False

        try:
            start_timeout = config.get("timeouts.recording.start", 10.0)
            result = await asyncio.wait_for(
                self.run_method("startRecording"), timeout=start_timeout
            )

            if not result.get("success"):
                error_msg = result.get("error", "Failed to start recording")
                return {"success": False, "error": error_msg}

            debug("Recording started successfully")
            return result

        except asyncio.TimeoutError:
            error_msg = f"Recording start timed out after {start_timeout} seconds"
            error(error_msg)
            return {"success": False, "error": error_msg}
        except Exception as e:
            log_exception(e, "Error in start_recording_async")
            return {"success": False, "error": f"Start recording failed: {str(e)}"}

    async def stop_recording_async(self) -> Dict[str, Any]:
        """Stop recording with simplified validation."""
        if self.is_destroyed:
            return {"success": False, "error": "Component has been destroyed"}

        self.processing_aborted = False

        try:
            stop_timeout = config.get("timeouts.recording.stop", 15.0)

            try:
                await asyncio.wait_for(
                    self.run_method("stopRecording"), timeout=stop_timeout
                )
            except asyncio.TimeoutError:
                debug(
                    f"stopRecording timed out after {stop_timeout}s - transmission should still complete"
                )
            except Exception as e:
                log_exception(e, "stopRecording error")
                debug(f"stopRecording error - transmission should still work: {e}")

            # Wait for transmission completion with timeout
            finalization_timeout = config.get("timeouts.recording.finalization", 300.0)
            poll_interval = 0.5
            elapsed_time = 0

            while elapsed_time < finalization_timeout:
                await asyncio.sleep(poll_interval)
                elapsed_time += poll_interval

                # Check if we have assembled audio data
                if self.recording_data and len(self.recording_data) > 0:
                    size_mb = len(self.recording_data) / (1024 * 1024)
                    debug(f"Chunked transmission completed: {size_mb:.2f}MB")

                    return {
                        "success": True,
                        "message": "Recording completed successfully",
                        "duration": self.get_recording_duration_ms(),
                        "size": len(self.recording_data),
                        "chunked_transmission": True,
                        "audio_chunks": len(self._transmission_state.received_chunks),
                        "transmission_session_id": self._transmission_state.session_id,
                    }

                # Progress logging
                if int(elapsed_time) % 10 == 0 and elapsed_time > 0:
                    if self._is_receiving_chunks:
                        debug(
                            f"Chunked transmission in progress: {len(self._transmission_state.received_chunks)} chunks - {elapsed_time}s elapsed"
                        )

            # Timeout handling
            warning(f"Transmission timed out after {finalization_timeout} seconds")

            if self.recording_data and len(self.recording_data) > 0:
                return {
                    "success": True,
                    "message": "Recording completed (partial timeout)",
                    "duration": self.get_recording_duration_ms(),
                    "size": len(self.recording_data),
                    "chunked_transmission": True,
                    "timeout": True,
                }

            return {
                "success": False,
                "error": f"Transmission timed out with no data after {finalization_timeout}s",
                "errors": self._transmission_state.errors,
                "missing_chunks": list(self._transmission_state.missing_chunks),
            }

        except Exception as e:
            log_exception(e, "Error in stop_recording_async")
            return {"success": False, "error": f"Stop recording failed: {str(e)}"}

    # Standard methods with simplified validation
    def validate_audio_data(self) -> Dict[str, Any]:
        """Validate audio data with simplified checks."""
        if self.is_destroyed:
            return {
                "valid": False,
                "error": "Component has been destroyed",
                "size": 0,
                "duration": 0,
            }

        if not self.has_recording():
            return {
                "valid": False,
                "error": "No audio data available",
                "size": 0,
                "duration": 0,
            }

        duration_ms = self.get_recording_duration_ms()
        size_bytes = self.get_recording_size()
        mime_type = self.recording_metadata.get("mime_type", "")

        # Format validation
        expected_format = self.recording_config["audio"]["format"]
        if not mime_type.startswith("audio/webm"):
            return {
                "valid": False,
                "error": f"Expected {expected_format} format, got {mime_type}",
                "size": size_bytes,
                "duration": duration_ms,
            }

        # Duration validation
        if duration_ms and duration_ms < self.min_duration_ms:
            if size_bytes < 1024 * 1024:  # Less than 1MB
                return {
                    "valid": False,
                    "error": f"Recording too short: {duration_ms}ms (minimum: {self.min_duration_ms}ms)",
                    "size": size_bytes,
                    "duration": duration_ms,
                }

        # Size validation
        if size_bytes < self.min_file_size_bytes:
            return {
                "valid": False,
                "error": f"Recording too small: {size_bytes} bytes (minimum: {self.min_file_size_bytes} bytes)",
                "size": size_bytes,
                "duration": duration_ms,
            }

        return {
            "valid": True,
            "error": None,
            "size": size_bytes,
            "duration": duration_ms,
            "mime_type": mime_type,
            "format": "WebM",
            "chunk_count": self.recording_metadata.get("chunk_count", 0),
            "chunked_transmission": self.recording_metadata.get(
                "chunked_transmission", False
            ),
            "simple_validation": self.recording_metadata.get("simple_validation", {}),
        }

    # Pause/Resume methods
    async def pause_recording_async(self) -> Dict[str, Any]:
        """Pause audio recording asynchronously."""
        result = await self._safe_run_method("pauseRecording", "pause_resume")
        if not result.get("success"):
            error(f"Pause recording failed: {result.get('error')}")
        return result

    async def resume_recording_async(self) -> Dict[str, Any]:
        """Resume audio recording asynchronously."""
        result = await self._safe_run_method("resumeRecording", "pause_resume")
        if not result.get("success"):
            error(f"Resume recording failed: {result.get('error')}")
        return result

    async def _safe_run_method(
        self, method_name: str, operation_type: str
    ) -> Dict[str, Any]:
        """Safely run a JavaScript method with timeout."""
        try:
            timeout = config.get(f"timeouts.recording.{operation_type}", 5.0)
            result = await asyncio.wait_for(
                self.run_method(method_name), timeout=timeout
            )
            return result
        except asyncio.TimeoutError:
            error_msg = f"{method_name} timed out after {timeout} seconds"
            error(error_msg)
            return {"success": False, "error": error_msg}
        except Exception as e:
            log_exception(e, "Error in _safe_run_method")
            return {"success": False, "error": f"Error in {method_name}: {str(e)}"}

    # Data access methods
    def get_recording_data(self) -> bytes:
        """Get the recorded audio data."""
        return self.recording_data

    def get_recording_metadata(self) -> Dict[str, Any]:
        """Get metadata about the recording."""
        return self.recording_metadata.copy()

    def get_recording_size(self) -> int:
        """Get the size of recorded audio data in bytes."""
        return len(self.recording_data)

    def has_recording(self) -> bool:
        """Check if there is recorded audio data."""
        return len(self.recording_data) > 0

    def get_recording_duration_ms(self) -> int:
        """Get recording duration in milliseconds."""
        return self.recording_metadata.get("duration", 0) or 0

    def clear_recording(self) -> None:
        """Clear the recorded audio data and state."""
        self.recording_data = b""
        self.recording_metadata = {}
        self.is_recording = False
        self.is_paused = False
        self.recording_session_active = False
        self.is_processing = False
        self.processing_aborted = False

        # Clear transmission state
        self._transmission_state = TransmissionState()
        self._is_receiving_chunks = False
        if hasattr(self, "_temp_transmission_chunks"):
            self._temp_transmission_chunks.clear()

    def destroy(self):
        """Mark component as destroyed and clear all state."""
        self.is_destroyed = True
        self.processing_aborted = True
        self.clear_recording()

    def __del__(self):
        """Cleanup when component is garbage collected."""
        if not self.is_destroyed:
            self.destroy()
