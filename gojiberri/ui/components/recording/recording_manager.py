# components/recording/recording_manager.py
"""
Recording Manager for Desktop Audio Recording Application

This module provides a comprehensive recording management system that handles
audio recording state, session management, caching, and error recovery for
desktop applications using NiceGUI and WebRTC audio recording.

Key Features:
- Dual-state management (internal vs UI state)
- Automatic session creation and management
- Real-time duration tracking with pause/resume support
- Persistent caching for recording recovery
- Comprehensive error handling with retry logic
- Permission management for microphone access

Dependencies:
- AudioRecorder: WebRTC-based audio recording component
- SessionManager: Unified session state management
- ConfigLoader: Application configuration management
- NiceGUI: UI framework for desktop applications
"""

from typing import Dict, Any, Optional
from nicegui import ui
from datetime import datetime
import time
import asyncio

from gojiberri.utils.logger import debug, error, warning, log_exception
from gojiberri.ui.config.config_loader import config
from .audio_recorder import AudioRecorder


class RecordingManager:
    """
    Manages audio recording lifecycle with robust state management and error recovery.

    This class serves as the central coordinator for audio recording operations,
    managing the complex interactions between UI state, recording hardware,
    session management, and data persistence. It implements a dual-state pattern
    where internal state is the source of truth, with UI state synchronized accordingly.

    Architecture:
    - Internal State: Authoritative recording state (is_recording, is_paused)
    - UI State: Visual representation synchronized with internal state
    - Session State: Backend session creation and management
    - Cache State: Persistent storage for recovery scenarios

    State Management Pattern:
    1. Operations modify internal state only after successful completion
    2. UI components are updated to reflect internal state
    3. Errors trigger comprehensive state reset to maintain consistency
    4. Cache provides recovery mechanism for interrupted sessions
    """

    def __init__(self, components: Dict[str, Any], duration_display: ui.label):
        """
        Initialize recording manager with UI components and configuration.

        Args:
            components: Dictionary containing UI components (patient_inputs, buttons, etc.)
            duration_display: NiceGUI label element for displaying recording duration

        Initializes:
            - Audio recording components and callbacks
            - State management properties
            - Error handling configuration
            - Caching system for session recovery
            - Timer management for duration tracking
        """
        # UI component references for form data extraction and updates
        self.components = components
        self.duration_display = duration_display

        # Recording timing and duration tracking
        self.start_time = None
        self.recording_paused_time = 0
        self.timer = None
        self.cache_timer = None

        # Dual-state management: internal state is source of truth
        self._internal_recording_state = False  # Authoritative recording state
        self._internal_paused_state = False  # Authoritative pause state
        self.recording_started = (
            False  # Historical flag - whether recording was ever initiated
        )

        # Session lifecycle management
        self.session_created = False
        self.session_id = None
        self.session_date = None
        self.session_time = None

        # Error handling configuration from application config
        self.last_error = None
        self.error_count = 0
        error_config = config.get_section("error_handling")
        self.max_retries = error_config.get("max_retries", 3)
        self.retry_delay = error_config.get("retry_delay", 1.0)

        # Microphone permission state tracking
        self.permission_status = "unknown"  # unknown, granted, denied
        self.permission_error_message = ""

        # Audio recording components and data storage
        self.audio_recorder = None  # AudioRecorder component instance
        self.audio_data = None  # Binary audio data
        self.recording_metadata = {}  # Audio metadata (duration, format, etc.)

        # Caching system configuration for session recovery
        cache_config = config.get_section("cache")
        self.cache_enabled = cache_config.get("enabled", True)
        self.cache_interval = config.get("timers.ui.cache_save", 30.0)

        # Initialize components and systems
        self._initialize_audio_recorder()
        self._load_cached_data()
        self._setup_caching()

    @property
    def is_recording(self) -> bool:
        """
        Get current recording state from internal state manager.

        This property ensures UI components always reflect the authoritative
        internal state rather than maintaining their own state tracking.

        Returns:
            bool: True if currently recording audio
        """
        return self._internal_recording_state

    @property
    def is_paused(self) -> bool:
        """
        Get current pause state from internal state manager.

        Returns:
            bool: True if recording is paused (not stopped, but temporarily halted)
        """
        return self._internal_paused_state

    def _initialize_audio_recorder(self):
        """
        Initialize AudioRecorder component with callback registration.

        Creates the WebRTC-based AudioRecorder component and registers callbacks
        for all audio recording lifecycle events. The component is hidden in the
        DOM as it provides programmatic audio recording without visual interface.

        Callbacks registered:
        - Audio data ready: Triggered when recording data is available
        - Recording state changes: Started, stopped, paused, resumed
        - Error handling: Recording errors and permission issues
        - Permission management: Microphone access granted/denied

        Error Handling:
        - Sets audio_recorder to None if initialization fails
        - Logs initialization errors for debugging
        - Application continues with limited functionality
        """
        try:
            self.audio_recorder = AudioRecorder(
                on_audio_ready=self._on_audio_ready,
                on_recording_started=self._on_recording_started,
                on_recording_stopped=self._on_recording_stopped,
                on_recording_paused=self._on_recording_paused,
                on_recording_resumed=self._on_recording_resumed,
                on_recording_error=self._on_recording_error,
                on_permission_granted=self._on_permission_granted,
                on_permission_denied=self._on_permission_denied,
            )

            # Hide component in DOM - it provides programmatic interface only
            with ui.element("div").style("display: none;"):
                self.audio_recorder

        except Exception as e:
            log_exception(e, "AudioRecorder initialization failed")
            error(f"Failed to initialize audio recorder: {e}")
            self.audio_recorder = None

    def _setup_caching(self):
        """
        Configure periodic caching system for session recovery.

        Creates a timer-based caching system that periodically saves recording
        state to enable recovery from unexpected interruptions. Caching only
        occurs during active recording sessions to minimize overhead.

        Cache Triggers:
        - Active recording or paused recording states
        - Configurable interval (default: 30 seconds)
        - Automatic deactivation when recording stops

        Cache Contents:
        - Recording state and timing information
        - Session metadata (ID, dates, times)
        - Audio metadata (format, duration, size)
        """
        if not self.cache_enabled:
            return

        def cache_data():
            """Timer callback for periodic cache operations."""
            try:
                if self.recording_started and (self.is_recording or self.is_paused):
                    self._save_cache()
            except Exception as e:
                log_exception(e, "Cache timer execution failed")
                error(f"Error in cache timer: {e}")

        self.cache_timer = ui.timer(self.cache_interval, cache_data)
        self.cache_timer.deactivate()  # Activated only during recording

    def _save_cache(self):
        """
        Save current recording state to cache for recovery purposes.

        Captures comprehensive recording state including timing, session info,
        and audio metadata. Cache data enables recovery from unexpected
        application interruptions or browser refreshes.

        Cached Data Structure:
        - audio_metadata: Format, duration, size information
        - recording_state: Current state flags and timing
        - session_info: Session ID and date/time information
        - timestamp: Cache creation time for expiration management
        """
        try:
            cache_data = {
                "audio_metadata": self.recording_metadata,
                "recording_state": {
                    "is_recording": self.is_recording,
                    "is_paused": self.is_paused,
                    "recording_started": self.recording_started,
                    "session_created": self.session_created,
                    "start_time": self.start_time,
                    "recording_paused_time": self.recording_paused_time,
                },
                "session_info": {
                    "session_id": self.session_id,
                    "session_date": self.session_date,
                    "session_time": self.session_time,
                },
                "timestamp": datetime.now().isoformat(),
            }

            self._cached_data = cache_data

        except Exception as e:
            log_exception(e, "Cache save operation failed")
            error(f"Error caching recording data: {e}")

    def _load_cached_data(self):
        """
        Load cached recording data for session recovery.

        Attempts to restore recording session from cached data if available
        and within expiration window. Enables seamless recovery from
        application restarts or unexpected interruptions.

        Recovery Process:
        1. Check for cached data existence
        2. Validate cache timestamp against expiration policy
        3. Restore session information and recording state
        4. Skip audio data restoration (requires new recording)

        Cache Expiration:
        - Configurable expiration time (default: 1 hour)
        - Expired cache data is ignored
        - No automatic cleanup (relies on natural cache replacement)
        """
        try:
            if hasattr(self, "_cached_data"):
                cached = self._cached_data
                cache_time = datetime.fromisoformat(cached["timestamp"])
                time_diff = datetime.now() - cache_time

                cache_expiration = config.get("cache.expiration", 3600)
                if time_diff.total_seconds() < cache_expiration:
                    # Restore session information
                    session_info = cached.get("session_info", {})
                    self.session_id = session_info.get("session_id")
                    self.session_date = session_info.get("session_date")
                    self.session_time = session_info.get("session_time")

                    # Restore recording state flags
                    state = cached["recording_state"]
                    self.recording_started = state.get("recording_started", False)
                    self.session_created = state.get("session_created", False)

        except Exception as e:
            log_exception(e, "Cache load operation failed")
            warning(f"No cached data to load: {e}")

    # Audio Recorder Event Callbacks

    def _on_audio_ready(self, audio_data: bytes):
        """
        Handle audio data availability from recording component.

        Called when the AudioRecorder component has captured and processed
        audio data. Validates audio format and stores data with metadata
        for subsequent processing or submission.

        Args:
            audio_data: Binary audio data in WebM format

        Operations:
        - Store audio data and metadata
        - Validate audio format against configuration
        - Trigger cache save if caching enabled
        - Log audio data reception for debugging
        """
        try:
            self.audio_data = audio_data
            self.recording_metadata = self.audio_recorder.get_recording_metadata()

            # Validate audio format against configuration
            expected_format = config.get("recording.audio.format", "audio/webm")
            mime_type = self.recording_metadata.get("mime_type", "")
            if mime_type.startswith("audio/webm"):
                debug(f"Audio ready: {len(audio_data)} bytes, format: {mime_type}")
            else:
                error(
                    f"Unexpected audio format: {mime_type} (expected: {expected_format})"
                )

            if self.cache_enabled:
                self._save_cache()

        except Exception as e:
            log_exception(e, "Audio ready callback failed")
            error(f"Error in _on_audio_ready: {e}")

    def _on_recording_started(self):
        """
        Handle recording start event from AudioRecorder component.

        Activates caching timer to begin periodic state persistence
        during recording session. Called when recording actually begins
        (after permission grants and hardware initialization).
        """
        try:
            if self.cache_timer and self.cache_enabled:
                self.cache_timer.activate()
        except Exception as e:
            log_exception(e, "Recording started callback failed")
            error(f"Error in _on_recording_started: {e}")

    def _on_recording_stopped(self, metadata: Dict[str, Any]):
        """
        Handle recording stop event from AudioRecorder component.

        Deactivates caching timer as recording session has ended.
        Metadata contains final recording information like total duration.

        Args:
            metadata: Recording metadata from AudioRecorder component
        """
        try:
            if self.cache_timer:
                self.cache_timer.deactivate()
        except Exception as e:
            log_exception(e, "Recording stopped callback failed")
            error(f"Error in _on_recording_stopped: {e}")

    def _on_recording_paused(self):
        """Handle recording pause event from AudioRecorder component."""
        try:
            debug("Recording paused")
        except Exception as e:
            log_exception(e, "Recording paused callback failed")
            error(f"Error in _on_recording_paused: {e}")

    def _on_recording_resumed(self):
        """Handle recording resume event from AudioRecorder component."""
        try:
            debug("Recording resumed")
        except Exception as e:
            log_exception(e, "Recording resumed callback failed")
            error(f"Error in _on_recording_resumed: {e}")

    def _on_recording_error(self, error_msg: str):
        """
        Handle recording errors from AudioRecorder component.

        Implements comprehensive error recovery by resetting internal state
        and tracking error count for potential retry logic.

        Args:
            error_msg: Error description from AudioRecorder component

        Error Recovery:
        - Reset internal recording state to prevent inconsistency
        - Track error count for retry logic
        - Log error for debugging and monitoring
        """
        try:
            self.last_error = error_msg
            self.error_count += 1
            self._reset_recording_state_on_error()
            error(f"Recording error: {error_msg}")
        except Exception as e:
            log_exception(e, "Recording error callback failed")
            error(f"Error in _on_recording_error: {e}")

    def _on_permission_granted(self):
        """
        Handle microphone permission granted event.

        Updates permission tracking state and clears any previous
        permission error messages. Enables recording functionality.
        """
        try:
            self.permission_status = "granted"
            self.permission_error_message = ""
        except Exception as e:
            log_exception(e, "Permission granted callback failed")
            error(f"Error in _on_permission_granted: {e}")

    def _on_permission_denied(self, error_msg: str):
        """
        Handle microphone permission denied event.

        Args:
            error_msg: Permission denial reason from browser/system

        Updates permission tracking and stores error message for
        user feedback. Prevents recording attempts until resolved.
        """
        try:
            self.permission_status = "denied"
            self.permission_error_message = error_msg
            error(f"Microphone permission denied: {error_msg}")
        except Exception as e:
            log_exception(e, "Permission denied callback failed")
            error(f"Error in _on_permission_denied: {e}")

    def update_duration(self):
        """
        Update recording duration display with current elapsed time.

        Calculates total elapsed recording time accounting for pause periods
        and updates the UI duration display. Called by timer at regular intervals.

        Duration Calculation:
        - Current time minus start time
        - Subtract total paused time
        - Format as HH:MM:SS for display

        UI Update:
        - Updates duration_display label text
        - Forces UI refresh to ensure visibility
        """
        try:
            if not self.start_time:
                return

            current_time = time.time()
            elapsed = (current_time - self.start_time) - self.recording_paused_time

            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

            if self.duration_display:
                self.duration_display.text = f"Duration: {duration_str}"

            ui.update()
        except Exception as e:
            log_exception(e, "Duration update failed")
            error(f"Error updating duration: {e}")

    async def toggle_recording(self, target_recording_state):
        """
        Toggle recording state between recording and paused with comprehensive error handling.

        This method implements the core recording state management logic with
        deferred state setting - internal state is only updated after successful
        operations to maintain consistency.

        Args:
            target_recording_state (bool): Desired recording state (True=recording, False=paused)

        Returns:
            tuple: (success: bool, message: str, session_id: Optional[str])

        State Management Pattern:
        1. Validate preconditions (permissions, recorder availability)
        2. Attempt recording operation with retry logic
        3. Update internal state only after successful operation
        4. Create session automatically when recording starts
        5. Reset state on errors to maintain consistency

        Error Handling:
        - Retry logic with configurable attempts and delays
        - Comprehensive state reset on failures
        - Permission validation before operations
        - Timeout handling for long operations
        """
        if not self.audio_recorder:
            return False, "Audio recorder not available", None

        if getattr(self.audio_recorder, "is_destroyed", False):
            return False, "Audio recorder destroyed", None

        try:
            # Validate microphone permissions before attempting recording
            if target_recording_state and self.permission_status == "denied":
                return False, f"Cannot record: {self.permission_error_message}", None

            debug(
                f"Attempting to {'start' if target_recording_state else 'pause'} recording"
            )

            # Retry logic for recording operations
            for attempt in range(self.max_retries):
                try:
                    result = await self._handle_recording_operation(
                        target_recording_state
                    )
                    if result[0]:  # Operation successful
                        # Update internal state after successful operation
                        self._internal_recording_state = target_recording_state
                        self._internal_paused_state = (
                            not target_recording_state
                            if self.recording_started
                            else False
                        )

                        debug(
                            f"Recording state successfully set to: recording={self.is_recording}, paused={self.is_paused}"
                        )

                        # Automatic session creation when recording starts
                        if (
                            target_recording_state
                            and not self.session_created
                            and self.recording_started
                        ):
                            session_id = await self._create_session_silently()
                            if session_id:
                                self.session_created = True
                                debug(f"Session created silently: {session_id}")
                            else:
                                # Rollback recording if session creation fails
                                await self._handle_recording_operation(False)
                                self._reset_recording_state_on_error()
                                return False, "Failed to create recording session", None
                        break
                    elif attempt == self.max_retries - 1:
                        # Final attempt failed - reset state
                        self._reset_recording_state_on_error()
                        return result
                except asyncio.TimeoutError:
                    if attempt == self.max_retries - 1:
                        self._reset_recording_state_on_error()
                        return (
                            False,
                            "Recording operation timed out. Please try again.",
                            self.session_id,
                        )
                    await asyncio.sleep(self.retry_delay)
                except Exception as e:
                    log_exception(
                        e, f"Recording operation attempt {attempt + 1} failed"
                    )
                    error(
                        f"Recording operation failed (attempt {attempt + 1}/{self.max_retries}): {str(e)}"
                    )
                    if attempt == self.max_retries - 1:
                        self._reset_recording_state_on_error()
                        return (
                            False,
                            f"Recording operation failed: {str(e)}",
                            self.session_id,
                        )
                    await asyncio.sleep(self.retry_delay)

            # Update cache after successful state change
            if self.cache_enabled:
                self._save_cache()

            # Return appropriate success message
            if target_recording_state and self.recording_started:
                action = "started" if self.start_time == time.time() else "resumed"
                return True, f"Recording {action} successfully", self.session_id
            elif not target_recording_state:
                return True, "Recording paused", self.session_id
            else:
                return True, "", self.session_id

        except Exception as e:
            log_exception(e, "Critical error in toggle_recording")
            self._reset_recording_state_on_error()
            return False, f"Critical recording error: {str(e)}", self.session_id

    def _reset_recording_state_on_error(self):
        """
        Reset recording state to consistent initial state after errors.

        Provides comprehensive state reset to maintain UI and internal state
        consistency when errors occur. Preserves historical flags (recording_started,
        session_created) while resetting active state variables.

        Reset Operations:
        - Clear internal recording and pause states
        - Reset timing information for fresh sessions
        - Preserve session history flags for UI context
        - Log state reset for debugging
        """
        try:
            debug("Resetting recording state due to error")

            # Reset active state variables
            self._internal_recording_state = False
            self._internal_paused_state = False

            # Reset timing state only if recording never actually started
            if not self.recording_started:
                self.start_time = None
                self.recording_paused_time = 0

            debug(
                f"State reset complete: recording={self.is_recording}, paused={self.is_paused}"
            )

        except Exception as e:
            log_exception(e, "State reset operation failed")
            error(f"Failed to reset recording state: {e}")

    async def _create_session_silently(self) -> Optional[str]:
        """
        Create recording session without triggering UI refresh callbacks.

        Integrates with the unified session manager to create a new session
        using patient information from UI form components. Session creation
        is performed silently (callbacks suppressed) to avoid UI refresh
        during recording operations.

        Returns:
            Optional[str]: Session ID if creation successful, None otherwise

        Patient Data Extraction:
        - Retrieves patient name and date of birth from UI components
        - Handles missing or invalid form data gracefully
        - Creates session with extracted patient information

        Session Manager Integration:
        - Temporarily suppresses callbacks to prevent UI refresh
        - Uses unified session manager for consistent session handling
        - Restores callback functionality after operation
        """
        try:
            # Extract patient information from UI form components
            patient_name = ""
            patient_dob = ""

            try:
                patient_name = self.components["patient_inputs"]["name"].value
                patient_dob = self.components["patient_inputs"]["dob"].value
            except (KeyError, AttributeError) as e:
                error(f"Error extracting patient info: {e}")

            from gojiberri.ui.utils.session_state import session_manager

            # Suppress callbacks during silent session creation
            session_manager._suppress_callbacks = True
            try:
                session_data = {
                    "patient_name": patient_name,
                    "patient_dob": patient_dob,
                }

                session_id = await session_manager.create_new_session(session_data)

                if session_id:
                    self.session_id = session_id
                    debug(f"Session created silently: {session_id}")
                    return session_id

            finally:
                # Always restore callback functionality
                session_manager._suppress_callbacks = False

            return None

        except Exception as e:
            log_exception(e, "Silent session creation failed")
            error(f"Error in _create_session_silently: {e}")
            return None

    async def _handle_recording_operation(self, target_recording_state):
        """
        Execute the actual recording operation (start/pause/resume) with the AudioRecorder.

        Args:
            target_recording_state (bool): True to start/resume recording, False to pause

        Returns:
            tuple: (success: bool, message: str, session_id: Optional[str])

        Recording Start Logic:
        - Initialize timing on first recording start
        - Stop any existing recording before starting new one
        - Set recording_started flag immediately on successful start

        Recording Resume Logic:
        - Calculate and accumulate pause time
        - Resume existing recording session

        Recording Pause Logic:
        - Record pause start time for duration calculation
        - Pause current recording session
        """
        try:
            if target_recording_state:
                if self.start_time is None:
                    # First time starting recording
                    self.start_time = time.time()
                    self.recording_paused_time = 0

                    # Ensure clean state by stopping any existing recording
                    if (
                        self.audio_recorder.is_recording
                        or self.audio_recorder.is_paused
                    ):
                        await self.audio_recorder.stop_recording_async()
                        await asyncio.sleep(0.5)  # Brief delay for state cleanup

                    result = await self.audio_recorder.start_recording_async()

                    if not result.get("success"):
                        self.start_time = None
                        return (
                            False,
                            f"Failed to start recording: {result.get('error')}",
                            self.session_id,
                        )

                    # Set historical flag immediately on successful start
                    self.recording_started = True
                    debug("Recording started flag set directly in main flow")

                else:
                    # Resuming existing recording
                    if hasattr(self, "pause_time"):
                        self.recording_paused_time += time.time() - self.pause_time
                        delattr(self, "pause_time")

                    result = await self.audio_recorder.resume_recording_async()

                    if not result.get("success"):
                        return (
                            False,
                            f"Failed to resume recording: {result.get('error')}",
                            self.session_id,
                        )

            else:
                # Pausing recording
                if self.start_time is not None:
                    self.pause_time = time.time()

                    result = await self.audio_recorder.pause_recording_async()

                    if not result.get("success"):
                        return (
                            False,
                            f"Failed to pause recording: {result.get('error')}",
                            self.session_id,
                        )

            return True, "Operation successful", self.session_id

        except Exception as e:
            log_exception(e, "Recording operation failed")
            error(f"Exception in _handle_recording_operation: {e}")
            return False, f"Operation failed: {str(e)}", self.session_id

    async def finalize_recording_async(self):
        """
        Finalize recording session and prepare audio data for submission.

        Performs final recording operations including stopping the recording,
        validating audio data, and transferring data from the AudioRecorder
        component to the manager for processing.

        Operations:
        1. Stop active recording in AudioRecorder component
        2. Validate recorded audio data quality and format
        3. Transfer audio data and metadata to manager
        4. Stop all active timers (duration, cache)
        5. Update internal state to reflect completion

        Error Handling:
        - Validates recorder availability and state
        - Handles validation warnings without failing
        - Ensures timer cleanup even on errors
        - Updates internal state consistently

        Raises:
            Exception: If audio recorder unavailable or recording stop fails
        """
        if not self.audio_recorder:
            raise Exception("Audio recorder not available")

        if getattr(self.audio_recorder, "is_destroyed", False):
            raise Exception("Audio recorder component has been destroyed")

        try:
            # Stop recording in AudioRecorder component
            result = await self.audio_recorder.stop_recording_async()

            if not result.get("success"):
                raise Exception(f"Failed to stop recording: {result.get('error')}")

            # Validate audio data quality
            validation = self.audio_recorder.validate_audio_data()
            if not validation["valid"]:
                warning(f"Audio validation warning: {validation['error']}")

            # Transfer audio data from recorder to manager
            if self.audio_recorder.has_recording():
                self.audio_data = self.audio_recorder.get_recording_data()
                self.recording_metadata = self.audio_recorder.get_recording_metadata()
                debug(f"Audio finalized: {len(self.audio_data)} bytes")
            else:
                warning("Warning: No audio data available after finalization")

        except Exception as e:
            log_exception(e, "Recording finalization failed")
            error(f"Error in finalize_recording_async: {e}")
            raise

        # Cleanup timers and update state
        try:
            if self.timer:
                self.timer.deactivate()
                self.timer = None
            if self.cache_timer:
                self.cache_timer.deactivate()
        except Exception as e:
            log_exception(e, "Timer cleanup failed")
            error(f"Error stopping timers: {e}")

        # Update internal state after successful finalization
        self._internal_recording_state = False
        self._internal_paused_state = False

        if self.cache_enabled:
            self._save_cache()

    # Utility and Data Access Methods

    def has_active_session(self) -> bool:
        """
        Check if there is an active recording session.

        Checks both local session state and unified session manager
        to determine if a recording session is currently active.

        Returns:
            bool: True if active session exists
        """
        if self.session_created and self.session_id:
            return True

        from gojiberri.ui.utils.session_state import session_manager

        return session_manager.has_active_session()

    def get_audio_data(self) -> Optional[bytes]:
        """
        Retrieve recorded audio data with fallback logic.

        Attempts to retrieve audio data from AudioRecorder component first,
        then falls back to locally stored data. Handles component destruction
        and missing data scenarios gracefully.

        Returns:
            Optional[bytes]: Binary audio data if available, None otherwise
        """
        try:
            # Try to get data from active recorder component
            if self.audio_recorder and not getattr(
                self.audio_recorder, "is_destroyed", False
            ):
                recorder_data = self.audio_recorder.get_recording_data()
                if recorder_data and len(recorder_data) > 0:
                    return recorder_data

            # Fallback to locally stored data
            return self.audio_data
        except Exception as e:
            log_exception(e, "Audio data retrieval failed")
            error(f"Error getting audio data: {e}")
            return None

    def get_audio_metadata(self) -> Dict[str, Any]:
        """
        Retrieve audio recording metadata with fallback logic.

        Returns metadata from AudioRecorder component if available,
        otherwise returns locally stored metadata.

        Returns:
            Dict[str, Any]: Audio metadata (duration, format, size, etc.)
        """
        try:
            if self.audio_recorder and not getattr(
                self.audio_recorder, "is_destroyed", False
            ):
                return self.audio_recorder.get_recording_metadata()
            return self.recording_metadata
        except Exception as e:
            log_exception(e, "Audio metadata retrieval failed")
            error(f"Error getting audio metadata: {e}")
            return {}

    def has_audio_data(self) -> bool:
        """
        Check if audio data is available from any source.

        Checks both AudioRecorder component and local storage
        to determine if recorded audio data is available.

        Returns:
            bool: True if audio data exists
        """
        try:
            # Check AudioRecorder component first
            if self.audio_recorder and not getattr(
                self.audio_recorder, "is_destroyed", False
            ):
                if self.audio_recorder.has_recording():
                    return True

            # Check local storage
            return self.audio_data is not None and len(self.audio_data) > 0
        except Exception as e:
            log_exception(e, "Audio data check failed")
            error(f"Error checking audio data: {e}")
            return False

    def get_audio_size(self) -> int:
        """
        Get the size of recorded audio data in bytes.

        Returns:
            int: Audio data size in bytes, 0 if no data available
        """
        try:
            if self.audio_recorder and not getattr(
                self.audio_recorder, "is_destroyed", False
            ):
                size = self.audio_recorder.get_recording_size()
                if size > 0:
                    return size
            return len(self.audio_data) if self.audio_data else 0
        except Exception as e:
            log_exception(e, "Audio size retrieval failed")
            error(f"Error getting audio size: {e}")
            return 0

    def validate_audio_data(self) -> Dict[str, Any]:
        """
        Validate recorded audio data availability and quality.

        Performs comprehensive validation of recorded audio data including
        format validation, size checks, and quality assessment.

        Returns:
            Dict[str, Any]: Validation result with 'valid' flag and error details

        Validation Checks:
        - Audio data availability from recorder or local storage
        - Audio format compatibility
        - Minimum duration and file size requirements
        - Metadata consistency
        """
        try:
            # Try validation from AudioRecorder component first
            if self.audio_recorder and not getattr(
                self.audio_recorder, "is_destroyed", False
            ):
                validation = self.audio_recorder.validate_audio_data()
                if validation.get("valid", False):
                    return validation

            # Fallback validation using local data
            if not self.has_audio_data():
                return {"valid": False, "error": "No audio data available"}

            return {
                "valid": True,
                "size": self.get_audio_size(),
                "duration": self.recording_metadata.get("duration", 0),
                "format": config.get("recording.audio.format", "audio/webm"),
            }
        except Exception as e:
            log_exception(e, "Audio data validation failed")
            error(f"Error validating audio data: {e}")
            return {"valid": False, "error": f"Validation error: {str(e)}"}

    async def ensure_audio_data_available(self) -> bool:
        """
        Ensure audio data is available for processing, attempting recovery if needed.

        Performs comprehensive checks and recovery attempts to ensure audio data
        is available for submission or processing. Attempts multiple recovery
        strategies before declaring failure.

        Returns:
            bool: True if audio data is available, False otherwise

        Recovery Strategies:
        1. Check existing audio data availability
        2. Attempt to retrieve data from AudioRecorder component
        3. Trigger recording finalization if recording was started
        4. Validate data availability after recovery attempts
        """
        debug(f"Has Audio Data: {self.has_audio_data()}")
        try:
            # Check if audio data already available
            if self.has_audio_data():
                return True

            debug(f"Recording started: {self.recording_started}")

            # Attempt recovery from AudioRecorder component
            if self.recording_started and self.audio_recorder:
                if hasattr(self.audio_recorder, "get_recording_data"):
                    recorder_data = self.audio_recorder.get_recording_data()
                    if recorder_data and len(recorder_data) > 0:
                        self.audio_data = recorder_data
                        self.recording_metadata = (
                            self.audio_recorder.get_recording_metadata()
                        )
                        debug(
                            f"Retrieved audio data from recorder: {len(self.audio_data)} bytes"
                        )
                        return True

                # Attempt finalization if direct retrieval failed
                try:
                    await self.finalize_recording_async()
                    if self.has_audio_data():
                        return True
                except Exception as e:
                    log_exception(e, "Recording finalization during recovery failed")
                    error(f"Finalization failed: {e}")

            return False

        except Exception as e:
            log_exception(e, "Audio data availability check failed")
            error(f"Error ensuring audio data availability: {e}")
            return False


def create_recording_manager(
    components: Dict[str, Any], duration_display: ui.label
) -> RecordingManager:
    """
    Factory function to create a RecordingManager instance.

    Args:
        components: Dictionary containing UI components for recording interface
        duration_display: NiceGUI label element for displaying recording duration

    Returns:
        RecordingManager: Configured recording manager instance

    This factory function provides a clean interface for creating RecordingManager
    instances with proper dependency injection and configuration setup.
    """
    return RecordingManager(components, duration_display)
