"""
Tests for ui_updates module - Simplified UI updates with error message handling
"""

import pytest  # type: ignore
from unittest.mock import Mock, patch

# Import functions to test
from gojiberri.ui.components.recording.session_card.ui_updates import (
    update_ui_after_toggle,
    _handle_successful_toggle,
    _handle_failed_toggle,
    _reset_button_state,
)


@pytest.fixture
def mock_components():
    """Create mock components dictionary for testing."""
    status_display = Mock()
    status_display.text = ""
    status_display.classes = Mock()

    record_button = Mock()
    record_button.toggled = False

    return {
        "status_display": status_display,
        "record_button": record_button,
        "duration_display": Mock(),
    }


@pytest.fixture
def mock_recording_manager():
    """Create mock recording manager for testing."""
    manager = Mock()
    manager.is_recording = False
    return manager


@pytest.fixture
def mock_log_error():
    """Mock the log_error function."""
    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates.log_error"
    ) as mock_error:
        yield mock_error


@pytest.fixture
def mock_manage_timer_state():
    """Mock the manage_timer_state function."""
    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates.manage_timer_state"
    ) as mock_timer:
        yield mock_timer


def test_update_ui_after_toggle_success_case(
    mock_components, mock_recording_manager, mock_manage_timer_state
):
    """Test update_ui_after_toggle with successful result."""
    # Arrange
    result = (True, "Recording started", "session_123")
    is_recording = True

    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates._handle_successful_toggle"
    ) as mock_success:
        # Act
        update_ui_after_toggle(
            result, mock_components, mock_recording_manager, is_recording
        )

        # Assert
        mock_success.assert_called_once_with(mock_components, mock_recording_manager)


def test_update_ui_after_toggle_failure_case(
    mock_components, mock_recording_manager, mock_log_error
):
    """Test update_ui_after_toggle with failed result."""
    # Arrange
    result = (False, "Failed to start recording", None)
    is_recording = True

    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates._handle_failed_toggle"
    ) as mock_failure:
        # Act
        update_ui_after_toggle(
            result, mock_components, mock_recording_manager, is_recording
        )

        # Assert
        mock_failure.assert_called_once_with(
            "Failed to start recording", mock_components, is_recording
        )


def test_update_ui_after_toggle_unpacks_result_tuple(
    mock_components, mock_recording_manager
):
    """Test that update_ui_after_toggle correctly unpacks result tuple."""
    # Arrange
    result = (True, "Success message", "session_456")
    is_recording = False

    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates._handle_successful_toggle"
    ) as mock_success:
        # Act
        update_ui_after_toggle(
            result, mock_components, mock_recording_manager, is_recording
        )

        # Assert
        # Verify that the function was called (indicating successful unpacking)
        mock_success.assert_called_once()


def test_handle_successful_toggle_manages_timer_state(
    mock_components, mock_recording_manager, mock_manage_timer_state
):
    """Test that successful toggle manages timer state."""
    # Act
    _handle_successful_toggle(mock_components, mock_recording_manager)

    # Assert
    mock_manage_timer_state.assert_called_once_with(mock_recording_manager)


def test_handle_successful_toggle_no_status_display(
    mock_recording_manager, mock_manage_timer_state
):
    """Test successful toggle when status_display is missing."""
    # Arrange
    components = {}  # No status_display

    # Act
    _handle_successful_toggle(components, mock_recording_manager)

    # Assert
    mock_manage_timer_state.assert_called_once_with(mock_recording_manager)


def test_handle_failed_toggle_logs_error(mock_components, mock_log_error):
    """Test that failed toggle logs error message."""
    # Arrange
    error_message = "Recording device not found"
    is_recording = True

    # Act
    _handle_failed_toggle(error_message, mock_components, is_recording)

    # Assert
    mock_log_error.assert_called_once_with(
        "Recording toggle failed: Recording device not found"
    )


def test_handle_failed_toggle_updates_status_display(mock_components, mock_log_error):
    """Test that failed toggle updates status display with error message."""
    # Arrange
    error_message = "Permission denied"
    is_recording = True
    status_display = mock_components["status_display"]

    # Act
    _handle_failed_toggle(error_message, mock_components, is_recording)

    # Assert
    assert status_display.text == error_message
    status_display.classes.assert_called_once_with(
        remove="text-green-500", add="text-red-500"
    )


def test_handle_failed_toggle_resets_button_state(mock_components, mock_log_error):
    """Test that failed toggle resets button state."""
    # Arrange
    error_message = "Network error"
    is_recording = True

    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates._reset_button_state"
    ) as mock_reset:
        # Act
        _handle_failed_toggle(error_message, mock_components, is_recording)

        # Assert
        mock_reset.assert_called_once_with(mock_components, is_recording)


def test_handle_failed_toggle_no_status_display(mock_log_error):
    """Test failed toggle when status_display is missing."""
    # Arrange
    components = {}  # No status_display
    error_message = "Some error"
    is_recording = False

    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates._reset_button_state"
    ) as mock_reset:
        # Act
        _handle_failed_toggle(error_message, components, is_recording)

        # Assert
        mock_log_error.assert_called_once()
        mock_reset.assert_called_once_with(components, is_recording)


def test_reset_button_state_toggles_button_when_recording_true(mock_components):
    """Test button reset when original is_recording was True."""
    # Arrange
    button = mock_components["record_button"]
    button.toggled = True
    is_recording = True

    # Act
    _reset_button_state(mock_components, is_recording)

    # Assert
    assert button.toggled is False  # Should be opposite of is_recording


def test_reset_button_state_toggles_button_when_recording_false(mock_components):
    """Test button reset when original is_recording was False."""
    # Arrange
    button = mock_components["record_button"]
    button.toggled = False
    is_recording = False

    # Act
    _reset_button_state(mock_components, is_recording)

    # Assert
    assert button.toggled is True  # Should be opposite of is_recording


def test_reset_button_state_no_button():
    """Test button reset when record_button is missing."""
    # Arrange
    components = {}  # No record_button
    is_recording = True

    # Act
    _reset_button_state(components, is_recording)

    # Assert - should not raise any exceptions


def test_reset_button_state_button_without_toggled_attribute():
    """Test button reset when button has no toggled attribute."""
    # Arrange
    button = Mock(spec=[])  # Mock without toggled attribute
    components = {"record_button": button}
    is_recording = True

    # Act
    _reset_button_state(components, is_recording)

    # Assert - should not raise any exceptions
    # Button should not be modified since it doesn't have toggled attribute


def test_handle_successful_toggle_status_display_none():
    """Test successful toggle when status_display is None."""
    # Arrange
    components = {"status_display": None}
    recording_manager = Mock()

    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates.manage_timer_state"
    ) as mock_timer:
        # Act
        _handle_successful_toggle(components, recording_manager)

        # Assert
        mock_timer.assert_called_once_with(recording_manager)


def test_handle_failed_toggle_status_display_none():
    """Test failed toggle when status_display is None."""
    # Arrange
    components = {"status_display": None}
    error_message = "Test error"
    is_recording = True

    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates.log_error"
    ) as mock_log:
        with patch(
            "gojiberri.ui.components.recording.session_card.ui_updates._reset_button_state"
        ) as mock_reset:
            # Act
            _handle_failed_toggle(error_message, components, is_recording)

            # Assert
            mock_log.assert_called_once()
            mock_reset.assert_called_once()


def test_update_ui_after_toggle_integration_failure():
    """Test complete integration flow for failed toggle."""
    # Arrange
    result = (False, "Device busy", None)
    components = {"status_display": Mock(), "record_button": Mock()}
    components["record_button"].toggled = True

    recording_manager = Mock()
    is_recording = True

    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates.log_error"
    ) as mock_log:
        # Act
        update_ui_after_toggle(result, components, recording_manager, is_recording)

        # Assert
        mock_log.assert_called_once_with("Recording toggle failed: Device busy")
        assert components["status_display"].text == "Device busy"
        components["status_display"].classes.assert_called_with(
            remove="text-green-500", add="text-red-500"
        )
        assert components["record_button"].toggled is False


def test_error_message_formatting():
    """Test that error message is formatted correctly in log."""
    # Arrange
    original_message = "Connection timeout"
    expected_log_message = "Recording toggle failed: Connection timeout"
    components = {"status_display": Mock()}

    with patch(
        "gojiberri.ui.components.recording.session_card.ui_updates.log_error"
    ) as mock_log:
        with patch(
            "gojiberri.ui.components.recording.session_card.ui_updates._reset_button_state"
        ):
            # Act
            _handle_failed_toggle(original_message, components, True)

            # Assert
            mock_log.assert_called_once_with(expected_log_message)


def test_button_toggled_attribute_check():
    """Test that button toggled attribute is checked before modification."""
    # Test with button that has toggled attribute
    button_with_toggled = Mock()
    button_with_toggled.toggled = True
    components1 = {"record_button": button_with_toggled}

    _reset_button_state(components1, True)
    assert button_with_toggled.toggled is False

    # Test with button that doesn't have toggled attribute
    button_without_toggled = Mock(spec=[])  # No toggled attribute
    components2 = {"record_button": button_without_toggled}

    # Should not raise exception
    _reset_button_state(components2, True)
