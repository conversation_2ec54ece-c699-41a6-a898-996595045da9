"""
Tests for utilities module - Helper functions for session card operations
"""

import pytest  # type: ignore
from unittest.mock import Mock

# Import functions to test
from gojiberri.ui.components.recording.session_card.utils import (
    extract_patient_info,
)


@pytest.fixture
def valid_components():
    """Create valid components dictionary with patient inputs."""
    name_input = Mock()
    name_input.value = "<PERSON> Doe"

    dob_input = Mock()
    dob_input.value = "1990-01-15"

    return {
        "patient_inputs": {"name": name_input, "dob": dob_input},
        "other_component": Mock(),
    }


@pytest.fixture
def empty_value_components():
    """Create components with empty values."""
    name_input = Mock()
    name_input.value = ""

    dob_input = Mock()
    dob_input.value = ""

    return {"patient_inputs": {"name": name_input, "dob": dob_input}}


@pytest.fixture
def none_value_components():
    """Create components with None values."""
    name_input = Mock()
    name_input.value = None

    dob_input = Mock()
    dob_input.value = None

    return {"patient_inputs": {"name": name_input, "dob": dob_input}}


def test_extract_patient_info_valid_components(valid_components):
    """Test extracting patient info from valid components."""
    # Act
    name, dob = extract_patient_info(valid_components)

    # Assert
    assert name == "John Doe"
    assert dob == "1990-01-15"


def test_extract_patient_info_returns_tuple(valid_components):
    """Test that extract_patient_info returns a tuple."""
    # Act
    result = extract_patient_info(valid_components)

    # Assert
    assert isinstance(result, tuple)
    assert len(result) == 2


def test_extract_patient_info_empty_values(empty_value_components):
    """Test extracting patient info when values are empty strings."""
    # Act
    name, dob = extract_patient_info(empty_value_components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_none_values(none_value_components):
    """Test extracting patient info when values are None."""
    # Act
    name, dob = extract_patient_info(none_value_components)

    # Assert
    assert name is None
    assert dob is None


def test_extract_patient_info_missing_patient_inputs():
    """Test extract_patient_info when patient_inputs key is missing."""
    # Arrange
    components = {"other_component": Mock(), "duration_display": Mock()}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_empty_components():
    """Test extract_patient_info with empty components dictionary."""
    # Arrange
    components = {}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_missing_name_key():
    """Test extract_patient_info when name key is missing."""
    # Arrange
    dob_input = Mock()
    dob_input.value = "1990-01-15"

    components = {
        "patient_inputs": {
            "dob": dob_input
            # Missing 'name' key
        }
    }

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_missing_dob_key():
    """Test extract_patient_info when dob key is missing."""
    # Arrange
    name_input = Mock()
    name_input.value = "Jane Smith"

    components = {
        "patient_inputs": {
            "name": name_input
            # Missing 'dob' key
        }
    }

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_empty_patient_inputs():
    """Test extract_patient_info when patient_inputs is empty."""
    # Arrange
    components = {"patient_inputs": {}}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_name_without_value_attribute():
    """Test extract_patient_info when name component has no value attribute."""
    # Arrange
    name_input = Mock(spec=[])  # Mock without value attribute
    dob_input = Mock()
    dob_input.value = "1990-01-15"

    components = {"patient_inputs": {"name": name_input, "dob": dob_input}}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_dob_without_value_attribute():
    """Test extract_patient_info when dob component has no value attribute."""
    # Arrange
    name_input = Mock()
    name_input.value = "John Doe"
    dob_input = Mock(spec=[])  # Mock without value attribute

    components = {"patient_inputs": {"name": name_input, "dob": dob_input}}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_both_without_value_attribute():
    """Test extract_patient_info when both components have no value attribute."""
    # Arrange
    name_input = Mock(spec=[])  # Mock without value attribute
    dob_input = Mock(spec=[])  # Mock without value attribute

    components = {"patient_inputs": {"name": name_input, "dob": dob_input}}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_name_is_none():
    """Test extract_patient_info when name component is None."""
    # Arrange
    dob_input = Mock()
    dob_input.value = "1990-01-15"

    components = {"patient_inputs": {"name": None, "dob": dob_input}}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_dob_is_none():
    """Test extract_patient_info when dob component is None."""
    # Arrange
    name_input = Mock()
    name_input.value = "John Doe"

    components = {"patient_inputs": {"name": name_input, "dob": None}}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == ""
    assert dob == ""


def test_extract_patient_info_different_data_types():
    """Test extract_patient_info with different value data types."""
    # Arrange
    name_input = Mock()
    name_input.value = 12345  # Integer instead of string

    dob_input = Mock()
    dob_input.value = ["1990", "01", "15"]  # List instead of string

    components = {"patient_inputs": {"name": name_input, "dob": dob_input}}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == 12345
    assert dob == ["1990", "01", "15"]


def test_extract_patient_info_whitespace_values():
    """Test extract_patient_info with whitespace values."""
    # Arrange
    name_input = Mock()
    name_input.value = "   John Doe   "

    dob_input = Mock()
    dob_input.value = "  1990-01-15  "

    components = {"patient_inputs": {"name": name_input, "dob": dob_input}}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == "   John Doe   "
    assert dob == "  1990-01-15  "


def test_extract_patient_info_exception_handling_keyerror():
    """Test that KeyError is properly handled."""
    # Arrange
    components = {
        "patient_inputs": {
            "name": Mock(),
            # Missing 'dob' key will cause KeyError
        }
    }
    components["patient_inputs"]["name"].value = "John Doe"

    # Act & Assert - should not raise exception
    name, dob = extract_patient_info(components)
    assert name == ""
    assert dob == ""


def test_extract_patient_info_exception_handling_attributeerror():
    """Test that AttributeError is properly handled."""
    # Arrange
    name_input = Mock(spec=[])  # No value attribute - will cause AttributeError
    dob_input = Mock()
    dob_input.value = "1990-01-15"

    components = {"patient_inputs": {"name": name_input, "dob": dob_input}}

    # Act & Assert - should not raise exception
    name, dob = extract_patient_info(components)
    assert name == ""
    assert dob == ""


def test_extract_patient_info_nested_structure_validation():
    """Test that the function accesses the correct nested structure."""
    # Arrange
    name_input = Mock()
    name_input.value = "Alice Johnson"

    dob_input = Mock()
    dob_input.value = "1985-12-25"

    components = {
        "patient_inputs": {
            "name": name_input,
            "dob": dob_input,
            "other_field": Mock(),  # Extra field that should be ignored
        },
        "other_section": {
            "name": Mock(),  # Should not access this one
            "dob": Mock(),  # Should not access this one
        },
    }

    # Act
    name, dob = extract_patient_info(components)

    # Assert - should access only the patient_inputs section
    assert name == "Alice Johnson"
    assert dob == "1985-12-25"


def test_extract_patient_info_with_complex_values():
    """Test extract_patient_info with complex object values."""
    # Arrange
    name_input = Mock()
    name_input.value = {"first": "John", "last": "Doe"}

    dob_input = Mock()
    dob_input.value = {"year": 1990, "month": 1, "day": 15}

    components = {"patient_inputs": {"name": name_input, "dob": dob_input}}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    assert name == {"first": "John", "last": "Doe"}
    assert dob == {"year": 1990, "month": 1, "day": 15}


def test_extract_patient_info_access_pattern():
    """Test that the function follows the exact access pattern."""
    # Arrange
    name_input = Mock()
    name_input.value = "Pattern Test"

    dob_input = Mock()
    dob_input.value = "Pattern DOB"

    components = {"patient_inputs": {"name": name_input, "dob": dob_input}}

    # Act
    name, dob = extract_patient_info(components)

    # Assert
    # Verify the mocks were accessed (indicating correct path)
    assert name_input.value == "Pattern Test"
    assert dob_input.value == "Pattern DOB"
    assert name == "Pattern Test"
    assert dob == "Pattern DOB"
