"""
Tests for recording_controls module - Enhanced recording controls with play functionality
"""

import pytest # type: ignore
from unittest.mock import Mock, patch, MagicMock

# Import functions to test
from gojiberri.ui.components.recording.session_card.recording_controls import (
    create_recording_controls,
    toggle_play_functionality,
    cleanup_recording_controls,
)


@pytest.fixture
def mock_components():
    """Create mock components dictionary for testing."""
    return {
        "duration_display": <PERSON><PERSON>(),
        "patient_inputs": {"name": <PERSON><PERSON>(), "age": <PERSON><PERSON>()},
    }


@pytest.fixture
def mock_recording_components():
    """Create mock recording components for testing."""
    recording_manager = Mock()
    return {
        "recording_manager": recording_manager,
        "record_button": <PERSON>ck(),
        "play_controls": {"container": <PERSON><PERSON>(), "update_timer": <PERSON>ck()},
        "cache_status": {"cache_timer": <PERSON><PERSON>()},
    }


@pytest.fixture
def mock_ui_and_functions():
    """Mock all external dependencies."""
    mocks = {}

    with patch(
        "gojiberri.ui.components.recording.session_card.recording_controls.ui"
    ) as mock_ui:
        with patch(
            "gojiberri.ui.components.recording.session_card.recording_controls.create_record_button"
        ) as mock_record:
            with patch(
                "gojiberri.ui.components.recording.session_card.recording_controls.create_recording_toggle_handler"
            ) as mock_handler:
                with patch(
                    "gojiberri.ui.components.recording.session_card.recording_controls.setup_recording_timer"
                ) as mock_timer:
                    with patch(
                        "gojiberri.ui.components.recording.session_card.recording_controls.create_recording_manager"
                    ) as mock_manager:

                        # Setup UI column mock
                        mock_column = MagicMock()
                        mock_column.classes.return_value = mock_column
                        mock_ui.column.return_value = mock_column

                        mocks["ui"] = mock_ui
                        mocks["create_record_button"] = mock_record
                        mocks["create_recording_toggle_handler"] = mock_handler
                        mocks["setup_recording_timer"] = mock_timer
                        mocks["create_recording_manager"] = mock_manager

                        yield mocks


def test_create_recording_controls_basic_functionality(
    mock_components, mock_ui_and_functions
):
    """Test basic functionality of create_recording_controls."""
    # Arrange
    mock_recording_manager = Mock()
    mock_record_button = Mock()
    mock_toggle_handler = Mock()

    mock_ui_and_functions["create_recording_manager"].return_value = (
        mock_recording_manager
    )
    mock_ui_and_functions["create_record_button"].return_value = mock_record_button
    mock_ui_and_functions["create_recording_toggle_handler"].return_value = (
        mock_toggle_handler
    )

    # Act
    result = create_recording_controls(mock_components)

    # Assert
    assert isinstance(result, dict)
    assert "recording_manager" in result
    assert "record_button" in result
    assert result["recording_manager"] == mock_recording_manager
    assert result["record_button"] == mock_record_button


def test_create_recording_controls_creates_ui_column(
    mock_components, mock_ui_and_functions
):
    """Test that UI column is created with correct classes."""
    # Arrange
    mock_ui_and_functions["create_recording_manager"].return_value = Mock()
    mock_ui_and_functions["create_record_button"].return_value = Mock()
    mock_ui_and_functions["create_recording_toggle_handler"].return_value = Mock()

    # Act
    create_recording_controls(mock_components)

    # Assert
    mock_ui_and_functions["ui"].column.assert_called_once()
    mock_ui_and_functions["ui"].column().classes.assert_called_once_with(
        "items-center gap-2"
    )


def test_create_recording_controls_creates_recording_manager(
    mock_components, mock_ui_and_functions
):
    """Test that recording manager is created with correct parameters."""
    # Arrange
    mock_recording_manager = Mock()
    mock_ui_and_functions["create_recording_manager"].return_value = (
        mock_recording_manager
    )
    mock_ui_and_functions["create_record_button"].return_value = Mock()
    mock_ui_and_functions["create_recording_toggle_handler"].return_value = Mock()

    # Act
    result = create_recording_controls(mock_components)

    # Assert
    mock_ui_and_functions["create_recording_manager"].assert_called_once_with(
        components=mock_components, duration_display=mock_components["duration_display"]
    )
    assert result["recording_manager"] == mock_recording_manager


def test_create_recording_controls_sets_up_timer(
    mock_components, mock_ui_and_functions
):
    """Test that recording timer is set up."""
    # Arrange
    mock_recording_manager = Mock()
    mock_ui_and_functions["create_recording_manager"].return_value = (
        mock_recording_manager
    )
    mock_ui_and_functions["create_record_button"].return_value = Mock()
    mock_ui_and_functions["create_recording_toggle_handler"].return_value = Mock()

    # Act
    create_recording_controls(mock_components)

    # Assert
    mock_ui_and_functions["setup_recording_timer"].assert_called_once_with(
        mock_recording_manager
    )


def test_create_recording_controls_creates_toggle_handler(
    mock_components, mock_ui_and_functions
):
    """Test that toggle handler is created with correct parameters."""
    # Arrange
    mock_recording_manager = Mock()
    mock_toggle_handler = Mock()
    mock_ui_and_functions["create_recording_manager"].return_value = (
        mock_recording_manager
    )
    mock_ui_and_functions["create_record_button"].return_value = Mock()
    mock_ui_and_functions["create_recording_toggle_handler"].return_value = (
        mock_toggle_handler
    )

    # Act
    create_recording_controls(mock_components)

    # Assert
    mock_ui_and_functions["create_recording_toggle_handler"].assert_called_once_with(
        mock_components, mock_recording_manager
    )


def test_create_recording_controls_creates_record_button(
    mock_components, mock_ui_and_functions
):
    """Test that record button is created with toggle handler."""
    # Arrange
    mock_recording_manager = Mock()
    mock_record_button = Mock()
    mock_toggle_handler = Mock()

    mock_ui_and_functions["create_recording_manager"].return_value = (
        mock_recording_manager
    )
    mock_ui_and_functions["create_record_button"].return_value = mock_record_button
    mock_ui_and_functions["create_recording_toggle_handler"].return_value = (
        mock_toggle_handler
    )

    # Act
    result = create_recording_controls(mock_components)

    # Assert
    mock_ui_and_functions["create_record_button"].assert_called_once_with(
        mock_toggle_handler
    )
    assert result["record_button"] == mock_record_button


def test_create_recording_controls_integration_flow(
    mock_components, mock_ui_and_functions
):
    """Test the complete integration flow of create_recording_controls."""
    # Arrange
    mock_recording_manager = Mock()
    mock_record_button = Mock()
    mock_toggle_handler = Mock()
    mock_column = MagicMock()

    mock_ui_and_functions["ui"].column.return_value = mock_column
    mock_column.classes.return_value = mock_column
    mock_ui_and_functions["create_recording_manager"].return_value = (
        mock_recording_manager
    )
    mock_ui_and_functions["create_record_button"].return_value = mock_record_button
    mock_ui_and_functions["create_recording_toggle_handler"].return_value = (
        mock_toggle_handler
    )

    # Act
    result = create_recording_controls(mock_components)

    # Assert - verify call sequence
    mock_ui_and_functions["ui"].column.assert_called_once()
    mock_column.classes.assert_called_once_with("items-center gap-2")
    mock_ui_and_functions["create_recording_manager"].assert_called_once()
    mock_ui_and_functions["setup_recording_timer"].assert_called_once()
    mock_ui_and_functions["create_recording_toggle_handler"].assert_called_once()
    mock_ui_and_functions["create_record_button"].assert_called_once()

    # Assert - verify result structure
    expected_keys = {"recording_manager", "record_button"}
    assert set(result.keys()) == expected_keys


def test_toggle_play_functionality_enable_with_play_controls(mock_recording_components):
    """Test enabling play functionality when play controls exist."""
    # Arrange
    recording_manager = mock_recording_components["recording_manager"]
    play_container = mock_recording_components["play_controls"]["container"]

    # Act
    toggle_play_functionality(mock_recording_components, True)

    # Assert
    recording_manager.enable_play_functionality_toggle.assert_called_once()
    play_container.style.assert_called_once_with("display: flex;")


def test_toggle_play_functionality_disable_with_play_controls(
    mock_recording_components,
):
    """Test disabling play functionality when play controls exist."""
    # Arrange
    recording_manager = mock_recording_components["recording_manager"]
    play_container = mock_recording_components["play_controls"]["container"]

    # Act
    toggle_play_functionality(mock_recording_components, False)

    # Assert
    recording_manager.disable_play_functionality.assert_called_once()
    play_container.style.assert_called_once_with("display: none;")


def test_toggle_play_functionality_enable_without_play_controls():
    """Test enabling play functionality when play controls don't exist."""
    # Arrange
    recording_manager = Mock()
    recording_components = {"recording_manager": recording_manager}

    # Act
    toggle_play_functionality(recording_components, True)

    # Assert
    recording_manager.enable_play_functionality_toggle.assert_called_once()
    # No assertion for play_container.style since it doesn't exist


def test_toggle_play_functionality_disable_without_play_controls():
    """Test disabling play functionality when play controls don't exist."""
    # Arrange
    recording_manager = Mock()
    recording_components = {"recording_manager": recording_manager}

    # Act
    toggle_play_functionality(recording_components, False)

    # Assert
    recording_manager.disable_play_functionality.assert_called_once()
    # No assertion for play_container.style since it doesn't exist


def test_toggle_play_functionality_no_recording_manager():
    """Test toggle play functionality when recording manager doesn't exist."""
    # Arrange
    recording_components = {"record_button": Mock()}

    # Act
    toggle_play_functionality(recording_components, True)

    # Assert - should not raise any exceptions
    # Function should return early without doing anything


def test_toggle_play_functionality_empty_components():
    """Test toggle play functionality with empty components dictionary."""
    # Arrange
    recording_components = {}

    # Act
    toggle_play_functionality(recording_components, True)

    # Assert - should not raise any exceptions
    # Function should return early without doing anything


def test_toggle_play_functionality_play_controls_without_container():
    """Test toggle play functionality when play_controls exists but without container."""
    # Arrange
    recording_manager = Mock()
    recording_components = {
        "recording_manager": recording_manager,
        "play_controls": {"other_component": Mock()},  # No container
    }

    # Act
    toggle_play_functionality(recording_components, True)

    # Assert
    recording_manager.enable_play_functionality_toggle.assert_called_once()
    # No style calls should be made since container doesn't exist


def test_cleanup_recording_controls_full_cleanup(mock_recording_components):
    """Test complete cleanup of recording controls."""
    # Arrange
    recording_manager = mock_recording_components["recording_manager"]
    update_timer = mock_recording_components["play_controls"]["update_timer"]
    cache_timer = mock_recording_components["cache_status"]["cache_timer"]

    # Act
    cleanup_recording_controls(mock_recording_components)

    # Assert
    recording_manager.stop_recording.assert_called_once()
    recording_manager.clear_recording_data.assert_called_once()
    update_timer.deactivate.assert_called_once()
    cache_timer.deactivate.assert_called_once()


def test_cleanup_recording_controls_only_recording_manager():
    """Test cleanup when only recording manager exists."""
    # Arrange
    recording_manager = Mock()
    recording_components = {"recording_manager": recording_manager}

    # Act
    cleanup_recording_controls(recording_components)

    # Assert
    recording_manager.stop_recording.assert_called_once()
    recording_manager.clear_recording_data.assert_called_once()


def test_cleanup_recording_controls_no_recording_manager():
    """Test cleanup when recording manager doesn't exist."""
    # Arrange
    update_timer = Mock()
    cache_timer = Mock()
    recording_components = {
        "play_controls": {"update_timer": update_timer},
        "cache_status": {"cache_timer": cache_timer},
    }

    # Act
    cleanup_recording_controls(recording_components)

    # Assert
    update_timer.deactivate.assert_called_once()
    cache_timer.deactivate.assert_called_once()


def test_cleanup_recording_controls_empty_components():
    """Test cleanup with empty components dictionary."""
    # Arrange
    recording_components = {}

    # Act
    cleanup_recording_controls(recording_components)

    # Assert - should not raise any exceptions
    # Function should handle missing components gracefully


def test_cleanup_recording_controls_partial_components():
    """Test cleanup when some components are missing."""
    # Arrange
    recording_manager = Mock()
    recording_components = {
        "recording_manager": recording_manager,
        "play_controls": {},  # Missing update_timer
        "cache_status": {},  # Missing cache_timer
    }

    # Act
    cleanup_recording_controls(recording_components)

    # Assert
    recording_manager.stop_recording.assert_called_once()
    recording_manager.clear_recording_data.assert_called_once()
    # No timer deactivation calls since timers don't exist


def test_cleanup_recording_controls_play_controls_without_timer():
    """Test cleanup when play_controls exists but without update_timer."""
    # Arrange
    recording_manager = Mock()
    recording_components = {
        "recording_manager": recording_manager,
        "play_controls": {"container": Mock()},  # No update_timer
        "cache_status": {"cache_timer": Mock()},
    }

    # Act
    cleanup_recording_controls(recording_components)

    # Assert
    recording_manager.stop_recording.assert_called_once()
    recording_manager.clear_recording_data.assert_called_once()
    recording_components["cache_status"]["cache_timer"].deactivate.assert_called_once()
    # No update_timer deactivation since it doesn't exist


def test_cleanup_recording_controls_cache_status_without_timer():
    """Test cleanup when cache_status exists but without cache_timer."""
    # Arrange
    recording_manager = Mock()
    recording_components = {
        "recording_manager": recording_manager,
        "play_controls": {"update_timer": Mock()},
        "cache_status": {"other_component": Mock()},  # No cache_timer
    }

    # Act
    cleanup_recording_controls(recording_components)

    # Assert
    recording_manager.stop_recording.assert_called_once()
    recording_manager.clear_recording_data.assert_called_once()
    recording_components["play_controls"][
        "update_timer"
    ].deactivate.assert_called_once()
    # No cache_timer deactivation since it doesn't exist


def test_create_recording_controls_context_manager_usage(
    mock_components, mock_ui_and_functions
):
    """Test that UI column is used as context manager."""
    # Arrange
    mock_column = MagicMock()
    mock_ui_and_functions["ui"].column.return_value = mock_column
    mock_column.classes.return_value = mock_column
    mock_ui_and_functions["create_recording_manager"].return_value = Mock()
    mock_ui_and_functions["create_record_button"].return_value = Mock()
    mock_ui_and_functions["create_recording_toggle_handler"].return_value = Mock()

    # Act
    create_recording_controls(mock_components)

    # Assert
    mock_column.__enter__.assert_called_once()
    mock_column.__exit__.assert_called_once()


def test_toggle_play_functionality_boolean_parameter_validation():
    """Test toggle_play_functionality with different boolean values."""
    # Arrange
    recording_manager = Mock()
    recording_components = {"recording_manager": recording_manager}

    # Act & Assert - Test with True
    toggle_play_functionality(recording_components, True)
    recording_manager.enable_play_functionality_toggle.assert_called_once()

    # Reset mock
    recording_manager.reset_mock()

    # Act & Assert - Test with False
    toggle_play_functionality(recording_components, False)
    recording_manager.disable_play_functionality.assert_called_once()


def test_create_recording_controls_return_structure():
    """Test the exact structure of the returned dictionary."""
    # Arrange
    mock_components = {"duration_display": Mock()}

    with patch(
        "gojiberri.ui.components.recording.session_card.recording_controls.ui"
    ) as mock_ui:
        with patch(
            "gojiberri.ui.components.recording.session_card.recording_controls.create_recording_manager"
        ) as mock_manager:
            with patch(
                "gojiberri.ui.components.recording.session_card.recording_controls.setup_recording_timer"
            ):
                with patch(
                    "gojiberri.ui.components.recording.session_card.recording_controls.create_recording_toggle_handler"
                ):
                    with patch(
                        "gojiberri.ui.components.recording.session_card.recording_controls.create_record_button"
                    ) as mock_button:

                        mock_column = MagicMock()
                        mock_ui.column.return_value = mock_column
                        mock_column.classes.return_value = mock_column

                        mock_recording_manager = Mock()
                        mock_record_button = Mock()
                        mock_manager.return_value = mock_recording_manager
                        mock_button.return_value = mock_record_button

                        # Act
                        result = create_recording_controls(mock_components)

                        # Assert
                        assert len(result) == 2
                        assert result["recording_manager"] == mock_recording_manager
                        assert result["record_button"] == mock_record_button
                        assert isinstance(result, dict)
