"""
Tests for ui_components module - Clean UI components
"""

import pytest  # type: ignore
from unittest.mock import Mock, patch

# Import functions to test
from gojiberri.ui.components.recording.session_card.ui_components import (
    create_card_container,
    create_duration_display,
    create_record_button,
)


@pytest.fixture
def mock_container():
    """Create a mock container for testing."""
    container = Mock()
    container.classes = Mock(return_value=container)
    container.style = Mock(return_value=container)
    return container


@pytest.fixture
def mock_toggle_handler():
    """Create a mock toggle handler function for testing."""
    return Mock()


@pytest.fixture
def mock_ui_label():
    """Mock the ui.label function."""
    with patch(
        "gojiberri.ui.components.recording.session_card.ui_components.ui.label"
    ) as mock_label:
        yield mock_label


@pytest.fixture
def mock_create_styled_button():
    """Mock the create_styled_button function."""
    with patch("gojiberri.ui.components.create_styled_button") as mock_button:
        yield mock_button


def test_create_card_container_applies_classes(mock_container):
    """Test that create_card_container applies correct CSS classes."""
    # Act
    result = create_card_container(mock_container)

    # Assert
    mock_container.classes.assert_called_once_with("w-full md:p-4 p-2 shadow-none")
    assert result == mock_container


def test_create_card_container_applies_style(mock_container):
    """Test that create_card_container applies correct inline styles."""
    # Act
    result = create_card_container(mock_container)

    # Assert
    expected_style = "max-width: 360px; background-color: rgba(255, 255, 255, 0);"
    mock_container.style.assert_called_once_with(expected_style)
    assert result == mock_container


def test_create_card_container_method_chaining(mock_container):
    """Test that create_card_container properly chains methods."""
    # Act
    result = create_card_container(mock_container)

    # Assert
    # Verify that classes is called first, then style on the returned object
    mock_container.classes.assert_called_once()
    mock_container.style.assert_called_once()
    assert result == mock_container


def test_create_card_container_returns_container(mock_container):
    """Test that create_card_container returns the styled container."""
    # Act
    result = create_card_container(mock_container)

    # Assert
    assert result is mock_container


def test_create_card_container_with_different_containers():
    """Test create_card_container with different container types."""
    # Test with first container
    container1 = Mock()
    container1.classes = Mock(return_value=container1)
    container1.style = Mock(return_value=container1)

    # Test with second container
    container2 = Mock()
    container2.classes = Mock(return_value=container2)
    container2.style = Mock(return_value=container2)

    # Act
    result1 = create_card_container(container1)
    result2 = create_card_container(container2)

    # Assert
    assert result1 is container1
    assert result2 is container2
    container1.classes.assert_called_once_with("w-full md:p-4 p-2 shadow-none")
    container2.classes.assert_called_once_with("w-full md:p-4 p-2 shadow-none")


def test_create_duration_display_creates_label(mock_ui_label):
    """Test that create_duration_display creates a UI label with correct text."""
    # Arrange
    mock_label_instance = Mock()
    mock_label_instance.classes = Mock(return_value=mock_label_instance)
    mock_ui_label.return_value = mock_label_instance

    # Act
    result = create_duration_display()

    # Assert
    mock_ui_label.assert_called_once_with("Duration: 00:00:00")
    assert result == mock_label_instance  # Should return the result of .classes()


def test_create_duration_display_applies_classes(mock_ui_label):
    """Test that create_duration_display applies correct CSS classes."""
    # Arrange
    mock_label_instance = Mock()
    mock_label_instance.classes = Mock(return_value=mock_label_instance)
    mock_ui_label.return_value = mock_label_instance

    # Act
    result = create_duration_display()

    # Assert
    mock_label_instance.classes.assert_called_once_with(
        "text-center mb-2 mt-4 test-duration"
    )
    assert result == mock_label_instance


def test_create_duration_display_method_chaining(mock_ui_label):
    """Test that create_duration_display properly chains methods."""
    # Arrange
    mock_label_instance = Mock()
    mock_label_instance.classes = Mock(return_value=mock_label_instance)
    mock_ui_label.return_value = mock_label_instance

    # Act
    result = create_duration_display()

    # Assert
    # Verify ui.label is called first, then classes on the returned object
    mock_ui_label.assert_called_once()
    mock_label_instance.classes.assert_called_once()
    assert result == mock_label_instance


def test_create_duration_display_returns_styled_label(mock_ui_label):
    """Test that create_duration_display returns the styled label."""
    # Arrange
    mock_label_instance = Mock()
    mock_label_instance.classes = Mock(return_value=mock_label_instance)
    mock_ui_label.return_value = mock_label_instance

    # Act
    result = create_duration_display()

    # Assert
    assert result is mock_label_instance


def test_create_duration_display_text_content():
    """Test that create_duration_display uses correct initial text."""
    with patch(
        "gojiberri.ui.components.recording.session_card.ui_components.ui.label"
    ) as mock_label:
        mock_label_instance = Mock()
        mock_label_instance.classes = Mock(return_value=mock_label_instance)
        mock_label.return_value = mock_label_instance

        # Act
        create_duration_display()

        # Assert
        mock_label.assert_called_once_with("Duration: 00:00:00")


def test_create_record_button_calls_create_styled_button(
    mock_toggle_handler, mock_create_styled_button
):
    """Test that create_record_button calls create_styled_button with correct parameters."""
    # Arrange
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    result = create_record_button(mock_toggle_handler)

    # Assert
    mock_create_styled_button.assert_called_once_with(
        button_text="Start Recording",
        icon="play_arrow",
        toggle_behavior=True,
        toggle_texts=("Start Recording", "Pause Recording"),
        toggle_icons=("play_arrow", "pause"),
        initial_state=False,
        on_click_callback=mock_toggle_handler,
        additional_classes="mt-2",
    )
    assert result == mock_button_instance


def test_create_record_button_passes_toggle_handler(mock_create_styled_button):
    """Test that create_record_button passes the toggle handler correctly."""
    # Arrange
    custom_handler = Mock()
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    result = create_record_button(custom_handler)

    # Assert
    call_args = mock_create_styled_button.call_args[1]  # Get keyword arguments
    assert call_args["on_click_callback"] == custom_handler
    assert result == mock_button_instance


def test_create_record_button_uses_correct_button_text(
    mock_toggle_handler, mock_create_styled_button
):
    """Test that create_record_button uses correct button text."""
    # Arrange
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    create_record_button(mock_toggle_handler)

    # Assert
    call_args = mock_create_styled_button.call_args[1]
    assert call_args["button_text"] == "Start Recording"


def test_create_record_button_uses_correct_icon(
    mock_toggle_handler, mock_create_styled_button
):
    """Test that create_record_button uses correct icon."""
    # Arrange
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    create_record_button(mock_toggle_handler)

    # Assert
    call_args = mock_create_styled_button.call_args[1]
    assert call_args["icon"] == "play_arrow"


def test_create_record_button_toggle_behavior_enabled(
    mock_toggle_handler, mock_create_styled_button
):
    """Test that create_record_button enables toggle behavior."""
    # Arrange
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    create_record_button(mock_toggle_handler)

    # Assert
    call_args = mock_create_styled_button.call_args[1]
    assert call_args["toggle_behavior"] is True


def test_create_record_button_toggle_texts(
    mock_toggle_handler, mock_create_styled_button
):
    """Test that create_record_button uses correct toggle texts."""
    # Arrange
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    create_record_button(mock_toggle_handler)

    # Assert
    call_args = mock_create_styled_button.call_args[1]
    assert call_args["toggle_texts"] == ("Start Recording", "Pause Recording")


def test_create_record_button_toggle_icons(
    mock_toggle_handler, mock_create_styled_button
):
    """Test that create_record_button uses correct toggle icons."""
    # Arrange
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    create_record_button(mock_toggle_handler)

    # Assert
    call_args = mock_create_styled_button.call_args[1]
    assert call_args["toggle_icons"] == ("play_arrow", "pause")


def test_create_record_button_initial_state(
    mock_toggle_handler, mock_create_styled_button
):
    """Test that create_record_button sets correct initial state."""
    # Arrange
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    create_record_button(mock_toggle_handler)

    # Assert
    call_args = mock_create_styled_button.call_args[1]
    assert call_args["initial_state"] is False


def test_create_record_button_additional_classes(
    mock_toggle_handler, mock_create_styled_button
):
    """Test that create_record_button applies additional CSS classes."""
    # Arrange
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    create_record_button(mock_toggle_handler)

    # Assert
    call_args = mock_create_styled_button.call_args[1]
    assert call_args["additional_classes"] == "mt-2"


def test_create_record_button_returns_styled_button(
    mock_toggle_handler, mock_create_styled_button
):
    """Test that create_record_button returns the created styled button."""
    # Arrange
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    result = create_record_button(mock_toggle_handler)

    # Assert
    assert result is mock_button_instance


def test_create_record_button_with_different_handlers(mock_create_styled_button):
    """Test create_record_button with different toggle handlers."""
    # Arrange
    handler1 = Mock()
    handler2 = Mock()
    mock_button_instance = Mock()
    mock_create_styled_button.return_value = mock_button_instance

    # Act
    result1 = create_record_button(handler1)
    result2 = create_record_button(handler2)

    # Assert
    assert mock_create_styled_button.call_count == 2

    # Check first call
    first_call_args = mock_create_styled_button.call_args_list[0][1]
    assert first_call_args["on_click_callback"] == handler1

    # Check second call
    second_call_args = mock_create_styled_button.call_args_list[1][1]
    assert second_call_args["on_click_callback"] == handler2

    assert result1 is mock_button_instance
    assert result2 is mock_button_instance


def test_create_record_button_all_parameters_passed():
    """Test that all required parameters are passed to create_styled_button."""
    # Arrange
    toggle_handler = Mock()

    with patch("gojiberri.ui.components.create_styled_button") as mock_styled:
        mock_button = Mock()
        mock_styled.return_value = mock_button

        # Act
        result = create_record_button(toggle_handler)

        # Assert
        expected_params = {
            "button_text": "Start Recording",
            "icon": "play_arrow",
            "toggle_behavior": True,
            "toggle_texts": ("Start Recording", "Pause Recording"),
            "toggle_icons": ("play_arrow", "pause"),
            "initial_state": False,
            "on_click_callback": toggle_handler,
            "additional_classes": "mt-2",
        }

        mock_styled.assert_called_once_with(**expected_params)
        assert result == mock_button


def test_create_card_container_style_string_format():
    """Test that create_card_container uses exact style string format."""
    # Arrange
    container = Mock()
    container.classes = Mock(return_value=container)
    container.style = Mock(return_value=container)

    # Act
    create_card_container(container)

    # Assert
    expected_style = "max-width: 360px; background-color: rgba(255, 255, 255, 0);"
    container.style.assert_called_once_with(expected_style)


def test_create_card_container_classes_string_format():
    """Test that create_card_container uses exact classes string format."""
    # Arrange
    container = Mock()
    container.classes = Mock(return_value=container)
    container.style = Mock(return_value=container)

    # Act
    create_card_container(container)

    # Assert
    expected_classes = "w-full md:p-4 p-2 shadow-none"
    container.classes.assert_called_once_with(expected_classes)


def test_create_duration_display_classes_string_format():
    """Test that create_duration_display uses exact classes string format."""
    with patch(
        "gojiberri.ui.components.recording.session_card.ui_components.ui.label"
    ) as mock_label:
        mock_label_instance = Mock()
        mock_label_instance.classes = Mock(return_value=mock_label_instance)
        mock_label.return_value = mock_label_instance

        # Act
        create_duration_display()

        # Assert
        expected_classes = "text-center mb-2 mt-4 test-duration"
        mock_label_instance.classes.assert_called_once_with(expected_classes)


def test_create_record_button_import_location():
    """Test that create_record_button imports create_styled_button from correct location."""
    # This test verifies the import happens within the function
    toggle_handler = Mock()

    # Mock the import at the actual location it's imported from
    with patch("gojiberri.ui.components.create_styled_button") as mock_styled:
        mock_button = Mock()
        mock_styled.return_value = mock_button

        # Act
        result = create_record_button(toggle_handler)

        # Assert
        mock_styled.assert_called_once()
        assert result == mock_button
