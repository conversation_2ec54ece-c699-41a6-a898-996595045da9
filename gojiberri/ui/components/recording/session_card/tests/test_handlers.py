"""
Comprehensive tests for event handlers module.
Tests recording toggle functionality, validation, UI updates, and error handling.
"""

import pytest  # type: ignore
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
import asyncio


from gojiberri.ui.components.recording.session_card.handlers import (
    create_recording_toggle_handler,
    _validate_before_recording,
    _highlight_field_error,
    _clear_validation_errors,
    _handle_toggle_completion,
    handle_recording_error_recovery,
    reset_validation_styles,
    apply_validation_feedback,
)


# Fixtures
@pytest.fixture
def mock_components():
    """Fixture for mock UI components."""
    return {"patient_inputs": {"name": <PERSON>ck(), "dob": <PERSON>ck()}}


@pytest.fixture
def mock_recording_manager():
    """Fixture for mock recording manager."""
    manager = Mock()
    manager.recording_started = False
    return manager


@pytest.fixture
def event_loop():
    """Create an event loop for async tests."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# Test Create Recording Toggle Handler
def test_creates_handler_function():
    """Test that create_recording_toggle_handler returns a callable."""
    components = {}
    recording_manager = Mock()

    handler = create_recording_toggle_handler(components, recording_manager)

    assert callable(handler)


@patch(
    "gojiberri.ui.components.recording.session_card.handlers._validate_before_recording"
)
@patch("gojiberri.ui.components.recording.session_card.handlers._process_toggle_async")
def test_handler_validates_before_starting_recording(mock_process, mock_validate):
    """Test that handler validates before starting recording."""
    components = {}
    recording_manager = Mock()
    recording_manager.recording_started = False
    mock_validate.return_value = True

    handler = create_recording_toggle_handler(components, recording_manager)
    result = handler(True)  # Start recording

    mock_validate.assert_called_once_with(components)
    mock_process.assert_called_once_with(True, components, recording_manager)
    assert result is True


@patch(
    "gojiberri.ui.components.recording.session_card.handlers._validate_before_recording"
)
@patch("gojiberri.ui.components.recording.session_card.handlers._process_toggle_async")
def test_handler_rejects_invalid_start(mock_process, mock_validate):
    """Test that handler rejects invalid recording start."""
    components = {}
    recording_manager = Mock()
    recording_manager.recording_started = False
    mock_validate.return_value = False

    handler = create_recording_toggle_handler(components, recording_manager)
    result = handler(True)  # Start recording

    mock_validate.assert_called_once_with(components)
    mock_process.assert_not_called()
    assert result is False


@patch(
    "gojiberri.ui.components.recording.session_card.handlers._validate_before_recording"
)
@patch("gojiberri.ui.components.recording.session_card.handlers._process_toggle_async")
def test_handler_skips_validation_when_already_recording(mock_process, mock_validate):
    """Test that handler skips validation when already recording."""
    components = {}
    recording_manager = Mock()
    recording_manager.recording_started = True

    handler = create_recording_toggle_handler(components, recording_manager)
    result = handler(True)  # Continue recording

    mock_validate.assert_not_called()
    mock_process.assert_called_once_with(True, components, recording_manager)
    assert result is True


@patch(
    "gojiberri.ui.components.recording.session_card.handlers._validate_before_recording"
)
@patch("gojiberri.ui.components.recording.session_card.handlers._process_toggle_async")
def test_handler_processes_stop_recording(mock_process, mock_validate):
    """Test that handler processes stop recording without validation."""
    components = {}
    recording_manager = Mock()

    handler = create_recording_toggle_handler(components, recording_manager)
    result = handler(False)  # Stop recording

    mock_validate.assert_not_called()
    mock_process.assert_called_once_with(False, components, recording_manager)
    assert result is True


# Test Validate Before Recording
@patch("gojiberri.ui.components.recording.session_card.handlers.extract_patient_info")
@patch("gojiberri.ui.components.recording.session_card.handlers.safe_show_notification")
@patch("gojiberri.ui.components.recording.session_card.handlers._highlight_field_error")
def test_validates_empty_patient_name(
    mock_highlight, mock_notify, mock_extract, mock_components
):
    """Test validation fails for empty patient name."""
    mock_extract.return_value = ("", "2000-01-01")

    result = _validate_before_recording(mock_components)

    assert result is False
    mock_notify.assert_called_with(
        "Please enter patient name before starting recording", type="negative"
    )
    mock_highlight.assert_called_with(mock_components, "name")


@patch("gojiberri.ui.components.recording.session_card.handlers.extract_patient_info")
@patch("gojiberri.ui.components.recording.session_card.handlers.safe_show_notification")
@patch("gojiberri.ui.components.recording.session_card.handlers._highlight_field_error")
def test_validates_empty_patient_dob(
    mock_highlight, mock_notify, mock_extract, mock_components
):
    """Test validation fails for empty date of birth."""
    mock_extract.return_value = ("John Doe", "")

    result = _validate_before_recording(mock_components)

    assert result is False
    mock_notify.assert_called_with(
        "Please enter patient date of birth before starting recording",
        type="negative",
    )
    mock_highlight.assert_called_with(mock_components, "dob")


@patch("gojiberri.ui.components.recording.session_card.handlers.extract_patient_info")
@patch("gojiberri.ui.components.recording.session_card.handlers.safe_show_notification")
@patch("gojiberri.ui.components.recording.session_card.handlers._highlight_field_error")
def test_validates_future_dob(
    mock_highlight, mock_notify, mock_extract, mock_components
):
    """Test validation fails for future date of birth."""
    future_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    mock_extract.return_value = ("John Doe", future_date)

    result = _validate_before_recording(mock_components)

    assert result is False
    mock_notify.assert_called_with(
        "Date of birth cannot be in the future", type="negative"
    )
    mock_highlight.assert_called_with(mock_components, "dob")


@patch("gojiberri.ui.components.recording.session_card.handlers.extract_patient_info")
@patch("gojiberri.ui.components.recording.session_card.handlers.safe_show_notification")
@patch("gojiberri.ui.components.recording.session_card.handlers._highlight_field_error")
def test_validates_invalid_date_format(
    mock_highlight, mock_notify, mock_extract, mock_components
):
    """Test validation fails for invalid date format."""
    mock_extract.return_value = ("John Doe", "invalid-date")

    result = _validate_before_recording(mock_components)

    assert result is False
    mock_notify.assert_called_with(
        "Invalid date format. Please use YYYY-MM-DD.", type="negative"
    )
    mock_highlight.assert_called_with(mock_components, "dob")


@patch("gojiberri.ui.components.recording.session_card.handlers.extract_patient_info")
@patch(
    "gojiberri.ui.components.recording.session_card.handlers._clear_validation_errors"
)
def test_validates_correct_patient_info(mock_clear, mock_extract, mock_components):
    """Test validation passes for correct patient information."""
    past_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
    mock_extract.return_value = ("John Doe", past_date)

    result = _validate_before_recording(mock_components)

    assert result is True
    mock_clear.assert_called_once_with(mock_components)


@patch("gojiberri.ui.components.recording.session_card.handlers.extract_patient_info")
def test_handles_whitespace_in_inputs(mock_extract, mock_components):
    """Test validation handles whitespace correctly."""
    past_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
    mock_extract.return_value = ("  John Doe  ", f"  {past_date}  ")

    result = _validate_before_recording(mock_components)

    assert result is True


# Test UI Helper Functions
def test_highlight_field_error():
    """Test field error highlighting."""
    mock_field = Mock()
    components = {"patient_inputs": {"name": mock_field, "dob": mock_field}}

    _highlight_field_error(components, "name")

    mock_field.style.assert_called_with("border-bottom: 1px solid red;")
    mock_field.style().props.assert_called_with("color='red'")


@patch("gojiberri.ui.components.recording.session_card.handlers.debug")
def test_highlight_field_error_handles_exception(mock_debug):
    """Test field error highlighting handles exceptions gracefully."""
    mock_field = Mock()
    mock_field.style.side_effect = Exception("UI error")
    components = {"patient_inputs": {"name": mock_field, "dob": mock_field}}

    _highlight_field_error(components, "name")

    mock_debug.assert_called_with("Error highlighting field error: UI error")


def test_reset_validation_styles():
    """Test resetting validation styles."""
    mock_field = Mock()
    components = {"patient_inputs": {"name": mock_field, "dob": mock_field}}

    reset_validation_styles(components)

    # Should be called twice (once for name, once for dob)
    assert mock_field.style.call_count == 2


@patch("gojiberri.ui.components.recording.session_card.handlers.debug")
def test_reset_validation_styles_handles_exception(mock_debug):
    """Test reset validation styles handles exceptions gracefully."""
    mock_field = Mock()
    mock_field.style.side_effect = Exception("UI error")
    components = {"patient_inputs": {"name": mock_field, "dob": mock_field}}

    reset_validation_styles(components)

    mock_debug.assert_called_with("Error resetting validation styles: UI error")


@patch("gojiberri.ui.components.recording.session_card.handlers.update_ui_after_toggle")
def test_handle_toggle_completion_success(mock_update_ui):
    """Test successful task completion handling."""
    task = Mock()
    task.result.return_value = {"success": True}
    components = {}
    recording_manager = Mock()

    _handle_toggle_completion(task, components, recording_manager, True)

    task.result.assert_called_once()
    mock_update_ui.assert_called_once_with(
        {"success": True}, components, recording_manager, True
    )


@patch("gojiberri.ui.components.recording.session_card.handlers.update_ui_after_toggle")
@patch("gojiberri.ui.components.recording.session_card.handlers.safe_show_notification")
@patch("gojiberri.ui.components.recording.session_card.handlers.critical")
def test_handle_toggle_completion_exception(mock_critical, mock_notify, mock_update_ui):
    """Test task completion with exception."""
    task = Mock()
    task.result.side_effect = Exception("Task failed")
    components = {}
    recording_manager = Mock()

    _handle_toggle_completion(task, components, recording_manager, True)

    mock_critical.assert_called()
    mock_notify.assert_called_with("Recording error: Task failed", type="negative")
    mock_update_ui.assert_not_called()


# Test Error Recovery
@patch(
    "gojiberri.ui.components.recording.session_card.handlers._clear_validation_errors"
)
@patch("gojiberri.ui.components.recording.session_card.handlers.debug")
def test_handle_recording_error_recovery(mock_debug, mock_clear):
    """Test recording error recovery resets state correctly."""
    recording_manager = Mock()
    record_button = Mock()
    record_button.toggled = True
    components = {
        "record_button": record_button,
        "patient_inputs": {"name": Mock(), "dob": Mock()},
    }

    handle_recording_error_recovery(components, recording_manager)

    recording_manager.reset_recording_state.assert_called_once()
    assert record_button.toggled is False
    mock_clear.assert_called_once_with(components)
    mock_debug.assert_called_with("Recording error recovery completed")


@patch("gojiberri.ui.components.recording.session_card.handlers.critical")
def test_handle_recording_error_recovery_exception(mock_critical):
    """Test error recovery handles exceptions gracefully."""
    recording_manager = Mock()
    recording_manager.reset_recording_state.side_effect = Exception("Reset failed")
    record_button = Mock()
    components = {
        "record_button": record_button,
        "patient_inputs": {"name": Mock(), "dob": Mock()},
    }

    handle_recording_error_recovery(components, recording_manager)

    mock_critical.assert_called_with(
        "Error during recording error recovery: Reset failed"
    )


# Test Validation Feedback
@patch(
    "gojiberri.ui.components.recording.session_card.handlers.reset_validation_styles"
)
@patch("gojiberri.ui.components.recording.session_card.handlers.safe_show_notification")
def test_apply_validation_feedback_success(mock_notify, mock_reset):
    """Test applying successful validation feedback."""
    components = {"patient_inputs": {"name": Mock(), "dob": Mock()}}
    validation_result = {"valid": True}

    apply_validation_feedback(components, validation_result)

    mock_reset.assert_called_once_with(components)
    mock_notify.assert_called_with("Validation passed", type="positive")


@patch("gojiberri.ui.components.recording.session_card.handlers.safe_show_notification")
def test_apply_validation_feedback_failure(mock_notify):
    """Test applying failed validation feedback."""
    components = {"patient_inputs": {"name": Mock(), "dob": Mock()}}
    validation_result = {"valid": False, "message": "Custom error message"}

    apply_validation_feedback(components, validation_result)

    mock_notify.assert_called_with("Custom error message", type="negative")


@patch("gojiberri.ui.components.recording.session_card.handlers.safe_show_notification")
def test_apply_validation_feedback_failure_default_message(mock_notify):
    """Test applying failed validation feedback with default message."""
    components = {"patient_inputs": {"name": Mock(), "dob": Mock()}}
    validation_result = {"valid": False}

    apply_validation_feedback(components, validation_result)

    mock_notify.assert_called_with("Validation failed", type="negative")


# Test Integration
@patch("gojiberri.ui.components.recording.session_card.handlers.extract_patient_info")
@patch("gojiberri.ui.components.recording.session_card.handlers._process_toggle_async")
@patch(
    "gojiberri.ui.components.recording.session_card.handlers._clear_validation_errors"
)
def test_complete_successful_recording_start(mock_clear, mock_process, mock_extract):
    """Test complete workflow for successful recording start."""
    components = {
        "record_button": Mock(),
        "patient_inputs": {"name": Mock(), "dob": Mock()},
    }
    recording_manager = Mock()
    recording_manager.recording_started = False

    # Setup valid patient info
    past_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
    mock_extract.return_value = ("John Doe", past_date)

    # Create and call handler
    handler = create_recording_toggle_handler(components, recording_manager)
    result = handler(True)

    # Verify complete flow
    assert result is True
    mock_extract.assert_called_once_with(components)
    mock_clear.assert_called_once_with(components)
    mock_process.assert_called_once_with(True, components, recording_manager)


@patch("gojiberri.ui.components.recording.session_card.handlers.extract_patient_info")
@patch("gojiberri.ui.components.recording.session_card.handlers.safe_show_notification")
@patch("gojiberri.ui.components.recording.session_card.handlers._highlight_field_error")
@patch("gojiberri.ui.components.recording.session_card.handlers._process_toggle_async")
def test_complete_failed_recording_start(
    mock_process, mock_highlight, mock_notify, mock_extract
):
    """Test complete workflow for failed recording start."""
    components = {
        "record_button": Mock(),
        "patient_inputs": {"name": Mock(), "dob": Mock()},
    }
    recording_manager = Mock()
    recording_manager.recording_started = False

    # Setup invalid patient info
    mock_extract.return_value = ("", "2000-01-01")

    # Create and call handler
    handler = create_recording_toggle_handler(components, recording_manager)
    result = handler(True)

    # Verify validation failure flow
    assert result is False
    mock_extract.assert_called_once_with(components)
    mock_notify.assert_called_with(
        "Please enter patient name before starting recording", type="negative"
    )
    mock_highlight.assert_called_with(components, "name")
    mock_process.assert_not_called()


# Test Edge Cases
def test_validate_before_recording_missing_components():
    """Test validation with missing UI components."""
    components = {}  # Empty components

    with patch(
        "gojiberri.ui.components.recording.session_card.handlers.extract_patient_info"
    ) as mock_extract:
        mock_extract.return_value = ("John Doe", "2000-01-01")
        result = _validate_before_recording(components)
        # Should handle missing components gracefully
        assert result is True


def test_highlight_field_error_missing_field():
    """Test field highlighting with missing field component."""
    components = {"patient_inputs": {}}  # Missing field

    # Should not raise exception
    _highlight_field_error(components, "nonexistent_field")


# Test parametrized scenarios
@pytest.mark.parametrize(
    "patient_name,patient_dob,expected_result",
    [
        ("John Doe", "2000-01-01", True),  # Valid inputs
        ("", "2000-01-01", False),  # Empty name
        ("John Doe", "", False),  # Empty DOB
        ("  ", "2000-01-01", False),  # Whitespace-only name
        ("John Doe", "  ", False),  # Whitespace-only DOB
        ("John Doe", "invalid-date", False),  # Invalid date format
    ],
)
@patch("gojiberri.ui.components.recording.session_card.handlers.extract_patient_info")
def test_validation_scenarios(
    mock_extract, patient_name, patient_dob, expected_result, mock_components
):
    """Test various validation scenarios using parametrized tests."""
    mock_extract.return_value = (patient_name, patient_dob)

    with patch(
        "gojiberri.ui.components.recording.session_card.handlers.safe_show_notification"
    ), patch(
        "gojiberri.ui.components.recording.session_card.handlers._highlight_field_error"
    ), patch(
        "gojiberri.ui.components.recording.session_card.handlers._clear_validation_errors"
    ):

        result = _validate_before_recording(mock_components)
        assert result == expected_result


# Test multiple recording states
@pytest.mark.parametrize(
    "recording_started,is_recording,should_validate",
    [
        (False, True, True),  # Start recording - should validate
        (True, True, False),  # Continue recording - skip validation
        (False, False, False),  # Stop recording - skip validation
        (True, False, False),  # Stop recording - skip validation
    ],
)
@patch(
    "gojiberri.ui.components.recording.session_card.handlers._validate_before_recording"
)
@patch("gojiberri.ui.components.recording.session_card.handlers._process_toggle_async")
def test_recording_toggle_validation_scenarios(
    mock_process, mock_validate, recording_started, is_recording, should_validate
):
    """Test recording toggle validation in different scenarios."""
    components = {}
    recording_manager = Mock()
    recording_manager.recording_started = recording_started
    mock_validate.return_value = True

    handler = create_recording_toggle_handler(components, recording_manager)
    result = handler(is_recording)

    if should_validate:
        mock_validate.assert_called_once_with(components)
    else:
        mock_validate.assert_not_called()

    mock_process.assert_called_once_with(is_recording, components, recording_manager)
    assert result is True


# Test async exception handling
def test_async_task_callback_exception_handling():
    """Test that async task callbacks handle exceptions properly."""
    task = Mock()
    task.result.side_effect = RuntimeError("Async task failed")
    components = {}
    recording_manager = Mock()

    with patch(
        "gojiberri.ui.components.recording.session_card.handlers.critical"
    ) as mock_critical, patch(
        "gojiberri.ui.components.recording.session_card.handlers.safe_show_notification"
    ) as mock_notify:

        _handle_toggle_completion(task, components, recording_manager, True)

        mock_critical.assert_called()
        mock_notify.assert_called_with(
            "Recording error: Async task failed", type="negative"
        )


# Test UI component interaction edge cases
def test_ui_component_methods_missing():
    """Test UI interactions when component methods are missing."""
    # Create a mock that doesn't have the expected methods
    incomplete_field = Mock()
    del incomplete_field.style

    components = {"patient_inputs": {"name": incomplete_field, "dob": incomplete_field}}

    with patch(
        "gojiberri.ui.components.recording.session_card.handlers.debug"
    ) as mock_debug:
        # These should handle missing methods gracefully
        _highlight_field_error(components, "name")
        _clear_validation_errors(components)
        reset_validation_styles(components)

        # Should log debug messages for errors
        assert mock_debug.call_count >= 1
