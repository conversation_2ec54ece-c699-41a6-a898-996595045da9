"""
Tests for timer_manager module - Timer setup and control utilities
"""

import pytest  # type: ignore
from unittest.mock import Mock, patch

# Import functions to test
from gojiberri.ui.components.recording.session_card.timer_manager import (
    setup_recording_timer,
    manage_timer_state,
    _activate_timer,
    _deactivate_timer,
)


@pytest.fixture
def mock_recording_manager():
    """Create a mock recording manager for testing."""
    manager = Mock()
    manager.update_duration = Mock()
    manager.is_recording = False
    manager.timer = None
    return manager


@pytest.fixture
def mock_recording_manager_with_timer():
    """Create a mock recording manager with timer for testing."""
    manager = Mock()
    manager.update_duration = Mock()
    manager.is_recording = False

    # Create mock timer with all necessary attributes
    mock_timer = Mock()
    mock_timer.activate = Mock()
    mock_timer.deactivate = Mock()
    mock_timer.callback = Mock()  # Timer has callback attribute
    manager.timer = mock_timer

    return manager


@pytest.fixture
def mock_ui_timer():
    """Mock the ui.timer function."""
    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager.ui.timer"
    ) as mock_timer:
        yield mock_timer


@pytest.fixture
def mock_logging_functions():
    """Mock the warning and critical logging functions."""
    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager.warning"
    ) as mock_warning:
        with patch(
            "gojiberri.ui.components.recording.session_card.timer_manager.critical"
        ) as mock_critical:
            yield {"warning": mock_warning, "critical": mock_critical}


def test_setup_recording_timer_creates_timer(
    mock_recording_manager, mock_ui_timer, mock_logging_functions
):
    """Test that setup_recording_timer creates a timer with correct parameters."""
    # Arrange
    mock_timer_instance = Mock()
    mock_ui_timer.return_value = mock_timer_instance

    # Act
    setup_recording_timer(mock_recording_manager)

    # Assert
    mock_ui_timer.assert_called_once_with(1.0, mock_recording_manager.update_duration)
    assert mock_recording_manager.timer == mock_timer_instance


def test_setup_recording_timer_deactivates_timer(
    mock_recording_manager, mock_ui_timer, mock_logging_functions
):
    """Test that setup_recording_timer deactivates the timer initially."""
    # Arrange
    mock_timer_instance = Mock()
    mock_ui_timer.return_value = mock_timer_instance

    # Act
    setup_recording_timer(mock_recording_manager)

    # Assert
    mock_timer_instance.deactivate.assert_called_once()


def test_setup_recording_timer_attaches_timer_to_manager(
    mock_recording_manager, mock_ui_timer, mock_logging_functions
):
    """Test that timer is properly attached to recording manager."""
    # Arrange
    mock_timer_instance = Mock()
    mock_ui_timer.return_value = mock_timer_instance

    # Act
    setup_recording_timer(mock_recording_manager)

    # Assert
    assert mock_recording_manager.timer is mock_timer_instance


def test_setup_recording_timer_uses_correct_interval_and_callback(
    mock_recording_manager, mock_ui_timer, mock_logging_functions
):
    """Test that timer is created with correct interval and callback."""
    # Arrange
    mock_timer_instance = Mock()
    mock_ui_timer.return_value = mock_timer_instance

    # Act
    setup_recording_timer(mock_recording_manager)

    # Assert
    mock_ui_timer.assert_called_once_with(1.0, mock_recording_manager.update_duration)


def test_manage_timer_state_activates_when_recording(
    mock_recording_manager_with_timer, mock_logging_functions
):
    """Test that manage_timer_state activates timer when recording is active."""
    # Arrange
    mock_recording_manager_with_timer.is_recording = True

    # Act
    manage_timer_state(mock_recording_manager_with_timer)

    # Assert
    mock_recording_manager_with_timer.timer.activate.assert_called_once()


def test_manage_timer_state_deactivates_when_not_recording(
    mock_recording_manager_with_timer, mock_logging_functions
):
    """Test that manage_timer_state deactivates timer when recording is inactive."""
    # Arrange
    mock_recording_manager_with_timer.is_recording = False

    # Act
    manage_timer_state(mock_recording_manager_with_timer)

    # Assert
    mock_recording_manager_with_timer.timer.deactivate.assert_called_once()


def test_manage_timer_state_calls_activate_timer(
    mock_recording_manager_with_timer, mock_logging_functions
):
    """Test that manage_timer_state calls _activate_timer when recording."""
    # Arrange
    mock_recording_manager_with_timer.is_recording = True

    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager._activate_timer"
    ) as mock_activate:
        # Act
        manage_timer_state(mock_recording_manager_with_timer)

        # Assert
        mock_activate.assert_called_once_with(mock_recording_manager_with_timer)


def test_manage_timer_state_calls_deactivate_timer(
    mock_recording_manager_with_timer, mock_logging_functions
):
    """Test that manage_timer_state calls _deactivate_timer when not recording."""
    # Arrange
    mock_recording_manager_with_timer.is_recording = False

    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager._deactivate_timer"
    ) as mock_deactivate:
        # Act
        manage_timer_state(mock_recording_manager_with_timer)

        # Assert
        mock_deactivate.assert_called_once_with(mock_recording_manager_with_timer)


def test_activate_timer_activates_existing_timer(
    mock_recording_manager_with_timer, mock_logging_functions
):
    """Test that _activate_timer activates the timer when it exists."""
    # Act
    _activate_timer(mock_recording_manager_with_timer)

    # Assert
    mock_recording_manager_with_timer.timer.activate.assert_called_once()


def test_activate_timer_checks_callback_attribute(
    mock_recording_manager_with_timer, mock_logging_functions
):
    """Test that _activate_timer checks for callback attribute."""
    # Act
    _activate_timer(mock_recording_manager_with_timer)

    # Assert
    # Should not call critical since callback exists
    mock_logging_functions["critical"].assert_not_called()


def test_activate_timer_logs_critical_when_no_callback(
    mock_recording_manager, mock_logging_functions
):
    """Test that _activate_timer logs critical error when timer has no callback."""
    # Arrange
    mock_timer = Mock()
    del mock_timer.callback  # Remove callback attribute
    mock_recording_manager.timer = mock_timer

    # Act
    _activate_timer(mock_recording_manager)

    # Assert
    mock_logging_functions["critical"].assert_called_once_with(
        "Timer has no callback attribute!"
    )


def test_activate_timer_logs_critical_when_no_timer(
    mock_recording_manager, mock_logging_functions
):
    """Test that _activate_timer logs critical error when timer doesn't exist."""
    # Arrange
    mock_recording_manager.timer = None

    # Act
    _activate_timer(mock_recording_manager)

    # Assert
    mock_logging_functions["critical"].assert_called_once_with(
        "Timer not found on recording_manager when trying to activate!"
    )


def test_activate_timer_handles_timer_without_callback_attribute():
    """Test _activate_timer when timer exists but callback attribute doesn't."""
    # Arrange
    mock_manager = Mock()
    mock_timer = Mock()
    # Create a timer that doesn't have callback attribute
    if hasattr(mock_timer, "callback"):
        delattr(mock_timer, "callback")
    mock_manager.timer = mock_timer

    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager.critical"
    ) as mock_critical:
        # Act
        _activate_timer(mock_manager)

        # Assert
        mock_timer.activate.assert_called_once()
        mock_critical.assert_called_once_with("Timer has no callback attribute!")


def test_deactivate_timer_deactivates_existing_timer(
    mock_recording_manager_with_timer, mock_logging_functions
):
    """Test that _deactivate_timer deactivates the timer when it exists."""
    # Act
    _deactivate_timer(mock_recording_manager_with_timer)

    # Assert
    mock_recording_manager_with_timer.timer.deactivate.assert_called_once()


def test_deactivate_timer_logs_warning_when_no_timer(
    mock_recording_manager, mock_logging_functions
):
    """Test that _deactivate_timer logs warning when timer doesn't exist."""
    # Arrange
    mock_recording_manager.timer = None

    # Act
    _deactivate_timer(mock_recording_manager)

    # Assert
    mock_logging_functions["warning"].assert_called_once_with(
        "Timer not found on recording_manager when trying to deactivate"
    )


def test_deactivate_timer_handles_none_timer(mock_logging_functions):
    """Test _deactivate_timer when timer is explicitly None."""
    # Arrange
    mock_manager = Mock()
    mock_manager.timer = None

    # Act
    _deactivate_timer(mock_manager)

    # Assert
    mock_logging_functions["warning"].assert_called_once_with(
        "Timer not found on recording_manager when trying to deactivate"
    )


def test_activate_timer_with_callback_attribute_present():
    """Test _activate_timer when timer has callback attribute."""
    # Arrange
    mock_manager = Mock()
    mock_timer = Mock()
    mock_timer.callback = Mock()  # Explicitly set callback
    mock_manager.timer = mock_timer

    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager.critical"
    ) as mock_critical:
        # Act
        _activate_timer(mock_manager)

        # Assert
        mock_timer.activate.assert_called_once()
        mock_critical.assert_not_called()  # Should not log critical error


def test_setup_recording_timer_integration():
    """Test complete setup_recording_timer integration."""
    # Arrange
    mock_manager = Mock()
    mock_manager.update_duration = Mock()

    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager.ui.timer"
    ) as mock_ui_timer:
        mock_timer_instance = Mock()
        mock_ui_timer.return_value = mock_timer_instance

        # Act
        setup_recording_timer(mock_manager)

        # Assert
        # Verify timer creation
        mock_ui_timer.assert_called_once_with(1.0, mock_manager.update_duration)

        # Verify timer attachment
        assert mock_manager.timer == mock_timer_instance

        # Verify initial deactivation
        mock_timer_instance.deactivate.assert_called_once()


def test_manage_timer_state_recording_true_flow():
    """Test complete flow when is_recording is True."""
    # Arrange
    mock_manager = Mock()
    mock_timer = Mock()
    mock_timer.callback = Mock()
    mock_manager.timer = mock_timer
    mock_manager.is_recording = True

    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager.critical"
    ) as mock_critical:
        # Act
        manage_timer_state(mock_manager)

        # Assert
        mock_timer.activate.assert_called_once()
        mock_critical.assert_not_called()


def test_manage_timer_state_recording_false_flow():
    """Test complete flow when is_recording is False."""
    # Arrange
    mock_manager = Mock()
    mock_timer = Mock()
    mock_manager.timer = mock_timer
    mock_manager.is_recording = False

    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager.warning"
    ) as mock_warning:
        # Act
        manage_timer_state(mock_manager)

        # Assert
        mock_timer.deactivate.assert_called_once()
        mock_warning.assert_not_called()


def test_deactivate_timer_safe_operation():
    """Test that _deactivate_timer operates safely under various conditions."""
    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager.warning"
    ) as mock_warning:

        # Test 1: Normal operation with timer
        mock_manager1 = Mock()
        mock_timer1 = Mock()
        mock_manager1.timer = mock_timer1
        _deactivate_timer(mock_manager1)
        mock_timer1.deactivate.assert_called_once()

        # Test 2: No timer
        mock_manager2 = Mock()
        mock_manager2.timer = None
        _deactivate_timer(mock_manager2)

        # Only one warning should be called for the None timer
        mock_warning.assert_called_once()


def test_timer_interval_and_callback_precision():
    """Test that timer is created with exact specifications."""
    # Arrange
    mock_manager = Mock()
    custom_callback = Mock()
    mock_manager.update_duration = custom_callback

    with patch(
        "gojiberri.ui.components.recording.session_card.timer_manager.ui.timer"
    ) as mock_ui_timer:
        mock_timer_instance = Mock()
        mock_ui_timer.return_value = mock_timer_instance

        # Act
        setup_recording_timer(mock_manager)

        # Assert exact parameters
        mock_ui_timer.assert_called_once_with(1.0, custom_callback)
        assert mock_manager.timer is mock_timer_instance
