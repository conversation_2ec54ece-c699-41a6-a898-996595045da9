"""
Tests for async_processing module - Asynchronous operations for recording
"""

import pytest  # type: ignore
from unittest.mock import AsyncMock, Mock

# Import the function to test
from gojiberri.ui.components.recording.session_card.async_processing import (
    process_recording_toggle_async,
)


@pytest.fixture
def mock_recording_manager():
    """Create a mock recording manager for testing."""
    manager = Mock()
    manager.toggle_recording = AsyncMock()
    return manager


@pytest.mark.anyio
async def test_process_recording_toggle_success():
    """Test successful recording toggle operation."""
    # Arrange
    mock_manager = Mock()
    mock_manager.toggle_recording = AsyncMock(
        return_value=(True, "Recording started", "session_123")
    )

    # Act
    result = await process_recording_toggle_async(mock_manager, True)

    # Assert
    assert result == (True, "Recording started", "session_123")
    mock_manager.toggle_recording.assert_called_once_with(True)


@pytest.mark.anyio
async def test_process_recording_toggle_start_recording():
    """Test starting recording returns expected values."""
    # Arrange
    mock_manager = Mock()
    expected_result = (True, "Recording started successfully", "session_456")
    mock_manager.toggle_recording = AsyncMock(return_value=expected_result)

    # Act
    result = await process_recording_toggle_async(mock_manager, True)

    # Assert
    assert result[0] is True  # success
    assert isinstance(result[1], str)  # message
    assert result[2] == "session_456"  # session_id
    mock_manager.toggle_recording.assert_called_once_with(True)


@pytest.mark.asyncio
async def test_process_recording_toggle_stop_recording():
    """Test stopping recording returns expected values."""
    # Arrange
    mock_manager = Mock()
    expected_result = (True, "Recording stopped successfully", None)
    mock_manager.toggle_recording = AsyncMock(return_value=expected_result)

    # Act
    result = await process_recording_toggle_async(mock_manager, False)

    # Assert
    assert result[0] is True  # success
    assert isinstance(result[1], str)  # message
    assert result[2] is None  # session_id (None when stopping)
    mock_manager.toggle_recording.assert_called_once_with(False)


@pytest.mark.asyncio
async def test_process_recording_toggle_failure():
    """Test recording toggle operation failure."""
    # Arrange
    mock_manager = Mock()
    mock_manager.toggle_recording = AsyncMock(
        return_value=(False, "Failed to start recording", None)
    )

    # Act
    result = await process_recording_toggle_async(mock_manager, True)

    # Assert
    assert result == (False, "Failed to start recording", None)
    mock_manager.toggle_recording.assert_called_once_with(True)


@pytest.mark.asyncio
async def test_process_recording_toggle_return_type_validation():
    """Test that return values match expected types."""
    # Arrange
    mock_manager = Mock()
    mock_manager.toggle_recording = AsyncMock(
        return_value=(True, "Success", "session_789")
    )

    # Act
    result = await process_recording_toggle_async(mock_manager, True)

    # Assert
    assert isinstance(result, tuple)
    assert len(result) == 3
    assert isinstance(result[0], bool)  # success
    assert isinstance(result[1], str)  # message
    assert isinstance(result[2], (str, type(None)))  # session_id


@pytest.mark.asyncio
async def test_process_recording_toggle_with_none_session_id():
    """Test handling when session_id is None."""
    # Arrange
    mock_manager = Mock()
    mock_manager.toggle_recording = AsyncMock(
        return_value=(True, "Operation completed", None)
    )

    # Act
    result = await process_recording_toggle_async(mock_manager, False)

    # Assert
    assert result[0] is True
    assert result[1] == "Operation completed"
    assert result[2] is None


@pytest.mark.asyncio
async def test_process_recording_toggle_manager_called_correctly():
    """Test that recording manager is called with correct parameters."""
    # Arrange
    mock_manager = Mock()
    mock_manager.toggle_recording = AsyncMock(
        return_value=(True, "Test", "test_session")
    )

    # Act - Test with True
    await process_recording_toggle_async(mock_manager, True)
    mock_manager.toggle_recording.assert_called_with(True)

    # Reset mock
    mock_manager.toggle_recording.reset_mock()

    # Act - Test with False
    await process_recording_toggle_async(mock_manager, False)
    mock_manager.toggle_recording.assert_called_with(False)


@pytest.mark.asyncio
async def test_process_recording_toggle_multiple_calls():
    """Test multiple sequential calls work correctly."""
    # Arrange
    mock_manager = Mock()
    mock_manager.toggle_recording = AsyncMock(
        side_effect=[
            (True, "Started", "session_1"),
            (True, "Stopped", None),
            (True, "Started again", "session_2"),
        ]
    )

    # Act & Assert
    result1 = await process_recording_toggle_async(mock_manager, True)
    assert result1 == (True, "Started", "session_1")

    result2 = await process_recording_toggle_async(mock_manager, False)
    assert result2 == (True, "Stopped", None)

    result3 = await process_recording_toggle_async(mock_manager, True)
    assert result3 == (True, "Started again", "session_2")

    # Verify call count
    assert mock_manager.toggle_recording.call_count == 3


# Integration-style test helpers
@pytest.fixture
def recording_scenarios():
    """Provide common test scenarios for recording operations."""
    return [
        # (is_recording, expected_success, expected_message_contains)
        (True, True, "start"),
        (False, True, "stop"),
    ]


@pytest.mark.asyncio
async def test_process_recording_toggle_scenarios(recording_scenarios):
    """Test common recording scenarios."""
    for is_recording, expected_success, message_contains in recording_scenarios:
        # Arrange
        mock_manager = Mock()
        mock_manager.toggle_recording = AsyncMock(
            return_value=(expected_success, f"Recording {message_contains}ed", None)
        )

        # Act
        result = await process_recording_toggle_async(mock_manager, is_recording)

        # Assert
        assert result[0] == expected_success
        assert message_contains in result[1].lower()
