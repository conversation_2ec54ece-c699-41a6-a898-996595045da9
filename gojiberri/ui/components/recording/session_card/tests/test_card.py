"""
Tests for session_card module - Main card creation and layout
"""

import pytest  # type: ignore
from unittest.mock import Mock, patch, MagicMock

# Import functions to test
from gojiberri.ui.components.recording.session_card.card import (
    create_record_session_card,
    _create_patient_section,
)


@pytest.fixture
def mock_container():
    """Create a mock container for testing."""
    return Mock()


@pytest.fixture
def mock_ui_components():
    """Mock all UI component creation functions."""
    mocks = {}

    # Patch each imported function individually
    with patch(
        "gojiberri.ui.components.recording.session_card.card.create_card_container"
    ) as mock_card:
        with patch(
            "gojiberri.ui.components.recording.session_card.card.create_duration_display"
        ) as mock_duration:
            with patch(
                "gojiberri.ui.components.recording.session_card.card.create_recording_controls"
            ) as mock_recording:
                with patch(
                    "gojiberri.ui.components.recording.session_card.card.create_patient_info_section"
                ) as mock_patient:
                    mocks["create_card_container"] = mock_card
                    mocks["create_duration_display"] = mock_duration
                    mocks["create_recording_controls"] = mock_recording
                    mocks["create_patient_info_section"] = mock_patient
                    yield mocks


@pytest.fixture
def mock_nicegui():
    """Mock nicegui ui components."""
    with patch("gojiberri.ui.components.recording.session_card.card.ui") as mock_ui:
        mock_column = Mock()
        mock_column.classes.return_value = mock_column
        mock_ui.column.return_value = mock_column
        yield mock_ui


def test_create_record_session_card_basic_functionality(
    mock_container, mock_ui_components
):
    """Test basic functionality of create_record_session_card."""
    # Arrange
    mock_card_container = Mock()
    mock_ui_components["create_card_container"].return_value = mock_card_container
    mock_ui_components["create_duration_display"].return_value = Mock()
    mock_ui_components["create_recording_controls"].return_value = {
        "start_button": Mock(),
        "stop_button": Mock(),
    }

    with patch(
        "gojiberri.ui.components.recording.session_card.card._create_patient_section"
    ) as mock_patient:
        mock_patient.return_value = {"patient_name": Mock(), "patient_id": Mock()}

        # Act
        result = create_record_session_card(mock_container)

        # Assert
        assert isinstance(result, dict)
        assert "patient_inputs" in result
        assert "duration_display" in result
        assert "start_button" in result
        assert "stop_button" in result


def test_create_record_session_card_creates_card_container(
    mock_container, mock_ui_components
):
    """Test that card container is created with correct parameters."""
    # Arrange
    mock_card_container = Mock()
    mock_ui_components["create_card_container"].return_value = mock_card_container
    mock_ui_components["create_duration_display"].return_value = Mock()
    mock_ui_components["create_recording_controls"].return_value = {}

    with patch(
        "gojiberri.ui.components.recording.session_card.card._create_patient_section"
    ):
        # Act
        create_record_session_card(mock_container)

        # Assert
        mock_ui_components["create_card_container"].assert_called_once_with(
            mock_container
        )


def test_create_record_session_card_creates_duration_display(
    mock_container, mock_ui_components
):
    """Test that duration display is created."""
    # Arrange
    mock_duration_display = Mock()
    mock_ui_components["create_card_container"].return_value = Mock()
    mock_ui_components["create_duration_display"].return_value = mock_duration_display
    mock_ui_components["create_recording_controls"].return_value = {}

    with patch(
        "gojiberri.ui.components.recording.session_card.card._create_patient_section"
    ):
        # Act
        result = create_record_session_card(mock_container)

        # Assert
        mock_ui_components["create_duration_display"].assert_called_once()
        assert result["duration_display"] == mock_duration_display


def test_create_record_session_card_creates_recording_controls(
    mock_container, mock_ui_components
):
    """Test that recording controls are created with components parameter."""
    # Arrange
    mock_recording_controls = {"record_btn": Mock(), "stop_btn": Mock()}
    mock_ui_components["create_card_container"].return_value = Mock()
    mock_ui_components["create_duration_display"].return_value = Mock()
    mock_ui_components["create_recording_controls"].return_value = (
        mock_recording_controls
    )

    with patch(
        "gojiberri.ui.components.recording.session_card.card._create_patient_section"
    ):
        # Act
        result = create_record_session_card(mock_container)

        # Assert
        # Verify create_recording_controls was called with components dict
        call_args = mock_ui_components["create_recording_controls"].call_args[0][0]
        assert isinstance(call_args, dict)
        assert "patient_inputs" in call_args
        assert "duration_display" in call_args

        # Verify recording controls are added to result
        assert result["record_btn"] == mock_recording_controls["record_btn"]
        assert result["stop_btn"] == mock_recording_controls["stop_btn"]


def test_create_record_session_card_creates_patient_section(
    mock_container, mock_ui_components
):
    """Test that patient section is created."""
    # Arrange
    mock_patient_components = {"name_input": Mock(), "id_input": Mock()}
    mock_ui_components["create_card_container"].return_value = Mock()
    mock_ui_components["create_duration_display"].return_value = Mock()
    mock_ui_components["create_recording_controls"].return_value = {}

    with patch(
        "gojiberri.ui.components.recording.session_card.card._create_patient_section"
    ) as mock_patient:
        mock_patient.return_value = mock_patient_components

        # Act
        result = create_record_session_card(mock_container)

        # Assert
        mock_patient.assert_called_once()
        assert result["patient_inputs"] == mock_patient_components


def test_create_record_session_card_context_manager_usage(
    mock_container, mock_ui_components
):
    """Test that card container is used as context manager."""
    # Arrange
    mock_card_container = MagicMock()
    mock_ui_components["create_card_container"].return_value = mock_card_container
    mock_ui_components["create_duration_display"].return_value = Mock()
    mock_ui_components["create_recording_controls"].return_value = {}

    with patch(
        "gojiberri.ui.components.recording.session_card.card._create_patient_section"
    ):
        # Act
        create_record_session_card(mock_container)

        # Assert
        mock_card_container.__enter__.assert_called_once()
        mock_card_container.__exit__.assert_called_once()


def test_create_record_session_card_return_structure(
    mock_container, mock_ui_components
):
    """Test the structure of returned components dictionary."""
    # Arrange
    mock_patient_inputs = {"patient_name": Mock()}
    mock_duration_display = Mock()
    mock_recording_controls = {"start_btn": Mock(), "pause_btn": Mock()}

    mock_ui_components["create_card_container"].return_value = Mock()
    mock_ui_components["create_duration_display"].return_value = mock_duration_display
    mock_ui_components["create_recording_controls"].return_value = (
        mock_recording_controls
    )

    with patch(
        "gojiberri.ui.components.recording.session_card.card._create_patient_section"
    ) as mock_patient:
        mock_patient.return_value = mock_patient_inputs

        # Act
        result = create_record_session_card(mock_container)

        # Assert
        expected_keys = {"patient_inputs", "duration_display", "start_btn", "pause_btn"}
        assert set(result.keys()) == expected_keys
        assert result["patient_inputs"] == mock_patient_inputs
        assert result["duration_display"] == mock_duration_display


def test_create_patient_section_creates_ui_column(mock_nicegui, mock_ui_components):
    """Test that _create_patient_section creates UI column with correct classes."""
    # Arrange
    mock_column = Mock()
    mock_nicegui.column.return_value = mock_column
    mock_column.classes.return_value = mock_column
    mock_ui_components["create_patient_info_section"].return_value = {"name": Mock()}

    # Act
    _create_patient_section()

    # Assert
    mock_nicegui.column.assert_called_once()
    mock_column.classes.assert_called_once_with("w-full test-patient")


def test_create_patient_section_calls_create_patient_info_section(
    mock_nicegui, mock_ui_components
):
    """Test that _create_patient_section calls create_patient_info_section."""
    # Arrange
    mock_column = Mock()
    mock_nicegui.column.return_value = mock_column
    mock_column.classes.return_value = mock_column
    expected_result = {"patient_name": Mock(), "patient_age": Mock()}
    mock_ui_components["create_patient_info_section"].return_value = expected_result

    # Act
    result = _create_patient_section()

    # Assert
    mock_ui_components["create_patient_info_section"].assert_called_once_with(
        mock_column
    )
    assert result == expected_result


def test_create_patient_section_return_value(mock_nicegui, mock_ui_components):
    """Test that _create_patient_section returns patient info components."""
    # Arrange
    mock_column = Mock()
    mock_nicegui.column.return_value = mock_column
    mock_column.classes.return_value = mock_column
    expected_components = {
        "name_input": Mock(),
        "age_input": Mock(),
        "id_input": Mock(),
    }
    mock_ui_components["create_patient_info_section"].return_value = expected_components

    # Act
    result = _create_patient_section()

    # Assert
    assert result == expected_components
    assert len(result) == 3
    assert "name_input" in result
    assert "age_input" in result
    assert "id_input" in result


def test_create_patient_section_context_manager_usage(mock_nicegui, mock_ui_components):
    """Test that patient section uses column as context manager."""
    # Arrange
    mock_column = MagicMock()
    mock_nicegui.column.return_value = mock_column
    mock_column.classes.return_value = mock_column
    mock_ui_components["create_patient_info_section"].return_value = {}

    # Act
    _create_patient_section()

    # Assert
    mock_column.__enter__.assert_called_once()
    mock_column.__exit__.assert_called_once()


def test_create_record_session_card_integration_flow(
    mock_container, mock_ui_components, mock_nicegui
):
    """Test the complete integration flow of create_record_session_card."""
    # Arrange
    mock_card_container = MagicMock()
    mock_patient_column = MagicMock()
    mock_duration = Mock()
    mock_recording = {"rec_btn": Mock()}
    mock_patient_info = {"patient": Mock()}

    mock_ui_components["create_card_container"].return_value = mock_card_container
    mock_ui_components["create_duration_display"].return_value = mock_duration
    mock_ui_components["create_recording_controls"].return_value = mock_recording
    mock_ui_components["create_patient_info_section"].return_value = mock_patient_info

    mock_nicegui.column.return_value = mock_patient_column
    mock_patient_column.classes.return_value = mock_patient_column

    # Act
    result = create_record_session_card(mock_container)

    # Assert - verify call sequence
    mock_ui_components["create_card_container"].assert_called_once_with(mock_container)
    mock_nicegui.column.assert_called_once()
    mock_patient_column.classes.assert_called_once_with("w-full test-patient")
    mock_ui_components["create_patient_info_section"].assert_called_once_with(
        mock_patient_column
    )
    mock_ui_components["create_duration_display"].assert_called_once()
    mock_ui_components["create_recording_controls"].assert_called_once()

    # Assert - verify result structure
    assert result["patient_inputs"] == mock_patient_info
    assert result["duration_display"] == mock_duration
    assert result["rec_btn"] == mock_recording["rec_btn"]


def test_create_record_session_card_empty_recording_controls(
    mock_container, mock_ui_components
):
    """Test handling when recording controls return empty dict."""
    # Arrange
    mock_ui_components["create_card_container"].return_value = Mock()
    mock_ui_components["create_duration_display"].return_value = Mock()
    mock_ui_components["create_recording_controls"].return_value = {}  # Empty dict

    with patch(
        "gojiberri.ui.components.recording.session_card.card._create_patient_section"
    ) as mock_patient:
        mock_patient.return_value = {"patient": Mock()}

        # Act
        result = create_record_session_card(mock_container)

        # Assert
        assert "patient_inputs" in result
        assert "duration_display" in result
        # Should not have any recording control keys since empty dict was returned
        assert (
            len(
                [
                    k
                    for k in result.keys()
                    if k not in ["patient_inputs", "duration_display"]
                ]
            )
            == 0
        )


def test_create_record_session_card_multiple_recording_controls(
    mock_container, mock_ui_components
):
    """Test handling multiple recording controls."""
    # Arrange
    mock_recording_controls = {
        "start_button": Mock(),
        "stop_button": Mock(),
        "pause_button": Mock(),
        "status_indicator": Mock(),
    }
    mock_ui_components["create_card_container"].return_value = Mock()
    mock_ui_components["create_duration_display"].return_value = Mock()
    mock_ui_components["create_recording_controls"].return_value = (
        mock_recording_controls
    )

    with patch(
        "gojiberri.ui.components.recording.session_card.card._create_patient_section"
    ) as mock_patient:
        mock_patient.return_value = {"patient": Mock()}

        # Act
        result = create_record_session_card(mock_container)

        # Assert
        for key, component in mock_recording_controls.items():
            assert result[key] == component


def test_components_passed_to_recording_controls(mock_container, mock_ui_components):
    """Test that components dict is properly built before passing to recording controls."""
    # Arrange
    mock_patient_inputs = {"name": Mock()}
    mock_duration_display = Mock()

    mock_ui_components["create_card_container"].return_value = Mock()
    mock_ui_components["create_duration_display"].return_value = mock_duration_display
    mock_ui_components["create_recording_controls"].return_value = {}

    with patch(
        "gojiberri.ui.components.recording.session_card.card._create_patient_section"
    ) as mock_patient:
        mock_patient.return_value = mock_patient_inputs

        # Act
        create_record_session_card(mock_container)

        # Assert
        call_args = mock_ui_components["create_recording_controls"].call_args[0][0]
        assert call_args["patient_inputs"] == mock_patient_inputs
        assert call_args["duration_display"] == mock_duration_display
