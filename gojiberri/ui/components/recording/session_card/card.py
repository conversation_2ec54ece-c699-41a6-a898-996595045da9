"""
Session card - Main card creation and layout
"""

from typing import Dict, Any
from nicegui import ui
from .ui_components import (
    create_card_container,
    create_duration_display,
)
from .recording_controls import create_recording_controls
from gojiberri.ui.components.recording.patient_info import create_patient_info_section


def create_record_session_card(container) -> Dict[str, Any]:
    """
    Create the record session card with patient info and recording controls.

    Args:
        container: UI container element

    Returns:
        Dictionary of UI components
    """
    components: Dict[str, Any] = {}

    # Create main card container
    card_container = create_card_container(container)

    with card_container:
        # Patient input section
        components["patient_inputs"] = _create_patient_section()

        # Create duration display only
        components["duration_display"] = create_duration_display()

        # Create recording controls
        recording_components = create_recording_controls(components)
        components.update(recording_components)

    return components


def _create_patient_section() -> Dict[str, Any]:
    """
    Create the patient information section.

    Returns:
        Patient input components
    """
    with ui.column().classes("w-full test-patient") as patient_section:
        return create_patient_info_section(patient_section)
