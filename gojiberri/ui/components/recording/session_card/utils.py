"""
Utilities - Helper functions for session card operations
"""

from typing import Dict, Any


def extract_patient_info(components: Dict[str, Any]) -> tuple[str, str]:
    """
    Extract patient information from components.

    Args:
        components: Dictionary of UI components

    Returns:
        Tuple of (patient_name, patient_dob)
    """
    try:
        patient_name = components["patient_inputs"]["name"].value
        patient_dob = components["patient_inputs"]["dob"].value
        return patient_name, patient_dob
    except (<PERSON><PERSON><PERSON><PERSON>, AttributeError):
        return "", ""
