# components/recording/session_card/recording_controls.py
"""
Recording controls with manager-driven state approach.

Creates recording button that reflects manager state without internal toggle logic.
"""

from typing import Dict, Any
from nicegui import ui
from .ui_components import create_record_button
from .handlers import create_recording_click_handler
from .timer_manager import setup_recording_timer
from gojiberri.ui.components.recording.recording_manager import create_recording_manager


def create_recording_controls(components: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create recording controls with manager-driven state management.

    Args:
        components: Dictionary of existing UI components

    Returns:
        Dictionary of recording control components
    """
    recording_components = {}

    with ui.column().classes("items-center gap-2"):
        # Create recording manager
        recording_manager = create_recording_manager(
            components=components, duration_display=components["duration_display"]
        )

        # Setup timer for recording manager
        setup_recording_timer(recording_manager)

        # Create click handler that reads from manager state
        click_handler = create_recording_click_handler(components, recording_manager)

        # Create button without toggle behavior
        record_button = create_record_button(click_handler)

        # Store components
        recording_components.update(
            {
                "recording_manager": recording_manager,
                "record_button": record_button,
            }
        )

    return recording_components
