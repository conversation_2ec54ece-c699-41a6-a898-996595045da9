"""
Timer management - Timer setup and control utilities
"""

from nicegui import ui
from gojiberri.utils.logger import warning, critical


def setup_recording_timer(recording_manager):
    """
    Setup timer for recording duration tracking.

    Args:
        recording_manager: Recording manager instance
    """
    # Create timer and attach to recording manager
    recording_manager.timer = ui.timer(1.0, recording_manager.update_duration)
    recording_manager.timer.deactivate()  # Initially deactivated


def manage_timer_state(recording_manager):
    """
    Manage timer state based on recording status.

    Args:
        recording_manager: Recording manager instance
    """
    if recording_manager.is_recording:
        _activate_timer(recording_manager)
    else:
        _deactivate_timer(recording_manager)


def _activate_timer(recording_manager):
    """
    Activate the recording timer.

    Args:
        recording_manager: Recording manager instance
    """

    if recording_manager.timer:
        recording_manager.timer.activate()
        # Verify timer callback
        if not hasattr(recording_manager.timer, "callback"):
            critical("Timer has no callback attribute!")

    else:
        critical("Timer not found on recording_manager when trying to activate!")


def _deactivate_timer(recording_manager):
    """
    Deactivate the recording timer.

    Args:
        recording_manager: Recording manager instance
    """

    if recording_manager.timer:
        recording_manager.timer.deactivate()
    else:
        warning("Timer not found on recording_manager when trying to deactivate")
