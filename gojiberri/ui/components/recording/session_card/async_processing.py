"""
Async processing - Asynchronous operations for recording
"""

from typing import <PERSON><PERSON>, Optional
from gojiberri.utils.logger import error
from gojiberri.utils.logger import log_exception


async def process_recording_toggle_async(
    recording_manager, is_recording: bool
) -> Tuple[bool, str, Optional[str]]:
    """
    Process recording toggle asynchronously.

    Args:
        recording_manager: Recording manager instance
        is_recording: Target recording state

    Returns:
        Tuple of (success, message, session_id)
    """

    try:
        result = await recording_manager.toggle_recording(is_recording)
        return result

    except Exception as e:
        log_exception(e, "Exception Error")
        error(f"Error in toggle_recording: {e}", title="Exception")
        import traceback

        error(traceback.format_exc())
        return False, f"Error: {str(e)}", None
