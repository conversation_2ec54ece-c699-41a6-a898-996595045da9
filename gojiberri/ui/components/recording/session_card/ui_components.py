# components/recording/session_card/ui_components.py
"""
Clean UI components - STATUS DISPLAY REMOVED
"""

from nicegui import ui


def create_card_container(container):
    """
    Create the main card container with styling.

    Args:
        container: Parent container element

    Returns:
        Styled card container
    """
    return container.classes("w-full md:p-4 p-2 shadow-none").style(
        "max-width: 360px; background-color: rgba(255, 255, 255, 0);"
    )


def create_duration_display():
    """
    Create the duration display label.

    Returns:
        Duration display UI element
    """
    return ui.label("Duration: 00:00:00").classes("text-center mb-2 mt-4 test-duration")


def create_record_button(toggle_handler):
    """
    Create the recording toggle button.

    Args:
        toggle_handler: Function to handle toggle events

    Returns:
        Recording button UI element
    """
    from gojiberri.ui.components import create_styled_button

    return create_styled_button(
        button_text="Start Recording",
        icon="play_arrow",
        toggle_behavior=False,
        toggle_texts=("Start Recording", "Pause Recording"),
        toggle_icons=("play_arrow", "pause"),
        initial_state=False,
        on_click_callback=toggle_handler,
        additional_classes="mt-2",
    )
