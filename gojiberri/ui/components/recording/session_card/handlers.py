# components/recording/session_card/handlers.py
"""
Simplified event handlers using manager-driven state management.

Handlers determine target state from manager's current state, not button state.
Recording manager is the single source of truth for all state decisions.
"""

from typing import Dict, Any, Callable
from nicegui import ui
from gojiberri.ui.components.recording.session_card.ui_updates import (
    _update_ui_after_recording_operation,
)
from gojiberri.utils.logger import (
    debug,
    log_exception,
)
from gojiberri.ui.utils import safe_show_notification

from .utils import extract_patient_info


def create_recording_click_handler(
    components: Dict[str, Any], recording_manager
) -> Callable:
    """
    Create recording click handler that reads manager state to determine action.

    Uses manager as single source of truth - no button state synchronization needed.

    Args:
        components: Dictionary of UI components
        recording_manager: Recording manager instance

    Returns:
        Click handler function
    """

    async def handle_recording_click(ignored_button_param=None):
        """
        Handle recording button click by reading manager state.

        Determines target action based on manager's current state, not button state.
        Always toggles the manager's current state.

        Args:
            ignored_button_param: Ignored parameter from button click
        """
        try:
            # Read current state from manager (single source of truth)
            current_recording = recording_manager.is_recording
            target_state = not current_recording  # Always toggle manager state

            debug(
                f"Click handler: manager_recording={current_recording} → target={target_state}"
            )

            # Validate before starting recording
            if target_state and not recording_manager.recording_started:
                if not _validate_before_recording(components):
                    debug("Validation failed - click ignored")
                    return

            # Process the recording operation
            success, message, session_id = await recording_manager.toggle_recording(
                target_state
            )

            # Always update UI components after operation
            _update_ui_after_recording_operation(components, recording_manager)

            # Show result
            if not success:
                safe_show_notification(f"Recording error: {message}", type="negative")

        except Exception as e:
            log_exception(e, "Error in recording click handler")
            safe_show_notification(f"Recording failed: {str(e)}", type="negative")

            # Ensure UI reflects manager state after error
            _update_ui_after_recording_operation(components, recording_manager)

    return handle_recording_click


def _validate_before_recording(components: Dict[str, Any]) -> bool:
    """
    Validate patient information before starting recording.

    Args:
        components: Dictionary of UI components

    Returns:
        bool: True if validation passes
    """
    # Extract patient information
    patient_name, patient_dob = extract_patient_info(components)

    # Validate DOB
    if not patient_dob.strip():
        safe_show_notification(
            "Please enter patient date of birth before recording",
            type="negative",
        )
        _highlight_field_error(components, "dob")
        return False

    # Validate name
    if not patient_name.strip():
        safe_show_notification(
            "Please enter patient name before recording", type="negative"
        )
        _highlight_field_error(components, "name")
        return False

    # Validate date format and future date
    try:
        from datetime import datetime

        dob_date = datetime.strptime(patient_dob.strip(), "%Y-%m-%d")
        if dob_date > datetime.now():
            safe_show_notification(
                "Date of birth cannot be in the future", type="negative"
            )
            _highlight_field_error(components, "dob")
            return False
    except ValueError:
        safe_show_notification(
            "Invalid date format. Please use YYYY-MM-DD.", type="negative"
        )
        _highlight_field_error(components, "dob")
        return False

    # Clear validation errors on success
    _clear_validation_errors(components)
    return True


def _highlight_field_error(components: Dict[str, Any], field_name: str):
    """Highlight field with error styling."""
    try:
        field_component = components.get("patient_inputs", {}).get(field_name)
        if field_component:
            field_component.style("border-bottom: 1px solid red;").props("color='red'")

        # Force UI refresh for validation styling
        ui.update()
    except Exception as e:
        log_exception(e, "Error highlighting field error")


def _clear_validation_errors(components: Dict[str, Any]):
    """Clear validation error styling."""
    try:
        for field_name in ["name", "dob"]:
            field_component = components.get("patient_inputs", {}).get(field_name)
            if field_component:
                field_component.style("border-bottom: 1px solid gray;").props(
                    "color='black'"
                )

        # Force UI refresh for validation styling
        ui.update()
    except Exception as e:
        log_exception(e, "Error clearing validation errors")
