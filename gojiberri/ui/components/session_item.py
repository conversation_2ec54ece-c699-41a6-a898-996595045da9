"""
Session item component for the Neurology Tool application.
"""

from nicegui import ui
from typing import Dict, Optional, Callable

from gojiberri.ui.config import ICONS


def create_in_progress_session_item(
    session_data: Dict, on_click: Optional[Callable] = None
) -> ui.row:
    """
    Create an in-progress session item.

    Args:
        session_data: Dictionary containing session information
        on_click: Callback function for item click

    Returns:
        ui.row: The created session item
    """
    with ui.row().classes(
        "w-full mt-[-10px] p-1 sidebar-item rounded cursor-pointer gap-0"
    ) as session_item:
        ui.icon(ICONS["bin_icon"]).classes("text-black text-[20px]")

        with ui.column().classes("ml-2 flex-grow"):
            # Name and Time in one row, truncating if name is too long
            with ui.row().classes("items-center overflow-hidden"):
                ui.label(session_data["name"]).classes(
                    "text-sm font-medium text-black truncate"
                )  # Add truncate to name
                ui.label(session_data["time"]).classes(
                    "text-xs text-gray-400 ml-2"
                )  # Time beside name with margin

            # Status row with icon and label
            with ui.row().classes("flex items-center justify-center h-full gap-2"):
                ui.icon(session_data["status_icon"]).classes(
                    f"text-[14px] {session_data['status_color']}"
                )
                ui.label(session_data["status"]).classes(
                    f"text-xs {session_data['status_color']}"
                )

            # Optional awaiting input label
            if session_data.get("awaiting_input"):
                ui.label("( ! Awaiting Input )").classes("text-xs text-red-500")

    if on_click:
        session_item.on("click", on_click)

    return session_item


def create_completed_session_item(
    session_data: Dict, on_click: Optional[Callable] = None
) -> ui.row:
    """
    Create a completed session item.

    Args:
        session_data: Dictionary containing session information
        on_click: Callback function for item click

    Returns:
        ui.row: The created session item
    """
    with ui.row().classes(
        "w-full mt-[-10px] p-1 sidebar-item rounded cursor-pointer gap-0"
    ) as session_item:
        ui.icon(ICONS["bin_icon"]).classes("text-black text-[20px]")
        with ui.column().classes("ml-2 flex-grow"):
            # Name and Time in one row, truncating if name is too long
            with ui.row().classes("items-center overflow-hidden"):
                ui.label(session_data["name"]).classes(
                    "text-sm font-medium text-black truncate"
                )  # Add truncate to name
                ui.label(session_data["time"]).classes("text-xs text-gray-400 ml-2")
    if on_click:
        session_item.on("click", on_click)

    return session_item
