# components/loading_spinner.py
"""
Pure inline styles loading spinner - no external CSS dependencies.
"""

import asyncio
from typing import Optional, Callable
from nicegui import ui
from gojiberri.utils.logger import debug, error, log_exception


class LoadingSpinner:
    """
    Loading spinner using only inline styles - guaranteed to work.
    """

    _instance_counter = 0

    def __init__(
        self,
        size: int = 120,
        show_percentage: bool = True,
        animation_duration: float = 1.0,
        text_color: str = "#000000",
        on_complete: Optional[Callable] = None,
    ):
        """
        Initialize the loading spinner.

        Args:
            size: Spinner size in pixels
            show_percentage: Whether to show percentage text
            animation_duration: Animation cycle duration in seconds
            text_color: Color of percentage text
            on_complete: Callback when loading reaches 100%
        """
        LoadingSpinner._instance_counter += 1
        self.instance_id = f"spinner-{LoadingSpinner._instance_counter}"

        self.size = size
        self.show_percentage = show_percentage
        self.animation_duration = animation_duration
        self.text_color = text_color
        self.on_complete = on_complete

        self.percentage = 0
        self.polling_task = None
        self.is_complete = False

        # UI elements
        self.container = None
        self.percentage_label = None
        self.bars = []

        self._create_ui()

    def _create_ui(self):
        """Create the spinner UI with pure inline styles."""
        # Create unique keyframe animation for this instance
        animation_name = f"spinner-pulse-{self.instance_id}"

        # Add keyframes as a style element (most reliable way)
        keyframes_html = f"""
        <style>
        @keyframes {animation_name} {{
            0%, 80%, 100% {{ 
                background-color: #000000;
                opacity: 1;
            }}
            40% {{ 
                background-color: #333333;
                opacity: 0.4;
            }}
        }}
        </style>
        """
        ui.html(keyframes_html)

        # Container with inline styles
        container_styles = f"""
            position: relative;
            width: {self.size}px;
            height: {self.size}px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        """

        self.container = ui.element("div").style(container_styles)

        with self.container:
            # Calculate bar dimensions based on size
            bar_width = max(3, int(self.size * 0.08))
            bar_height = max(15, int(self.size * 0.20))
            radius = int(self.size * 0.35)

            # Create 8 spinner bars with individual inline styles
            for i in range(8):
                angle = i * 45  # 0, 45, 90, 135, 180, 225, 270, 315 degrees
                delay = i * (self.animation_duration / 8)

                # Calculate position using trigonometry
                import math

                radian = math.radians(angle)
                x_offset = radius * math.sin(radian)
                y_offset = -radius * math.cos(radian)

                # Complete inline styles for each bar
                bar_styles = f"""
                    position: absolute;
                    width: {bar_width}px;
                    height: {bar_height}px;
                    background-color: #000000;
                    border-radius: calc({self.size}px * 0.03);
                    left: 50%;
                    top: 50%;
                    margin-left: -{bar_width//2}px;
                    margin-top: -{bar_height//2}px;
                    transform: translate({x_offset:.1f}px, {y_offset:.1f}px) rotate({angle}deg);
                    animation: {animation_name} {self.animation_duration}s infinite ease-in-out;
                    animation-delay: {delay:.3f}s;
                    transform-origin: center;
                """

                bar = ui.element("div").style(bar_styles)
                self.bars.append(bar)

            # Percentage display with inline styles
            if self.show_percentage:
                percentage_styles = f"""
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: {max(12, int(self.size * 0.12))}px;
                    font-weight: bold;
                    color: {self.text_color};
                    z-index: 100;
                    text-align: center;
                    pointer-events: none;
                    line-height: 1;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                """

                self.percentage_label = ui.label("0%").style(percentage_styles)

    def set_percentage(self, value: int):
        """Update the percentage value."""
        self.percentage = max(0, min(100, value))

        if self.percentage_label:
            self.percentage_label.set_text(f"{self.percentage}%")

        # Check for completion
        if self.percentage >= 100 and not self.is_complete:
            self.is_complete = True
            if self.on_complete:
                try:
                    self.on_complete()
                except Exception as e:
                    log_exception(e, "Error in completion callback")

    async def start_polling(
        self, poll_function: Callable, interval: float = 0.5, timeout: float = 300.0
    ):
        """
        Start polling for progress updates.

        Args:
            poll_function: Async function that returns current percentage
            interval: Polling interval in seconds
            timeout: Maximum polling time in seconds
        """
        self.reset()

        async def _polling_task():
            try:
                start_time = asyncio.get_event_loop().time()

                while self.percentage < 100:
                    if asyncio.get_event_loop().time() - start_time > timeout:
                        error("Polling timeout reached")
                        break

                    try:
                        current_percentage = await poll_function()
                        if isinstance(current_percentage, (int, float)):
                            self.set_percentage(int(current_percentage))

                        if self.percentage >= 100:
                            break

                    except Exception as e:
                        log_exception(e, "Error during polling")
                        await asyncio.sleep(interval * 2)  # Wait longer on error
                        continue

                    await asyncio.sleep(interval)

            except asyncio.CancelledError:
                debug("Polling task cancelled")
            except Exception as e:
                log_exception(e, "Error in polling task")

        # Cancel existing task
        if self.polling_task:
            self.polling_task.cancel()

        self.polling_task = asyncio.create_task(_polling_task())

    def stop_polling(self):
        """Stop the polling task."""
        if self.polling_task:
            self.polling_task.cancel()
            self.polling_task = None

    def reset(self):
        """Reset the spinner to 0%."""
        self.percentage = 0
        self.is_complete = False
        self.stop_polling()

        if self.percentage_label:
            self.percentage_label.set_text("0%")

    def hide(self):
        """Hide the spinner."""
        if self.container:
            self.container.style("display: none;")

    def show(self):
        """Show the spinner."""
        if self.container:
            self.container.style("display: flex;")


def create_loading_spinner(
    size: int = 120,
    show_percentage: bool = True,
    animation_duration: float = 1.0,
    text_color: str = "#000000",
    on_complete: Optional[Callable] = None,
) -> LoadingSpinner:
    """
    Create a loading spinner component with pure inline styles.

    Args:
        size: Spinner size in pixels
        show_percentage: Whether to show percentage text
        animation_duration: Animation cycle duration in seconds
        text_color: Color of percentage text
        on_complete: Callback when loading reaches 100%

    Returns:
        LoadingSpinner instance
    """
    return LoadingSpinner(
        size=size,
        show_percentage=show_percentage,
        animation_duration=animation_duration,
        text_color=text_color,
        on_complete=on_complete,
    )
