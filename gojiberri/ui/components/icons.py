"""
Icon component definitions for the Neurology Tool application.
"""

from nicegui import ui


def create_connector_line() -> ui.element:  # noqa: F811
    """
    Create a connector line between workflow buttons with appropriate styling.

    Args:
        status: The status of the line ('current', 'completed', or 'pending')

    Returns:
        ui.element: The created connector line
    """
    # Determine styling based on status
    # if status == "completed":
    #     color_class = "bg-blue-500"
    # else:  # current or pending
    color_class = "bg-gray-300"

    # Create the connector line
    with ui.element("div").classes(f"h-0.5 {color_class} flex-grow test-line") as line:
        pass

    return line
