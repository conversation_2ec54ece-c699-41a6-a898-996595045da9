"""
components/loading.py - Simple reusable loading component with CSS styling
"""

from nicegui import ui


def create_loading_overlay(
    container=None,
    message: str = "Loading...",
    spinner_size: str = "lg",
    full_screen: bool = False,
) -> dict:
    """
    Create a loading overlay with spinner and optional message.

    Args:
        container: Container to add the loading overlay to (None for current container)
        message: Message to display under the spinner
        spinner_size: Size of the spinner ("sm", "md", "lg", "xl")
        full_screen: Whether to make the overlay cover the full screen

    Returns:
        Dictionary with element references and control functions
    """
    # Create container if not provided
    if container is None:
        container = ui.element("div")

    # Map spinner size to dimensions
    size_map = {
        "sm": "24px",
        "md": "32px",
        "lg": "48px",
        "xl": "64px",
    }
    spinner_size_px = size_map.get(spinner_size, "48px")

    # Define position style based on full_screen setting
    position_style = (
        "position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 9999;"
        if full_screen
        else "position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 100;"
    )

    # Create the loading overlay - initially hidden
    with container:
        with ui.element("div").style(
            f"{position_style} display: none; align-items: center; justify-content: center; background-color: rgba(255, 255, 255, 0.8);"
        ) as overlay:
            with ui.card().style(
                "padding: 1.5rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); background-color: white;"
            ):
                with ui.column().style("align-items: center; gap: 1rem;"):
                    # Spinner (using material icon with custom animation)
                    with ui.element("div").style(
                        f"width: {spinner_size_px}; height: {spinner_size_px};"
                    ).classes("custom-spinner"):
                        ui.icon("autorenew").style(
                            f"font-size: {spinner_size_px}; color: #3B82F6;"
                        )

                    # Message label
                    message_label = ui.label(message).style(
                        "color: #374151; font-weight: 500;"
                    )

    # Control functions
    def show():
        """Show the loading overlay"""
        overlay.style("display: flex;")

    def hide():
        """Hide the loading overlay"""
        overlay.style("display: none;")

    def set_message(new_message):
        """Update the message text"""
        message_label.set_text(new_message)

    # Return object with references and control functions
    loading = {
        "overlay": overlay,
        "message_label": message_label,
        "show": show,
        "hide": hide,
        "set_message": set_message,
    }

    return loading


def create_page_loader() -> dict:
    """
    Create a full-screen page loader.

    Returns:
        Dictionary with element references and control functions
    """
    return create_loading_overlay(
        message="Loading page...", spinner_size="xl", full_screen=True
    )
