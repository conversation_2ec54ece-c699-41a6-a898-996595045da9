"""
Button component definitions for the Neurology Tool application.
"""

import asyncio
from nicegui import ui
from typing import Callable, Optional
from gojiberri.utils.logger import debug, log_exception


def create_new_session_button(on_click: Optional[Callable] = None) -> ui.button:
    """
    Create a new session button.

    Args:
        on_click: Callback function for button click

    Returns:
        ui.button: The created button
    """
    with ui.row().classes("w-full justify-center"):  # centers the button in the row
        with (
            ui.button()
            .style("border: 2px solid black;")
            .classes("w-52 bg-white rounded hover:bg-gray-50") as button
        ):
            ui.label("+ New Session").classes("text-gray-700 text-center p-1")

    if on_click:
        button.on("click", on_click)

    return button


def create_workflow_button(
    icon: str,
    status: str,
    on_click: Optional[Callable] = None,
    step_name: Optional[str] = None,
) -> ui.button:
    """
    Create a responsive circular workflow step button.

    Args:
        icon: Material icon name to display
        status: Status of the step ('current', 'completed', or 'pending')
        on_click: Optional callback function when button is clicked
        step_name: Optional step name for click handling

    Returns:
        ui.button: The created button
    """

    # Status-based color logic
    if status == "current":
        border_color = "black"
        bg_color = "bg-white"
        text_color = "text-black"
    else:  # pending or completed
        border_color = "#D1D5DB"  # Tailwind gray-300
        bg_color = "bg-white"
        text_color = "text-gray-500"

    with (
        ui.button()
        .style(f"border: 4px solid {border_color};")
        .classes(
            f"rounded-full flex items-center justify-center "
            f"{bg_color} hover:bg-gray-100 "
            f"w-16 h-16 text-2xl md:w-12 md:h-12 md:text-xl test-button"
        ) as button
    ):
        ui.icon(icon).classes(f"{text_color} md:text-xl")

    # Handle button click
    if step_name:
        # Simple click handler that just calls the callback with step_name
        async def handle_workflow_click():
            """Handle workflow button click."""
            try:
                debug(f"Workflow button clicked: {step_name}")

                if on_click:
                    # Call the provided callback with step_name
                    if asyncio.iscoroutinefunction(on_click):
                        await on_click(step_name)
                    else:
                        on_click(step_name)

            except Exception as e:
                log_exception(e, f"Error handling workflow click for {step_name}")

        button.on("click", handle_workflow_click)

    elif on_click:
        # Direct callback assignment
        if asyncio.iscoroutinefunction(on_click):
            button.on("click", on_click)
        else:
            button.on("click", lambda: on_click())

    return button


def create_profile_button(on_click: Optional[Callable] = None) -> ui.link:
    """
    Create a responsive profile link styled like a button with a circular gray icon.

    Args:
        on_click: Callback function for link click

    Returns:
        ui.link: The styled, responsive link
    """
    with ui.row().classes("w-full justify-between  m-2"):
        ui.label("").classes("w-8")
        with ui.link(target="#").classes(
            "bg-transparent px-2 py-1 hover:bg-gray-100 flex-shrink-0 mt-[-20px] no-underline"
        ) as link:
            with ui.row().classes("items-center gap-2"):
                ui.icon("person").classes(
                    "bg-gray-500 text-white p-1 rounded-full text-sm"
                )
                ui.label("Profile").classes("text-black text-sm truncate no-underline")

        if on_click:
            link.on("click", on_click)

    return link


def create_styled_button(
    button_text: str,
    icon: Optional[str] = None,
    width_class: str = "w-52",
    on_click_callback: Optional[Callable] = None,
    toggle_behavior: bool = False,
    toggle_texts: Optional[tuple[str, str]] = None,
    toggle_icons: Optional[tuple[str, str]] = None,
    initial_state: bool = False,
    additional_classes: str = "",
    additional_styles: str = "",
    use_default_styling: bool = True,
) -> ui.button:
    """
    Create a styled button with customizable properties.
    Now supports async on_click callbacks.

    Args:
        button_text: Text to display on the button
        icon: Optional icon to display (None for no icon)
        width_class: CSS width class (default: "w-52")
        on_click_callback: Callback function to execute when button is clicked (can be async)
        toggle_behavior: Whether the button should toggle between two states
        toggle_texts: Tuple of (inactive_text, active_text) for toggle buttons
        toggle_icons: Tuple of (inactive_icon, active_icon) for toggle buttons
        initial_state: Initial state for toggle buttons (False=inactive, True=active)
        additional_classes: Additional CSS classes to apply to the button
        additional_styles: Additional inline styles to apply to the button
        use_default_styling: Whether to apply the default styling (can be disabled for completely custom styling)

    Returns:
        The button element
    """
    from gojiberri.utils.logger import error as log_error

    button_state = {"active": initial_state}

    # For toggle buttons, use the appropriate initial text/icon based on state
    if toggle_behavior and toggle_texts:
        button_text = toggle_texts[1] if button_state["active"] else toggle_texts[0]

    if toggle_behavior and toggle_icons:
        icon = toggle_icons[1] if button_state["active"] else toggle_icons[0]

    # Create the button with or without an icon
    if icon:
        button = ui.button(button_text, icon=icon)
    else:
        button = ui.button(button_text)

    # Apply styling
    if use_default_styling:
        # Apply default styling
        base_classes = f"{width_class} !bg-[#EEEEEE] text-black rounded-l normal-case"
        base_styles = "border: 2px solid black; text-transform: none;"

        # Combine default and additional styling
        full_classes = f"{base_classes} {additional_classes}".strip()
        full_styles = f"{base_styles} {additional_styles}".strip()

        button.classes(full_classes).style(full_styles)
    else:
        # Apply only custom styling without defaults
        if width_class and width_class != "w-52":
            additional_classes = f"{width_class} {additional_classes}".strip()

        if additional_classes:
            button.classes(additional_classes)
        if additional_styles:
            button.style(additional_styles)

    # Add toggle behavior if needed
    if toggle_behavior:
        import inspect

        is_async_callback = on_click_callback and inspect.iscoroutinefunction(
            on_click_callback
        )

        if is_async_callback:
            # Handle async toggle callback
            async def toggle_async():
                # Calculate new state but don't apply it yet
                new_state = not button_state["active"]

                # Call the callback with the new state BEFORE changing the button
                callback_result = (
                    True  # Default to True if callback doesn't return anything
                )
                if on_click_callback is not None:
                    try:
                        result = await on_click_callback(new_state)
                        # If the callback returns a boolean, use it to decide whether to toggle
                        if result is not None and isinstance(result, bool):
                            callback_result = result
                    except Exception as e:
                        log_exception(e, "Exception Error")
                        log_error(f"Error in async toggle callback: {e}")
                        import traceback

                        log_error(traceback.format_exc())
                        callback_result = False  # Don't toggle on error

                # Only toggle if the callback allowed it
                if callback_result:
                    # Actually update the button state
                    button_state["active"] = new_state

                    # Update button text and icon
                    if toggle_texts:
                        button.text = (
                            toggle_texts[1]
                            if button_state["active"]
                            else toggle_texts[0]
                        )

                    if toggle_icons:
                        button.icon = (
                            toggle_icons[1]
                            if button_state["active"]
                            else toggle_icons[0]
                        )

            button.on("click", toggle_async)
        else:
            # Handle regular toggle callback
            def toggle():
                # Calculate new state but don't apply it yet
                new_state = not button_state["active"]

                # Call the callback with the new state BEFORE changing the button
                callback_result = (
                    True  # Default to True if callback doesn't return anything
                )
                if on_click_callback is not None:
                    try:
                        result = on_click_callback(new_state)
                        # If the callback returns a boolean, use it to decide whether to toggle
                        if result is not None and isinstance(result, bool):
                            callback_result = result
                    except Exception as e:
                        log_exception(e, "Exception Error")
                        log_error(f"Error in toggle callback: {e}")
                        import traceback

                        log_error(traceback.format_exc())
                        callback_result = False  # Don't toggle on error

                # Only toggle if the callback allowed it
                if callback_result:
                    # Actually update the button state
                    button_state["active"] = new_state

                    # Update button text and icon
                    if toggle_texts:
                        button.text = (
                            toggle_texts[1]
                            if button_state["active"]
                            else toggle_texts[0]
                        )

                    if toggle_icons:
                        button.icon = (
                            toggle_icons[1]
                            if button_state["active"]
                            else toggle_icons[0]
                        )

            button.on("click", toggle)
    elif on_click_callback is not None:
        # Check if callback is async
        import inspect

        is_async_callback = inspect.iscoroutinefunction(on_click_callback)

        if is_async_callback:
            # Handle async non-toggle callback
            async def handle_click_async():
                if on_click_callback is not None:
                    try:
                        await on_click_callback(None)
                    except Exception as e:
                        log_exception(e, "Exception Error")
                        log_error(f"Error in async callback: {e}")
                        import traceback

                        log_error(traceback.format_exc())

            button.on("click", handle_click_async)
        else:
            # Handle regular non-toggle callback
            def handle_click():
                if on_click_callback is not None:
                    try:
                        on_click_callback(None)
                    except Exception as e:
                        log_exception(e, "Exception Error")
                        log_error(f"Error in callback: {e}")
                        import traceback

                        log_error(traceback.format_exc())

            button.on("click", handle_click)

    return button
