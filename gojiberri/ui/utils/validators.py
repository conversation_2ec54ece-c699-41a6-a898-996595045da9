# utils/validators.py
"""
Updated validators that work with the corrected recording flow
"""
from typing import Dict, Any, Tuple
from datetime import datetime

from gojiberri.utils.logger import log_exception


def validate_session_data(
    session_data: Dict[str, Any],
    components: Dict[str, Any],
    recording_manager=None,
) -> Dict[str, Any]:
    """
    Validate session data before proceeding to next step.

    Args:
        session_data: Current session data from UI components
        components: Dictionary of UI components
        recording_manager: Optional recording manager for additional checks

    Returns:
        Dictionary with validation result and message
    """
    patient = session_data.get("patient", {})

    name_component = components.get("patient_inputs", {}).get("name")
    dob_component = components.get("patient_inputs", {}).get("dob")

    has_error = False
    message = ""

    # Validate DOB
    dob_str = patient.get("dob", "").strip()
    if not dob_str:
        if dob_component:
            dob_component.style("border-bottom: 1px solid red;").props("color='red'")
        has_error = True
        message = "Please enter patient date of birth"
    else:
        try:
            dob_date = datetime.strptime(dob_str, "%Y-%m-%d")
            if dob_date > datetime.now():
                if dob_component:
                    dob_component.style("border-bottom: 1px solid red;").props(
                        "color='red'"
                    )
                has_error = True
                message = "Date of birth cannot be in the future"
            else:
                if dob_component:
                    dob_component.style("border-bottom: 1px solid gray;").props(
                        "color='black'"
                    )
        except ValueError:
            if dob_component:
                dob_component.style("border-bottom: 1px solid red;").props(
                    "color='red'"
                )
            has_error = True
            message = "Invalid date format for date of birth. Use YYYY-MM-DD."

    # Validate Name
    name_str = patient.get("name", "").strip()
    if not name_str:
        if name_component:
            name_component.style("border-bottom: 1px solid red;").props("color='red'")
        has_error = True
        if not message:  # Preserve first error if already set
            message = "Please enter a patient name"
    else:
        if name_component:
            name_component.style("border-bottom: 1px solid gray;").props(
                "color='black'"
            )

    # Additional recording-specific validation if manager is provided
    if recording_manager:
        recording_validation = validate_recording_state(recording_manager)
        if not recording_validation["valid"]:
            if not has_error:  # Only set if no previous error
                has_error = True
                message = recording_validation["message"]

    if has_error:
        return {"valid": False, "message": message}

    return {"valid": True, "message": "Data validated successfully"}


def validate_recording_state(recording_manager) -> Dict[str, Any]:
    """
    Validate recording state for session completion.

    Args:
        recording_manager: Recording manager instance

    Returns:
        Validation result dictionary
    """
    try:
        # Check if recording has been started
        if not getattr(recording_manager, "recording_started", False):
            return {
                "valid": False,
                "message": "Please start recording before proceeding",
            }

        # Check if we have audio data
        if not recording_manager.has_audio_data():
            return {
                "valid": False,
                "message": "No audio data available. Please record audio before proceeding",
            }

        # Validate audio data quality
        audio_validation = recording_manager.validate_audio_data()
        if not audio_validation.get("valid", False):
            return {
                "valid": False,
                "message": f"Invalid audio data: {audio_validation.get('error', 'Unknown error')}",
            }

        # Check minimum recording duration (e.g., 1 second)
        duration_ms = audio_validation.get("duration", 0)
        if duration_ms < 1000:  # Less than 1 second
            return {
                "valid": False,
                "message": f"Recording too short: {duration_ms}ms. Minimum recording time is 1 second.",
            }

        return {"valid": True, "message": "Recording validation passed"}

    except Exception as e:
        log_exception(e, "Exception Error")
        return {"valid": False, "message": f"Error validating recording: {str(e)}"}


def validate_recording_request(recording_data: Dict[str, Any]) -> Tuple[bool, str]:
    """
    Validate recording request data before sending to backend.

    Returns:
        Tuple of (is_valid, error_message)
    """
    # Check required fields
    if not recording_data.get("file_path"):
        return False, "file_path is required"

    duration = recording_data.get("duration_milliseconds")
    if duration is None:
        return False, "duration_milliseconds is None"

    # Validate end_time format
    end_time = recording_data.get("end_time")
    if end_time:
        try:
            # Handle both timezone-aware and naive datetime strings
            # First try parsing as-is (for timezone-aware strings)
            try:
                datetime.fromisoformat(end_time)
            except ValueError:
                # If that fails, try replacing 'Z' with '+00:00' for UTC
                datetime.fromisoformat(end_time.replace("Z", "+00:00"))
        except ValueError:
            return (
                False,
                "end_time must be in ISO format (e.g., '2024-01-01T10:00:00+00:00')",
            )

    return True, ""
