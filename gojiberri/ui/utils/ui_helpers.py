# utils/ui_helpers.py
"""
UI helper functions with async context safety.
"""

from nicegui import ui
from typing import Literal


def show_notification(
    message: str,
    type: Literal["positive", "negative", "warning", "info", "ongoing"] = "info",
    duration: float = 5.0,
    position: Literal[
        "top-left",
        "top-right",
        "bottom-left",
        "bottom-right",
        "top",
        "bottom",
        "left",
        "right",
        "center",
    ] = "bottom-right",
) -> None:
    """
    Show a notification message with async context safety.

    Args:
        message: Message to display
        type: Notification type ('positive', 'negative', 'warning', 'info', 'ongoing')
        duration: Display duration in seconds (default 5.0)
        position: Position on screen
    """
    try:
        ui.notify(message=message, type=type, position=position, timeout=None)
    except RuntimeError as e:
        if "slot stack" in str(e).lower():
            # Handle async context error by scheduling notification for next UI update
            def deferred_notify():
                try:
                    ui.notify(
                        message=message, type=type, position=position, timeout=None
                    )
                except Exception:
                    # Fallback: just print to console if UI notification fails
                    print(f"[{type.upper()}] {message}")

            # Try to schedule for next UI update cycle
            try:
                ui.timer(0.1, deferred_notify, once=True)
            except Exception:
                # Final fallback: just print to console
                print(f"[{type.upper()}] {message}")
        else:
            # Different error, just print to console
            print(f"[{type.upper()}] {message}")
    except Exception:
        # Any other error, fallback to console
        print(f"[{type.upper()}] {message}")


def safe_show_notification(
    message: str,
    type: Literal["positive", "negative", "warning", "info", "ongoing"] = "info",
    duration: float = 5.0,
    position: Literal[
        "top-left",
        "top-right",
        "bottom-left",
        "bottom-right",
        "top",
        "bottom",
        "left",
        "right",
        "center",
    ] = "bottom-right",
) -> None:
    """
    Safe notification that always works, even in async contexts.
    Falls back to console logging if UI notification fails.

    Args:
        message: Message to display
        type: Notification type
        duration: Display duration in seconds
        position: Position on screen
    """
    try:
        show_notification(message, type, duration, position)
    except Exception:
        # Always fallback to console
        print(f"[{type.upper()}] {message}")
