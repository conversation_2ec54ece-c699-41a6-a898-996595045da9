# utils/session_state.py
"""
Simplified session state management - consolidated loading methods and better step progress utilization.
"""

from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from gojiberri.models.enums import WorkflowType
from gojiberri.ui.config import config
from gojiberri.ui.api_client import api
from gojiberri.utils.logger import debug, log_exception


@dataclass
class SessionState:
    """Represents the complete state of a session from backend."""

    session_id: str
    status: str
    current_step: str
    step_status: str
    patient_name: str
    patient_dob: str
    session_date: str
    session_time: str
    workflow_steps: List[Dict[str, Any]]
    session_data: Dict[str, Any]
    polling_manager: Optional[Any] = None


class SessionStateManager:
    """Simplified session state manager with unified loading."""

    def __init__(self):
        self.current_session_id: Optional[str] = None
        self.sessions: Dict[str, SessionState] = {}
        self._cached_workflow_steps: Optional[List[Dict[str, Any]]] = None
        self._workflow_steps_loaded: bool = False

        # Simple cache for avoiding redundant API calls
        self._session_cache: Dict[str, Dict] = {}
        cache_seconds = config.get("session.cache_ttl_seconds", 30)
        self._cache_ttl = timedelta(seconds=cache_seconds)

        # Callbacks
        self.content_change_callback: Optional[Callable] = None
        self.workflow_update_callback: Optional[Callable] = None
        self._suppress_callbacks: bool = False
        self._initialized: bool = False

    async def initialize(self):
        """Initialize the session state manager."""
        if self._initialized:
            return
        try:
            # await self._load_and_cache_workflow_steps()
            self._initialized = True
        except Exception as e:
            log_exception(e, "Error initializing session state manager")
            self._initialized = True

    # UNIFIED SESSION LOADING
    async def load_session(
        self, session_id: str, force_full_data: bool = False
    ) -> SessionState:
        """
        Load session with basic display data always included for sidebar consistency.

        Always loads: patient_name, session_date, session_time for sidebar display
        Conditionally loads: full form data only when needed for form prefilling

        Args:
            session_id: Session ID to load
            force_full_data: Force loading complete session data for form operations
        """
        try:
            # Check cache first
            if not force_full_data and self._is_cached_and_fresh(session_id):
                debug(f"Using cached session data for {session_id}")
                return self.sessions[session_id]

            # Always get status first (lightweight and fast)
            status_data = await api.sessions.get_session_status(session_id)

            # Extract step progress from status
            step_progress = self._extract_step_progress(status_data.get("current_step"))

            # Always load basic display data for sidebar consistency
            full_data = await api.sessions.get_session(session_id)

            # Create session with both status and basic display data
            session_state = SessionState(
                session_id=session_id,
                status=status_data.get("status", "in_progress"),
                current_step=step_progress["step"],
                step_status=step_progress["step_status"],
                patient_name=full_data.get("patient_name", ""),
                patient_dob=full_data.get("patient_dob", ""),
                session_date=full_data.get("session_date", ""),
                session_time=full_data.get("session_time", ""),
                workflow_steps=await self.get_cached_workflow_steps(
                    step_progress["step"]
                ),
                session_data={**status_data, **full_data},
            )

            # Cache and store
            self.sessions[session_id] = session_state

            debug(
                f"Loaded session with display data: {session_id}, patient: {session_state.patient_name}, step: {session_state.current_step}, status: {session_state.step_status}"
            )
            return session_state

        except Exception as e:
            log_exception(e, f"Error loading session {session_id}")
            raise

    def _extract_step_progress(self, step_data: Any) -> Dict[str, str]:
        """Extract step and status from backend response."""
        try:
            if isinstance(step_data, dict):
                return {
                    "step": step_data.get("step", "recording"),
                    "step_status": step_data.get("step_status", "not_started"),
                }
            elif isinstance(step_data, str):
                return {"step": step_data, "step_status": "in_progress"}
            else:
                return {"step": "recording", "step_status": "not_started"}
        except Exception:
            return {"step": "recording", "step_status": "not_started"}

    async def get_cached_workflow_steps(
        self, current_step: str = "recording"
    ) -> List[Dict[str, Any]]:
        """
        Get cached workflow steps with status based on current step.
        If not cached, attempts to fetch from backend.
        Returns empty list if backend failed or unavailable.
        """
        # If not loaded, attempt to fetch from backend
        if not self._workflow_steps_loaded or not self._cached_workflow_steps:
            debug("Workflow steps not cached, attempting to fetch from backend...")
            try:
                # Fetch workflow steps from API
                workflow_steps = await api.workflow.get_workflow_steps()

                if workflow_steps:
                    self._cached_workflow_steps = workflow_steps
                    self._workflow_steps_loaded = True
                    debug(
                        f"Successfully fetched and cached {len(workflow_steps)} workflow steps"
                    )
                else:
                    debug("Backend returned empty workflow steps")
                    self._cached_workflow_steps = []
                    self._workflow_steps_loaded = True

            except Exception as e:
                log_exception(e, "Error fetching workflow steps from backend")
                debug(f"Failed to fetch workflow steps: {e}")
                # Return empty list if fetch fails
                return []

        # Process cached workflow steps with current step status
        if not self._cached_workflow_steps:
            debug("No cached workflow steps available")
            return []

        try:
            updated_steps = []
            for step in self._cached_workflow_steps:
                step_copy = step.copy()
                step_name = step_copy.get("step_name", "")

                if step_name == current_step.lower():
                    step_copy["status"] = "current"
                else:
                    step_copy["status"] = "pending"

                updated_steps.append(step_copy)

            return updated_steps
        except Exception as e:
            log_exception(e, "Error processing cached workflow steps")
            return []

    # SIMPLIFIED SESSION OPERATIONS
    async def switch_to_session(self, session_id: str):
        """Switch to a different session with polling cleanup."""
        try:
            #  Stop polling from previous session
            await self._cleanup_current_session_polling()

            session_state = await self.load_session(session_id)
            self.current_session_id = session_id

            await self._notify_workflow_update(
                session_state.workflow_steps, session_state.current_step
            )
            await self._notify_content_change(session_state.current_step, session_state)

        except Exception as e:
            log_exception(e, f"Error switching to session {session_id}")
            raise

    async def refresh_current_session(self):
        """Refresh the current session using cached or fresh data."""
        if not self.current_session_id:
            return

        # Invalidate cache first
        self.invalidate_session_cache(self.current_session_id)

        try:
            # Always refresh status to get latest step progress
            session_state = await self.load_session(
                self.current_session_id, force_full_data=False
            )

            await self._notify_workflow_update(
                session_state.workflow_steps, session_state.current_step
            )
            await self._notify_content_change(session_state.current_step, session_state)

        except Exception as e:
            log_exception(e, f"Error refreshing session {self.current_session_id}")

    async def create_new_session(
        self, initial_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new session and switch to it."""
        try:
            session_data = {
                "patient_name": (
                    initial_data.get("patient_name", "") if initial_data else ""
                ),
                "patient_dob": (
                    initial_data.get("patient_dob", "") if initial_data else ""
                ),
                "workflow_type": WorkflowType.DOCUMENT_GENERATION.value,
            }

            response = await api.sessions.create_session(session_data)
            session_id = response.get("session_id") or response.get("id")

            if not session_id:
                raise Exception("No session ID returned from API")

            await self.switch_to_session(session_id)
            return session_id

        except Exception as e:
            log_exception(e, "Error creating new session")
            raise

    # CACHE MANAGEMENT
    def _is_cached_and_fresh(self, session_id: str) -> bool:
        """Check if session is cached and still fresh."""
        if session_id not in self._session_cache:
            return False

        cache_entry = self._session_cache[session_id]
        cached_at = cache_entry.get("cached_at")

        if not cached_at:
            return False

        return datetime.now() - cached_at < self._cache_ttl

    # CALLBACK MANAGEMENT
    def set_callbacks(
        self, content_callback: Callable = None, workflow_callback: Callable = None
    ):
        """Set callbacks for UI updates."""
        self.content_change_callback = content_callback
        self.workflow_update_callback = workflow_callback

    async def _notify_workflow_update(
        self, workflow_steps: List[Dict[str, Any]], current_step: str
    ):
        """Notify workflow update callback."""
        try:
            if not self._suppress_callbacks and self.workflow_update_callback:
                self.workflow_update_callback(workflow_steps, current_step)
        except Exception as e:
            log_exception(e, "Error in workflow update callback")

    async def _notify_content_change(
        self, step_name: str, session_state: Optional[SessionState]
    ):
        """Notify content change callback."""
        try:
            if not self._suppress_callbacks and self.content_change_callback:
                await self.content_change_callback(step_name, session_state)
        except Exception as e:
            log_exception(e, "Error in content change callback")

    # WORKFLOW MANAGEMENT
    async def show_default_workflow(self):
        """Show default workflow when no session is active."""
        try:
            await self._cleanup_current_session_polling()

            if not self._workflow_steps_loaded:
                await self._load_and_cache_workflow_steps()

            self.current_session_id = None
            workflow_steps = await self.get_cached_workflow_steps("recording")

            await self._notify_workflow_update(workflow_steps, "recording")
            await self._notify_content_change("recording", None)

        except Exception as e:
            log_exception(e, "Error showing default workflow")
            raise

    async def _load_and_cache_workflow_steps(self):
        """Load workflow steps from backend and cache them."""
        try:
            workflow_steps = await api.workflow.get_workflow_steps()
            if workflow_steps:
                self._cached_workflow_steps = workflow_steps
                self._workflow_steps_loaded = True
            else:
                self._cached_workflow_steps = []
                self._workflow_steps_loaded = True
        except Exception as e:
            log_exception(e, "Error loading workflow steps")
            self._cached_workflow_steps = []
            self._workflow_steps_loaded = True

    async def _cleanup_current_session_polling(self):
        """Clean up polling from the current session if it exists."""
        if self.current_session_id:
            previous_session = self.sessions.get(self.current_session_id)
            if previous_session and previous_session.polling_manager:
                debug(f"Stopping polling for session: {self.current_session_id}")
                try:
                    await previous_session.polling_manager.stop_polling()
                    previous_session.polling_manager = None
                    debug("Session polling stopped successfully")
                except Exception as e:
                    log_exception(e, "Error stopping session polling")

    def invalidate_session_cache(self, session_id: str):
        """Force invalidate cache for a specific session."""
        try:
            if session_id in self._session_cache:
                del self._session_cache[session_id]
                debug(f"Cache invalidated for session {session_id}")
        except Exception as e:
            log_exception(e, f"Error invalidating cache for session {session_id}")

    # UTILITY METHODS
    def get_current_session(self) -> Optional[SessionState]:
        """Get current session state."""
        if self.current_session_id and self.current_session_id in self.sessions:
            return self.sessions[self.current_session_id]
        return None

    def get_all_sessions(self) -> List[SessionState]:
        """Get all session states, sorted newest to oldest."""
        sessions = list(self.sessions.values())
        try:
            # Sort sessions with the newest first.
            # Sessions without a valid date/time will be at the end.
            sessions.sort(
                key=lambda s: (
                    datetime.strptime(
                        f"{s.session_date} {s.session_time}", "%Y-%m-%d %H:%M:%S"
                    )
                    if s.session_date and s.session_time
                    else datetime.min
                ),
                reverse=True,
            )
        except ValueError as e:
            log_exception(e, "Could not parse session date/time for sorting")
        return sessions

    def has_active_session(self) -> bool:
        """Check if there's an active session."""
        return (
            self.current_session_id is not None
            and self.current_session_id in self.sessions
        )

    async def refresh_all_sessions(self) -> List[SessionState]:
        """
        Refresh all sessions with consistent display data loading.

        Ensures all sessions have patient names and time information
        for proper sidebar display across all workflow steps.

        Returns:
            List[SessionState]: Updated session states with complete display data
        """
        try:
            list_limit = config.get("session.list_limit", 50)
            response = await api.sessions.list_sessions(limit=list_limit)
            sessions_data = response.get("sessions", [])

            for session_data in sessions_data:
                session_id = session_data.get("session_id") or session_data.get("id")
                if session_id:
                    # Extract step progress
                    step_progress = self._extract_step_progress(
                        session_data.get("current_step")
                    )

                    # Create session state with all available display data
                    session_state = SessionState(
                        session_id=session_id,
                        status=session_data.get("status", "in_progress"),
                        current_step=step_progress["step"],
                        step_status=step_progress["step_status"],
                        patient_name=session_data.get("patient_name", ""),
                        patient_dob=session_data.get("patient_dob", ""),
                        session_date=session_data.get("session_date", ""),
                        session_time=session_data.get("session_time", ""),
                        workflow_steps=await self.get_cached_workflow_steps(
                            step_progress["step"]
                        ),
                        session_data=session_data,
                    )
                    self.sessions[session_id] = session_state

            return self.get_all_sessions()

        except Exception as e:
            log_exception(e, "Error refreshing all sessions")
            return []

    def cleanup(self):
        """Cleanup all resources."""
        debug("Cleaning up session state manager")
        self.content_change_callback = None
        self.workflow_update_callback = None
        self._session_cache.clear()


# Global session state manager instance
session_manager = SessionStateManager()
