# ui/utils/error_fallback.py
"""
Global error fallback utility for consistent error display across the application.
"""

from nicegui import ui
from typing import Optional
from gojiberri.utils.logger import log_exception
from gojiberri.ui.components.buttons import create_styled_button


def create_error_fallback(
    error_message: str,
    title: str = "Something went wrong",
    session_id: Optional[str] = None,
    show_session_info: bool = False,
    container: Optional[ui.element] = None,
) -> ui.element:
    """
    Create standardized error fallback section with refresh and navigation options.

    Args:
        error_message: Error message to display
        title: Error title (default: "Something went wrong")
        session_id: Optional session ID for context
        show_session_info: Whether to show session information
        container: Optional container to clear and display error in

    Returns:
        ui.element: Error section (new ui.column if no container, or the cleared container)
    """
    try:
        # If container is provided, clear it and create error content inside
        if container:
            container.clear()
            with container:
                _create_error_content(
                    error_message, title, session_id, show_session_info
                )
            return container
        else:
            # Create new error content
            return _create_error_content(
                error_message, title, session_id, show_session_info
            )

    except Exception as e:
        log_exception(e, "Error creating error fallback")
        # Last resort - return minimal content
        return _create_minimal_error()


def _create_error_content(
    error_message: str,
    title: str,
    session_id: Optional[str],
    show_session_info: bool,
) -> ui.column:
    """Create the actual error content using styled components."""
    with ui.column().classes(
        "w-full h-full flex items-center justify-center p-8"
    ) as error_content:

        # Error icon
        ui.icon("error").classes("text-6xl text-red-400 mb-4")

        # Error title
        ui.label(title).classes("text-2xl font-bold text-red-600 mb-2 text-center")

        # Error message
        ui.label(error_message).classes("text-gray-600 text-center mb-6 max-w-md")

        # Session info (optional)
        if show_session_info and session_id:
            ui.label(f"Session: {session_id[:8]}...").classes(
                "text-sm text-gray-400 mb-4"
            )

        # Action buttons using create_styled_button
        with ui.row().classes("gap-4"):
            create_styled_button(
                button_text="Go to Recording",
                icon="mic",
                width_class="w-auto",
                on_click_callback=lambda event_data: _safe_navigate_to_recording(),
                additional_styles="padding: 8px 16px; border-radius: 6px;",
            )

            create_styled_button(
                button_text="Refresh",
                icon="refresh",
                width_class="w-auto",
                on_click_callback=lambda event_data: _safe_refresh_page(),
                additional_styles="padding: 8px 16px; border-radius: 6px;",
            )

    return error_content


def _safe_navigate_to_recording():
    """Safely navigate to recording step."""
    try:
        import asyncio
        from gojiberri.ui.utils.session_state import session_manager

        asyncio.create_task(session_manager.show_default_workflow())
    except Exception as e:
        log_exception(e, "Error navigating to recording")
        _safe_refresh_page()


def _safe_refresh_page():
    """Safely refresh the current page."""
    try:
        ui.navigate.reload()
    except Exception as e:
        log_exception(e, "Error refreshing page")
        # Try alternative refresh method
        try:
            ui.open("/")
        except Exception:
            pass  # Last resort - do nothing


def _create_minimal_error() -> ui.column:
    """Create minimal error content as absolute last resort."""
    with ui.column().classes("w-full p-4") as content:
        ui.label("Error occurred").classes("text-xl text-red-600 mb-2")
        ui.button("Reload", on_click=lambda: ui.open("/"))
    return content
