# Generated test file for validators
import pytest
from unittest.mock import Mock, patch, MagicMock
from gojiberri.ui.utils.validators import validate_session_data, validate_recording_state


def test_validate_session_data_valid_input():
    """Test validate_session_data with valid input."""
    session_data = {
        'participant_id': '123',
        'session_date': '2023-01-01',
        'required_field': 'value',
        'patient_dob': '2000-01-01'
    }
    components = {
        'required_field': {'required': True},
        'patient_dob': {'required': False},  # Make patient_dob optional for this test
        'participant_id': {'required': True},
        'session_date': {'required': True}
    }
    recording_manager = Mock()
    
    result = validate_session_data(session_data, components, recording_manager)
    assert result['valid'] is True
    assert 'message' in result


def test_validate_session_data_missing_required_field():
    """Test validate_session_data with missing required field."""
    session_data = {
        'participant_id': '123',
        'session_date': '2023-01-01',
        'patient_dob': '2000-01-01'  # required_field is missing
    }
    components = {
        'required_field': {'required': True},
        'patient_dob': {'required': False},  # Make patient_dob optional
        'participant_id': {'required': True},
        'session_date': {'required': True}
    }
    recording_manager = Mock()
    
    result = validate_session_data(session_data, components, recording_manager)
    assert result['valid'] is False
    assert 'required_field' in result['message'].lower()  # Check for specific field name


def test_validate_session_data_invalid_date_format():
    """Test validate_session_data with invalid date format."""
    session_data = {
        'participant_id': '123',
        'session_date': 'invalid-date',
        'required_field': 'value',
        'patient_dob': '2000-01-01'
    }
    components = {
        'required_field': {'required': True},
        'patient_dob': {'required': False},  # Make patient_dob optional
        'participant_id': {'required': True},
        'session_date': {'required': True}
    }
    recording_manager = Mock()
    
    result = validate_session_data(session_data, components, recording_manager)
    assert result['valid'] is False
    assert 'invalid' in result['message'].lower() or 'date' in result['message'].lower()


def test_validate_recording_state_active_recording():
    """Test validate_recording_state with active recording."""
    recording_manager = Mock()
    recording_manager.is_recording_active.return_value = True
    
    result = validate_recording_state(recording_manager)
    assert result['valid'] is False
    assert 'recording' in result['message'].lower()


def test_validate_recording_state_no_active_recording():
    """Test validate_recording_state with no active recording."""
    recording_manager = Mock()
    recording_manager.is_recording_active.return_value = False
    
    result = validate_recording_state(recording_manager)
    assert result['valid'] is True
    assert result.get('message', '') == ''


@patch('gojiberri.ui.utils.validators.datetime')
def test_validate_session_data_future_date(mock_datetime):
    """Test validate_session_data with future date."""
    mock_datetime.now.return_value.year = 2023
    session_data = {
        'participant_id': '123',
        'session_date': '2024-01-01',
        'required_field': 'value',
        'patient_dob': '2000-01-01'
    }
    components = {
        'required_field': {'required': True},
        'patient_dob': {'required': False},  # Make patient_dob optional
        'participant_id': {'required': True},
        'session_date': {'required': True}
    }
    recording_manager = Mock()
    
    result = validate_session_data(session_data, components, recording_manager)
    assert result['valid'] is False
    assert 'future' in result['message'].lower() or 'date' in result['message'].lower()