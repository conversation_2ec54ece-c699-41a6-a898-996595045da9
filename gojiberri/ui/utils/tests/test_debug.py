# Generated test file for debug
import pytest
from unittest.mock import Mock, patch, MagicMock
from gojiberri.utils.logger import (
    set_debug_level,
    enable_debug,
    debug_print,
    debug,
    info,
    warning,
    error,
    success,
    critical,
)


@pytest.mark.parametrize("level", ["debug", "info", "warning", "error", "critical"])
def test_set_debug_level(level):
    """Test the set_debug_level function."""
    set_debug_level(level)
    # No return value to assert, just verify no exceptions


@pytest.mark.parametrize("enabled", [True, False])
def test_enable_debug(enabled):
    """Test the enable_debug function."""
    enable_debug(enabled)
    # No return value to assert, just verify no exceptions


@patch("builtins.print")
def test_debug_print(mock_print):
    """Test the debug_print function."""
    # Test basic message
    debug_print("test message", "info")
    assert mock_print.called

    # Test with all options
    debug_print(
        "test message", "warning", title="Title", show_caller=True, show_time=True
    )
    assert mock_print.call_count == 2

    # Test invalid type
    with pytest.raises(ValueError):
        debug_print("test message", "invalid_type")


@pytest.mark.parametrize(
    "func,msg_type",
    [
        (debug, "debug"),
        (info, "info"),
        (warning, "warning"),
        (error, "error"),
        (success, "success"),
        (critical, "critical"),
    ],
)
@patch("gojiberri.ui.utils.debug.debug_print")
def test_log_functions(mock_debug_print, func, msg_type):
    """Test all log functions (debug, info, warning, etc)."""
    func("test message")
    mock_debug_print.assert_called_once_with(
        "test message", msg_type, title=None, show_caller=False, show_time=False
    )

    # Test with kwargs
    mock_debug_print.reset_mock()
    func("test message", title="Title", show_caller=True)
    mock_debug_print.assert_called_once_with(
        "test message", msg_type, title="Title", show_caller=True, show_time=False
    )


@pytest.mark.parametrize("level", ["invalid", "", None, 123])
def test_set_debug_level_invalid(level):
    """Test set_debug_level with invalid inputs."""
    with pytest.raises(ValueError):
        set_debug_level(level)


@patch("builtins.print")
def test_debug_print_edge_cases(mock_print):
    """Test debug_print with edge cases."""
    # Test non-string message
    debug_print(12345, "info")
    assert mock_print.called

    # Test empty message
    debug_print("", "warning")
    assert mock_print.call_count == 2

    # Test None message
    debug_print(None, "error")
    assert mock_print.call_count == 3
