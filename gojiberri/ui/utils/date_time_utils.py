from datetime import datetime


def parse_session_datetime(session_start_str: str) -> tuple[str, str]:
    """Parse session datetime from UTC ISO string."""
    if not session_start_str:
        return "", ""

    try:
        dt = datetime.fromisoformat(session_start_str.replace("Z", "+00:00"))
        return dt.strftime("%Y-%m-%d"), dt.strftime("%H:%M:%S")
    except (ValueError, AttributeError):
        return "", ""
