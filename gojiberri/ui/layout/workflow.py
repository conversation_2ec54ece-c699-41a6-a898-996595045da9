# layout/workflow.py
"""
Clean workflow component for desktop application - no client validation complexity.
"""

from nicegui import ui
from typing import List, Dict, Any, Optional, Callable
from gojiberri.ui.components import create_connector_line, create_workflow_button
from gojiberri.utils.logger import error, debug, log_exception


def create_workflow_stepper(
    workflow_steps: List[Dict[str, Any]],
    on_step_click: Optional[Callable[[str], None]] = None,
) -> ui.column:
    """Create workflow stepper from backend workflow data."""

    try:
        with ui.column().style(
            "max-width: 1600px; width: 100%; margin-right: 100px;"
        ).classes("items-center") as workflow:

            # First row: Buttons and connectors
            with ui.row().classes("w-full justify-center items-center gap-0"):
                for i, step in enumerate(workflow_steps):
                    # Create connector line between buttons
                    if i > 0:
                        create_connector_line()

                    step_name = step.get("step_name", "")
                    step_icon = step.get("icon", "help")
                    step_status = step.get("status", "pending")

                    # Create workflow button with safe click handler
                    def safe_step_click(s=step_name):
                        try:
                            if on_step_click:
                                on_step_click(s)
                        except Exception as e:
                            log_exception(e, "Exception Error")
                            error(f"Error in step click handler for {s}: {e}")

                    create_workflow_button(
                        step_icon,
                        step_status,
                        on_click=safe_step_click,
                        step_name=step_name,
                    )

            # Second row: Labels
            with ui.row().classes("w-full justify-center"):
                for i, step in enumerate(workflow_steps):
                    if i > 0:
                        # Spacer for connector line
                        ui.label("").classes("flex-grow")

                    step_status = step.get("status", "pending")
                    step_label = step.get("label", step.get("step_name", "Unknown"))

                    # Text color based on status
                    text_class = (
                        "text-black font-bold"
                        if step_status == "current"
                        else "text-gray-500"
                    )

                    ui.label(step_label).classes(
                        f"text-sm {text_class} text-center max-w-16 md:text-[15px] test-label"
                    )

        return workflow

    except Exception as e:
        log_exception(e, "Exception Error")
        error(f"Error creating workflow stepper: {e}")
        # Return empty column as fallback
        return ui.column().classes("w-full")


class DynamicWorkflow:
    """Simple workflow display manager for desktop application."""

    def __init__(self, container):
        self.container = container
        self.step_click_callback: Optional[Callable] = None
        self.current_workflow_hash = None

    def set_step_click_callback(self, callback: Callable[[str], None]):
        """Set callback for workflow step clicks."""
        self.step_click_callback = callback

    def update_workflow(self, workflow_steps: List[Dict[str, Any]], current_step: str):
        """Update workflow display with backend data."""
        try:
            # Check if workflow has actually changed
            workflow_hash = self._get_workflow_hash(workflow_steps, current_step)
            if workflow_hash == self.current_workflow_hash:
                debug("Workflow unchanged, skipping update")
                return

            # Clear container safely
            try:
                self.container.clear()
            except Exception as e:
                log_exception(e, "Exception Error")
                error(f"Error clearing workflow container: {e}")
                return

            # Create new workflow
            with self.container:
                create_workflow_stepper(workflow_steps, self._safe_step_click)

            # Update hash
            self.current_workflow_hash = workflow_hash

        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error updating workflow: {e}")

    def _get_workflow_hash(
        self, workflow_steps: List[Dict[str, Any]], current_step: str
    ) -> str:
        """Generate a hash of workflow state for change detection."""
        try:
            step_info = []
            for step in workflow_steps:
                info = f"{step.get('step_name', '')}:{step.get('status', '')}"
                step_info.append(info)
            return f"{current_step}|{'|'.join(step_info)}"
        except Exception:
            return ""

    def _safe_step_click(self, step_name: str):
        """Safely handle step clicks."""
        try:
            if self.step_click_callback:
                debug(f"Workflow step clicked: {step_name}")
                # Create async task for step handling
                import asyncio

                asyncio.create_task(self._handle_step_click_async(step_name))

        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error in safe step click: {e}")

    async def _handle_step_click_async(self, step_name: str):
        """Handle step click asynchronously."""
        try:
            await self.step_click_callback(step_name)
        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error handling step click for {step_name}: {e}")


def create_dynamic_workflow_container() -> ui.element:
    """Create container for dynamic workflow updates."""

    try:
        container = ui.element("div").classes("flex-grow")
        workflow_manager = DynamicWorkflow(container)

        # Attach methods to container
        container.set_step_click_callback = workflow_manager.set_step_click_callback
        container.update_workflow = workflow_manager.update_workflow

        return container

    except Exception as e:
        log_exception(e, "Exception Error")
        error(f"Error creating dynamic workflow container: {e}")
        # Return basic container as fallback
        return ui.element("div").classes("flex-grow")
