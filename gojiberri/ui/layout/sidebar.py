# layout/sidebar.py
"""
Clean dynamic sidebar for desktop application - no client validation complexity.
"""

from nicegui import ui
from typing import Callable, Dict, List, Optional
from gojiberri.ui.config import config
from gojiberri.ui.components import create_new_session_button
from gojiberri.ui.utils.session_state import session_manager
from gojiberri.utils.logger import error, debug, log_exception


class DynamicSidebar:
    """Simple dynamic sidebar for desktop application."""

    def __init__(self, container):
        self.container = container
        self.on_session_click: Optional[Callable] = None
        self.on_new_session: Optional[Callable] = None
        self.session_items: Dict[str, ui.element] = {}
        self.sessions_container = None
        self.refresh_timer = None
        self.last_sessions_state = None

    def set_callbacks(self, on_session_click: Callable, on_new_session: Callable):
        """Set callback functions."""
        self.on_session_click = on_session_click
        self.on_new_session = on_new_session

    def initialize_ui(self):
        """Initialize the sidebar UI elements."""
        try:
            with self.container:
                # Header
                with ui.row().classes("w-full items-center justify-center gap-4 px-4"):
                    ui.separator().classes("flex-grow border-b-2 border-black")
                    ui.label("Gojiberri").classes(
                        "font-bold text-black text-xl text-center"
                    )
                    ui.separator().style("border-bottom: 2px solid black;").classes(
                        "flex-grow"
                    )

                # New Session Button
                create_new_session_button(on_click=self._handle_new_session)

                # Sessions Container - this will be dynamically updated
                self.sessions_container = ui.column().classes(
                    "w-full px-1 flex-grow overflow-auto"
                )

                # Bottom link
                with ui.row().classes("w-full justify-center mt-auto pb-4"):
                    ui.link("See older sessions", target="#").classes(
                        "text-blue-500 text-sm"
                    )

        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error initializing sidebar UI: {e}")

    async def initialize_data(self):
        """Initialize the sidebar data."""
        try:
            # Load initial sessions
            await self.refresh_sessions()

            # Set up automatic refresh timer
            self.setup_refresh_timer()

        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error initializing sidebar data: {e}")

    def setup_refresh_timer(self):
        """Set up periodic refresh timer."""

        async def periodic_refresh():
            try:
                # Refresh sessions from session manager
                sessions = session_manager.get_all_sessions()

                # Check if sessions have changed
                current_state = self._get_sessions_state_hash(sessions)
                if current_state != self.last_sessions_state:
                    await self.refresh_sessions()
                    self.last_sessions_state = current_state

            except Exception as e:
                log_exception(e, "Exception Error")
                debug(f"Error in periodic sidebar refresh: {e}")

        # Create timer for every 5 seconds
        refresh_interval = config.get("session.refresh_interval", 5.0)
        self.refresh_timer = ui.timer(refresh_interval, periodic_refresh)

    def _get_sessions_state_hash(self, sessions: List) -> str:
        """Generate a simple hash of sessions state for change detection."""
        try:
            session_info = []
            current_session = session_manager.get_current_session()
            current_session_id = current_session.session_id if current_session else None
            for session in sessions:
                info = f"{session.session_id}:{session.status}:{session.current_step}:{session.patient_name}"
                session_info.append(info)
            # Include the current session ID in the hash to detect session switches
            return f"current:{current_session_id}|{"|".join(session_info)}"
        except Exception:
            return ""

    async def refresh_sessions(self):
        """Refresh sessions display."""
        try:
            # Get current sessions from session manager
            sessions = session_manager.get_all_sessions()
            current_session = session_manager.get_current_session()
            current_session_id = current_session.session_id if current_session else None

            # Clear existing items safely
            if self.sessions_container:
                try:
                    self.sessions_container.clear()
                    self.session_items.clear()
                except Exception as e:
                    log_exception(e, "Exception Error")
                    error(f"Error clearing sessions container: {e}")
                    return

                with self.sessions_container:
                    # Group sessions by status
                    in_progress_sessions = [
                        s for s in sessions if s.status == "in_progress"
                    ]
                    completed_sessions = [
                        s for s in sessions if s.status == "completed"
                    ]

                    # In-Progress Sessions Section
                    if in_progress_sessions:
                        self._create_session_section(
                            "In-Progress Sessions",
                            "expand_more",
                            in_progress_sessions,
                            current_session_id,
                            True,
                        )

                    # Completed Sessions Section
                    if completed_sessions:
                        self._create_session_section(
                            "Completed Sessions",
                            "expand_more",
                            completed_sessions,
                            current_session_id,
                            False,
                        )

        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error refreshing sessions in sidebar: {e}")

    def _create_session_section(
        self,
        title: str,
        icon: str,
        sessions: List,
        current_session_id: Optional[str],
        is_in_progress: bool,
    ):
        """Create a session section."""
        try:
            with ui.column().classes(
                "w-full" + (" mt-2" if not is_in_progress else "")
            ):
                # Section header
                with ui.row().classes("w-full py-3 flex items-center gap-0"):
                    ui.icon(icon).classes("text-[35px] text-black")
                    ui.label(title).classes("text-[16px] text-black font-semibold p-0")

                # Session items
                with ui.column().classes("w-full"):
                    for session_state in sessions:
                        self._create_session_item(
                            session_state, current_session_id, is_in_progress
                        )
        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error creating session section: {e}")

    def _create_session_item(
        self, session_state, current_session_id: Optional[str], is_in_progress: bool
    ):
        """
        Create session item with patient name, time at right end, and step status information.

        Layout: [Icon] [Patient Name] ... [Time]
                [Icon] [Step Status]

        Args:
            session_state: Session state containing display data
            current_session_id: Currently selected session ID for highlighting
            is_in_progress: Whether this is an in-progress session requiring status display
        """
        try:
            session_id = session_state.session_id
            is_current = session_id == current_session_id

            # Determine styling based on current selection
            item_classes = (
                "w-full mt-[-10px] p-1 sidebar-item rounded cursor-pointer gap-0"
            )
            if is_current:
                item_classes += " bg-white border-l-4 border-black"

            with ui.row().classes(item_classes) as session_item:
                ui.icon("delete").classes("text-black text-[20px] flex-shrink-0")

                with ui.column().classes("ml-2 flex-grow min-w-0"):
                    # Name and Time row with justified layout
                    with ui.row().classes("items-center justify-between w-full"):
                        # Left side - Patient name (with truncation)
                        display_name = self._get_display_name(session_state)
                        ui.label(display_name).classes(
                            "text-sm font-medium text-black truncate flex-shrink min-w-0"
                        )

                        # Right side - Session time
                        time_display = self._get_time_display(session_state)
                        if time_display:
                            ui.label(time_display).classes(
                                "text-xs text-gray-400 flex-shrink-0 ml-2"
                            )

                    if is_in_progress:
                        # Status row with current step and detailed step status
                        step_info = self._get_step_display_info(
                            session_state.current_step, session_state.step_status
                        )
                        with ui.row().classes("flex items-center w-full gap-x-1"):
                            ui.icon(step_info["icon"]).classes(
                                f"text-[20px] {step_info['color']}"
                            )

                            ui.label(step_info["label"]).classes(
                                f"text-xs {step_info['color']} font-medium"
                            )

                            if step_info["status_label"]:
                                ui.label(f"({step_info['status_label']})").classes(
                                    f"text-xs {step_info['status_color']} ml-auto"
                                )

            # Add click handler
            def safe_session_click():
                try:
                    self._handle_session_click(session_id)
                except Exception as e:
                    log_exception(e, "Exception Error")
                    error(f"Error handling session click: {e}")

            session_item.on("click", safe_session_click)

            # Store reference
            self.session_items[session_id] = session_item

        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error creating session item: {e}")

    def _get_step_display_info(
        self, step_name: str, step_status: str = None
    ) -> Dict[str, str]:
        """
        Get comprehensive display information for workflow step using centralized configuration.

        Retrieves step icon, label, status information, and colors from UI config
        for consistent display across all UI components.

        Args:
            step_name: Current workflow step name
            step_status: Detailed step status (in_progress, awaiting_review, etc.)

        Returns:
            Dict containing icon, label, color, status_label, and status_color
        """
        from gojiberri.ui.config import (
            STEP_DISPLAY_CONFIG,
            STATUS_DISPLAY_CONFIG,
            DEFAULT_STEP_CONFIG,
            DEFAULT_STATUS_CONFIG,
        )

        # Get base step info from config
        base_info = STEP_DISPLAY_CONFIG.get(step_name)
        if not base_info:
            # Create fallback for unknown steps
            base_info = {
                **DEFAULT_STEP_CONFIG,
                "label": f"{step_name.replace('_', ' ').title()}",
            }

        # Get status info from config
        status_details = STATUS_DISPLAY_CONFIG.get(step_status, DEFAULT_STATUS_CONFIG)

        # Combine configuration information
        return {
            **base_info,
            "status_label": status_details["status_label"],
            "status_color": status_details["status_color"],
        }

    def _get_display_name(self, session_state) -> str:
        """
        Get appropriate display name for session item.

        Prioritizes patient name over session ID for better user experience.

        Args:
            session_state: Session state containing patient information

        Returns:
            str: Display name for session item
        """
        if session_state.patient_name and session_state.patient_name.strip():
            return session_state.patient_name.strip()
        else:
            return f"Session {session_state.session_id[:8]}..."

    def _get_time_display(self, session_state) -> str:
        """
        Get formatted time display for session item.

        Returns session time if available, empty string otherwise.

        Args:
            session_state: Session state containing time information

        Returns:
            str: Formatted time display or empty string
        """
        if session_state.session_time and session_state.session_time.strip():
            return session_state.session_time.strip()
        return ""

    def _handle_session_click(self, session_id: str):
        """Handle session item click."""
        try:
            if self.on_session_click:
                # Create async task for session switching
                import asyncio

                asyncio.create_task(self._safe_session_click(session_id))
        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error in session click handler: {e}")

    async def _safe_session_click(self, session_id: str):
        """Safely handle session click."""
        try:
            await self.on_session_click(session_id)
        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error switching session: {e}")

    def _handle_new_session(self):
        """Handle new session button click."""
        try:
            if self.on_new_session:
                # Create async task for new session creation
                import asyncio

                asyncio.create_task(self._safe_new_session())
        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error in new session handler: {e}")

    async def _safe_new_session(self):
        """Safely handle new session."""
        try:
            await self.on_new_session()
        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error creating new session: {e}")

    def cleanup(self):
        """Cleanup resources."""
        try:
            if self.refresh_timer:
                self.refresh_timer.deactivate()
            debug("Sidebar cleanup completed")
        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error during sidebar cleanup: {e}")


def create_dynamic_sidebar() -> ui.column:
    """
    Create the dynamic sidebar container for desktop application.

    Returns:
        Sidebar container with dynamic session management
    """
    with ui.column().classes(
        "w-64 !bg-[#EEEEEE] border-r border-black flex-shrink-0 h-full overflow-hidden"
    ) as sidebar_container:
        pass

    # Create dynamic sidebar instance
    dynamic_sidebar = DynamicSidebar(sidebar_container)

    # Initialize UI elements immediately (synchronous)
    dynamic_sidebar.initialize_ui()

    # Store initialization function for later use
    async def load_initial_data():
        await dynamic_sidebar.initialize_data()

    sidebar_container._load_initial_data = load_initial_data

    # Attach methods to container
    sidebar_container.set_callbacks = dynamic_sidebar.set_callbacks
    sidebar_container.refresh_sessions = dynamic_sidebar.refresh_sessions
    sidebar_container.cleanup = dynamic_sidebar.cleanup

    return sidebar_container
