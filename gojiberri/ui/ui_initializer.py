# ui_initializer.py
"""
Core application setup for the Neurology Tool Single Page Application with session manager initialization.
"""

from gojiberri.ui.routes import setup_routes
from gojiberri.ui.events import setup_event_handlers
from gojiberri.utils.logger import log_exception
from gojiberri.ui.utils.session_state import session_manager
from nicegui import app


def initialize_ui_app():
    """Initialize the Single Page Application with proper startup sequence."""
    # Set up single page route
    setup_routes()

    # Set up startup handlers in proper order
    app.on_startup(initialize_global_components)
    app.on_startup(initialize_session_manager)


async def initialize_global_components():
    """Initialize global components like loaders and event handlers"""
    try:
        setup_event_handlers()
        print("✅ Global components initialized")
    except Exception as e:
        log_exception(e, "Exception Error")
        log_exception(e, "initialize_global_components")
        print(f"⚠️ Error initializing global components: {e}")


async def initialize_session_manager():
    """Initialize the session manager and load workflow steps during startup."""
    try:
        print("🔄 Initializing session manager...")

        # Initialize session manager (this loads and caches workflow steps)
        await session_manager.initialize()

        print("✅ Session manager initialized successfully")
        print(
            f"📋 Workflow steps cached: {len(session_manager._cached_workflow_steps) if session_manager._cached_workflow_steps else 0}"
        )

    except Exception as e:
        log_exception(e, "Exception Error")
        log_exception(e, "initialize_session_manager")
        print(f"⚠️ Error initializing session manager: {e}")
        print("🔄 Application will continue with default workflow steps")


def cleanup_on_shutdown():
    """Cleanup resources on application shutdown."""
    try:
        # Cleanup session manager resources
        if hasattr(session_manager, "cleanup"):
            session_manager.cleanup()

        print("🧹 Application cleanup completed")

    except Exception as e:
        log_exception(e, "Exception Error")
        print(f"⚠️ Error during cleanup: {e}")


# Set up shutdown handler
app.on_shutdown(cleanup_on_shutdown)
