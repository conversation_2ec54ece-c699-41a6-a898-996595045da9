"""
Event handlers for the Neurology Tool application with proper async handling.
"""

from nicegui import ui

from gojiberri.ui.utils.ui_helpers import show_notification

# Create a global loading overlay that can be accessed from anywhere
GLOBAL_LOADER = {"element": None, "message_label": None, "is_initialized": False}


def setup_event_handlers():
    """Set up global event handlers and initialize the global loader."""
    # Initialize the global loader if needed
    if not GLOBAL_LOADER["is_initialized"]:
        # Create loader on app page
        with ui.element("div").classes(
            "fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
        ).style("display: none;") as loader:
            with ui.card().classes("p-4"):
                ui.spinner("dots", size="lg")
                GLOBAL_LOADER["message_label"] = ui.label("Loading...")

            GLOBAL_LOADER["element"] = loader
            GLOBAL_LOADER["is_initialized"] = True


def show_loader(message="Loading..."):
    """Show the global loader with a message."""
    if GLOBAL_LOADER["is_initialized"]:
        if GLOBAL_LOADER["message_label"]:
            GLOBAL_LOADER["message_label"].text = message
        if GLOBAL_LOADER["element"]:
            GLOBAL_LOADER["element"].style("display: flex;")


def hide_loader():
    """Hide the global loader."""
    if GLOBAL_LOADER["is_initialized"] and GLOBAL_LOADER["element"]:
        GLOBAL_LOADER["element"].style("display: none;")


def handle_profile_click():
    """
    Handle profile button click.
    This is a synchronous function.
    """
    # This is simple enough to not need async
    show_notification("Profile settings not implemented", duration=3.0)
