"""
Application settings and configuration.
"""

# CSS and JavaScript dependencies
HEAD_HTML = """
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/static/css/styles.css">
"""

# Material Icons mapping
ICONS = {
    "recording_icon": "mic",
    "transcript_icon": "notes",
    "document_upload_icon": "arrow_upward",
    "deidentification_icon": "shield",
    "document_generation_icon": "note_add",
    "bin_icon": "delete",
    "calendar_icon": "calendar_today",
    "person_icon": "person",
}

# Workflow step display configuration for UI components
STEP_DISPLAY_CONFIG = {
    "recording": {
        "icon": "mic",
        "label": "Recording",
        "color": "text-red-500",
    },
    "transcription": {
        "icon": "notes",
        "label": "Transcription",
        "color": "text-blue-500",
    },
    "document_upload": {
        "icon": "arrow_upward",
        "label": "Upload",
        "color": "text-orange-500",
    },
    "document_upload_ocr": {
        "icon": "arrow_upward",
        "label": "Upload",
        "color": "text-orange-500",
    },
    "deidentification": {
        "icon": "shield",
        "label": "De-ID",
        "color": "text-purple-500",
    },
    "document_generation": {
        "icon": "note_add",
        "label": "Generate",
        "color": "text-green-500",
    },
}

# Step status display configuration for detailed status indicators
STATUS_DISPLAY_CONFIG = {
    "not_started": {
        "status_label": "Not Started",
        "status_color": "text-gray-400",
    },
    "awaiting_user_input": {
        "status_label": "Awaiting Input",
        "status_color": "text-yellow-600",
    },
    "in_progress": {
        "status_label": "In Progress",
        "status_color": "text-blue-600",
    },
    "awaiting_review": {
        "status_label": "Review Needed",
        "status_color": "text-orange-600",
    },
    "completed": {
        "status_label": "Complete",
        "status_color": "text-green-600",
    },
    "skipped": {
        "status_label": "Skipped",
        "status_color": "text-gray-500",
    },
    "error": {
        "status_label": "Error",
        "status_color": "text-red-600",
    },
}

# Default fallback configuration for unknown steps/statuses
DEFAULT_STEP_CONFIG = {
    "icon": "help",
    "color": "text-gray-500",
}

DEFAULT_STATUS_CONFIG = {
    "status_label": "",
    "status_color": "text-gray-400",
}
