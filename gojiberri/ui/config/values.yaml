# config/values.yaml - Central Configuration with Service-Based Organization
# This file contains all configurable values used throughout UI application

# API & Network Configuration (keep existing)
api:
  base_url: "http://localhost:8000/"

# Recording service - consolidate all recording configs
recording:
  # Minimum recording duration in milliseconds
  min_duration_ms: 500

  # Minimum file size in bytes
  min_file_size_bytes: 512

  # Chunk size for recording in milliseconds (larger chunks for better performance)
  chunk_size_ms: 5000  # 5 seconds per chunk instead of 1 second


  # Audio format settings
  audio:
    # Preferred audio format
    format: "audio/webm"

    # Supported WebM codecs in order of preference
    supported_codecs:
      - "audio/webm;codecs=opus"
      - "audio/webm;codecs=vorbis"
      - "audio/webm"

    # Audio quality settings optimized for long recordings
    quality:
      echo_cancellation: true
      noise_suppression: true
      auto_gain_control: true

  validations:
    max_recording_duration_ms: 86400000
    min_duration_ms: 500
    min_file_size_bytes: 512
    valid_audio_extensions:
      - ".webm"
      - ".mp3"
      - ".wav"
      - ".ogg"
      - ".m4a"

# Session service
session:
  cache_ttl_seconds: 30
  list_limit: 50
  refresh_interval: 5.0

# Transcription service (move polling configs here)
transcription:
  interval: 2.0  # Move from polling.transcription_interval
  max_consecutive_errors: 5  # Move from polling.max_consecutive_errors

# Timeout Configuration (in seconds) - Centralized timeout management
timeouts:

  session:
    refresh_all: 10.0
    
  # API request timeouts
  api:
    default: 30.0
    upload: 600.0  # 10 minutes for large file uploads
  
  # Recording operation timeouts - Optimized for chunked transmission
  recording:
    start: 30.0  # Starting recording operation
    stop: 15.0   # Stopping recording operation (short since chunked transmission handles the rest)
    pause_resume: 10.0  # Pause/resume operations
    finalization: 300.0  # 5 minutes - waiting for chunked transmission to complete
  
  # JavaScript method call timeouts - DISABLED for recording operations
  javascript:
    default: 30.0  # General JavaScript operations


# Timer Intervals (in seconds)
timers:
  # UI update intervals
  ui:
    duration_display: 1.0
    cache_save: 60.0  # Less frequent caching for performance
  
  # Async operation monitoring
  async:
    slow_callback_threshold: 0.1  # 100ms threshold for long recordings

# Cache Configuration
cache:
  # Enable/disable caching
  enabled: true
  
  # Cache expiration time in seconds
  expiration: 7200  # 2 hours for long recordings

# Error Handling
error_handling:
  # Maximum retry attempts
  max_retries: 3
  
  # Retry delay in seconds
  retry_delay: 2.0  # Longer delay for stability


# Chunked Transmission Configuration (keep existing)
chunked_transmission:
  # Enable/disable chunked transmission for recordings
  enabled: true

  # Size of each transmission chunk in bytes
  chunk_size: 65536  # 64KB per transmission chunk

  # Interval for sending chunks in milliseconds
  send_interval_ms: 5000  # Send chunks every 5 seconds
