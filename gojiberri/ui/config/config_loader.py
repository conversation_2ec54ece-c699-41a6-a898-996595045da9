# config/config_loader.py
"""Configuration loader for centralized application settings."""

import yaml
from typing import Dict, Any
from pathlib import Path


class ConfigLoader:
    """Centralized configuration loader for application settings."""

    _instance = None
    _config = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigLoader, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._config is None:
            self._load_config()

    def _load_config(self):
        """Load configuration from YAML file."""
        try:
            config_path = Path(__file__).parent / "values.yaml"
            with open(config_path, "r") as file:
                self._config = yaml.safe_load(file)
        except Exception as e:
            from gojiberri.utils.logger import log_exception

            log_exception(e, "⚠️ Exception Error")
            self._config = self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Provide default configuration if YAML file is not available."""
        return {
            "api": {
                "base_url": "http://localhost:8000/",
            },
            "recording": {
                "min_duration_ms": 500,
                "min_file_size_bytes": 512,
                "chunk_size_ms": 5000,
                "audio": {
                    "format": "audio/webm",
                    "supported_codecs": [
                        "audio/webm;codecs=opus",
                        "audio/webm;codecs=vorbis",
                        "audio/webm",
                    ],
                    "quality": {
                        "echo_cancellation": True,
                        "noise_suppression": True,
                        "auto_gain_control": True,
                    },
                },
                "validations": {
                    "max_recording_duration_ms": 86400000,
                    "min_duration_ms": 500,
                    "min_file_size_bytes": 512,
                    "valid_audio_extensions": [".webm", ".mp3", ".wav", ".ogg", ".m4a"],
                },
            },
            "session": {
                "cache_ttl_seconds": 30,
                "list_limit": 50,
                "refresh_interval": 5.0,
            },
            "transcription": {
                "interval": 2.0,
                "max_consecutive_errors": 5,
            },
            "timeouts": {
                "api": {"default": 30.0, "upload": 600.0},
                "recording": {
                    "start": 30.0,
                    "stop": 15.0,
                    "pause_resume": 10.0,
                    "finalization": 300.0,
                },
                "javascript": {"default": 30.0},
                "session": {"refresh_all": 10.0},
            },
            "timers": {
                "ui": {
                    "duration_display": 1.0,
                    "cache_save": 60.0,
                },
                "async": {"slow_callback_threshold": 0.1},
            },
            "cache": {
                "enabled": True,
                "expiration": 7200,
            },
            "error_handling": {
                "max_retries": 3,
                "retry_delay": 2.0,
            },
            "chunked_transmission": {
                "enabled": True,
                "chunk_size": 65536,
                "send_interval_ms": 5000,
            },
        }

    def get(self, key_path: str, default=None):
        """
        Get configuration value using dot notation.

        Args:
            key_path: Dot-separated path to the configuration value (e.g., 'recording.min_duration_ms')
            default: Default value if key is not found

        Returns:
            Configuration value or default
        """
        try:
            keys = key_path.split(".")
            value = self._config

            for key in keys:
                value = value[key]

            return value
        except (KeyError, TypeError):
            return default

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get entire configuration section.

        Args:
            section: Configuration section name

        Returns:
            Configuration section dictionary
        """
        return self._config.get(section, {})


# Global configuration instance
config = ConfigLoader()
