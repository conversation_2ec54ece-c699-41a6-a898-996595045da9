from .api_client import APIClient
from .api_session import <PERSON><PERSON><PERSON>
from .api_recording import Recording<PERSON><PERSON>
from .api_workflow import Workflow<PERSON><PERSON>
from .api_transcription import TranscriptionAPI

# Create a single API client instance for the app
api_client = APIClient()

# Create individual API module instances
api = type(
    "API",
    (),
    {
        "sessions": SessionAPI(api_client),
        "recordings": RecordingAP<PERSON>(api_client),
        "workflow": WorkflowAPI(api_client),
        "transcription": TranscriptionAPI(api_client),
    },
)

__all__ = [
    "api",
    "APIClient",
    "SessionAPI",
    "RecordingAPI",
    "WorkflowAPI",
    "TranscriptionAPI",
]
