import httpx
from typing import Dict, Any, Optional

from gojiberri.ui.config import config


class BackendAPIError(Exception):
    """Custom exception for backend API errors with detailed error messages."""

    def __init__(
        self, status_code: int, backend_message: str, url: str, request_method: str = ""
    ):
        self.status_code = status_code
        self.backend_message = backend_message
        self.url = url
        self.request_method = request_method

        # Create user-friendly error message
        super().__init__(f"{backend_message}")


class APIClient:
    """Base API client for making HTTP requests to the backend with proper error handling."""

    def __init__(self):
        """
        Initialize the API client with the base URL.

        Args:
            base_url: Base URL for the API
        """
        self.base_url = config.get("api.base_url", "http://localhost:8000/")
        timeout = config.get("timeouts.api.default", 30.0)
        self.client = httpx.AsyncClient(base_url=self.base_url, timeout=timeout)
        self.is_connected = True  # Track connection status

    async def _process_response(self, response: httpx.Response) -> Dict[str, Any]:
        """
        Process HTTP response and extract backend error messages before raising exceptions.

        This helper ensures that backend error messages are properly extracted from
        the response body before any exceptions are raised, so users get meaningful
        error messages instead of generic HTTP status errors.

        Args:
            response: httpx Response object to process

        Returns:
            Dict containing response JSON data

        Raises:
            BackendAPIError: With backend's actual error message if status >= 400
            ConnectionError: If connection failed
        """
        try:
            # Check for HTTP errors BEFORE calling raise_for_status()
            if response.status_code >= 400:
                # Extract backend error message from response body
                backend_message = self._extract_backend_error_message(response)

                # Raise custom exception with backend's actual error message
                raise BackendAPIError(
                    status_code=response.status_code,
                    backend_message=backend_message,
                    url=str(response.url),
                    request_method=response.request.method,
                )

            # Success path - return JSON data
            self.is_connected = True
            return response.json()

        except BackendAPIError:
            # Re-raise our custom error as-is
            raise
        except httpx.ConnectError as e:
            self.is_connected = False
            raise ConnectionError(
                f"Could not connect to API server at {self.base_url}. Please check if the server is running."
            ) from e
        except Exception as e:
            # Handle any other unexpected errors
            print(f"Unexpected error processing response: {str(e)}")
            raise

    def _extract_backend_error_message(self, response: httpx.Response) -> str:
        """
        Extract meaningful error message from backend response.

        Tries multiple strategies to get the most useful error message:
        1. FastAPI detail field (most common)
        2. Generic message field
        3. Raw response text
        4. Fallback to HTTP status

        Args:
            response: HTTP response with error status

        Returns:
            str: Best available error message
        """
        try:
            # Try to parse JSON error response
            error_data = response.json()

            # FastAPI typically uses "detail" field for error messages
            if isinstance(error_data, dict):
                # Try common error message fields
                if "detail" in error_data:
                    detail = error_data["detail"]
                    # Handle both string and structured detail
                    if isinstance(detail, str):
                        return detail
                    elif isinstance(detail, list) and len(detail) > 0:
                        # Validation errors are often lists
                        return str(detail[0])
                    else:
                        return str(detail)

                elif "message" in error_data:
                    return str(error_data["message"])

                elif "error" in error_data:
                    return str(error_data["error"])

            # If we got JSON but no recognizable error field
            return f"Server error: {str(error_data)}"

        except (ValueError, TypeError):
            # Response is not valid JSON - try raw text
            response_text = response.text.strip()
            if response_text:
                return f"Server error: {response_text}"

        # Final fallback - just use HTTP status
        return (
            f"HTTP {response.status_code}: {response.reason_phrase or 'Unknown error'}"
        )

    async def get(
        self, endpoint: str, params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a GET request to the API.

        Args:
            endpoint: API endpoint
            params: Query parameters

        Returns:
            Response data as a dictionary
        """
        response = await self.client.get(endpoint, params=params)
        return await self._process_response(response)

    async def post(
        self, endpoint: str, data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a POST request to the API.

        Args:
            endpoint: API endpoint
            data: Request data

        Returns:
            Response data as a dictionary
        """
        response = await self.client.post(endpoint, json=data)
        return await self._process_response(response)

    async def put(
        self, endpoint: str, data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a PUT request to the API.

        Args:
            endpoint: API endpoint
            data: Request data

        Returns:
            Response data as a dictionary
        """
        response = await self.client.put(endpoint, json=data)
        return await self._process_response(response)

    async def patch(
        self, endpoint: str, data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a PATCH request to the API.

        Args:
            endpoint: API endpoint
            data: Request data

        Returns:
            Response data as a dictionary
        """
        response = await self.client.patch(endpoint, json=data)
        return await self._process_response(response)

    async def delete(self, endpoint: str) -> Dict[str, Any]:
        """
        Make a DELETE request to the API.

        Args:
            endpoint: API endpoint

        Returns:
            Response data as a dictionary
        """
        response = await self.client.delete(endpoint)
        return await self._process_response(response)

    async def upload_file(
        self, endpoint: str, file_path: str, form_data: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Upload a file to the API.

        Args:
            endpoint: API endpoint
            file_path: Path to the file
            form_data: Additional form data

        Returns:
            Response data as a dictionary
        """
        files = {"file": open(file_path, "rb")}
        response = await self.client.post(endpoint, files=files, data=form_data)
        return await self._process_response(response)

    async def upload_binary(
        self,
        endpoint: str,
        binary_data: bytes,
        filename: str,
        form_data: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Upload binary data to the API.

        Args:
            endpoint: API endpoint
            binary_data: Binary data to upload
            filename: Name to give the file
            form_data: Additional form data

        Returns:
            Response data as a dictionary
        """
        files = {"file": (filename, binary_data)}
        response = await self.client.post(endpoint, files=files, data=form_data)
        return await self._process_response(response)
