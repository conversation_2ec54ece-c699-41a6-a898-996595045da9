# api_client/api_workflow.py
from typing import Dict, Any, List
from .api_client import APIClient
from gojiberri.utils.logger import error, log_exception


class WorkflowAPI:
    """API client for workflow-related operations - Fixed to match backend structure."""

    def __init__(self, api_client: APIClient):
        """
        Initialize the workflow API client.

        Args:
            api_client: Base API client instance
        """
        self.api_client = api_client
        self.endpoint = "/workflow"

    async def get_workflow_steps(self) -> List[Dict[str, Any]]:
        """
        Get workflow steps from backend - NO FALLBACK TO DEFAULTS.

        Backend endpoint: GET /sessions/workflow/steps
        Returns WorkflowStepsResponse with steps containing:
        - step_name, icon, label, status, requires_user_start, requires_user_review

        Returns:
            List of workflow steps from backend or empty list if backend fails
        """
        try:
            # Use correct backend endpoint
            response = await self.api_client.get(f"{self.endpoint}/steps")

            # Backend returns WorkflowStepsResponse with 'steps' field
            if isinstance(response, dict) and "steps" in response:
                steps = response["steps"]
                if isinstance(steps, list):
                    return steps
                else:
                    error(f"Backend returned invalid steps format: {type(steps)}")
                    return []
            else:
                error(
                    f"Backend returned invalid workflow steps response format: {type(response)}"
                )
                return []

        except ConnectionError as e:
            error(f"Connection error - cannot reach backend: {e}")
            return []
        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Backend error getting workflow steps: {e}")
            return []
