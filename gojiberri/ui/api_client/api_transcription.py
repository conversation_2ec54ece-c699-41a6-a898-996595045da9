# gojiberri/ui/api_client/api_transcription.py
"""
API client for transcription operations with gojiberri compliance.

This module provides transcription-related API operations including:
- Progress polling using the backend progress endpoint
- Results fetching using the new consistent results endpoint pattern
- Status verification for transcription completion
"""

from typing import Dict, Any
from gojiberri.ui.api_client.api_client import APIClient
from gojiberri.utils.logger import error


class TranscriptionAPI:
    """
    API client for transcription operations using consistent endpoint patterns.

    This class provides a simplified interface to transcription operations
    by utilizing the existing StepProgressResponse and new StepResultResponse
    endpoints with consistent URL patterns.
    """

    def __init__(self, api_client: APIClient):
        """
        Initialize the transcription API client.

        Args:
            api_client: Base API client instance for HTTP operations
        """
        self.api_client = api_client
        self.endpoint = "/sessions"

    async def get_transcription_progress_and_status(
        self, session_id: str
    ) -> Dict[str, Any]:
        """
        Get comprehensive transcription progress and status from progress endpoint.

        This method utilizes the existing StepProgressResponse to get both
        progress percentage and completion status in a single API call.

        Args:
            session_id: Session ID for status retrieval

        Returns:
            Dict containing:
                - session_id: Session identifier
                - percentage: Progress percentage (0-100)
                - status: Current status ("in_progress", "completed", "error")
        """

        try:
            response = await self.api_client.get(
                f"{self.endpoint}/{session_id}/step/transcription/progress"
            )

            return {
                "session_id": response.get("session_id", session_id),
                "percentage": response.get("percentage", 0),
                "status": response.get("status", "in_progress"),
                "backend_available": True,
            }
        except Exception as e:
            error(f"Backend progress unavailable: {e}")
            raise

    async def get_transcription_result(self, session_id: str) -> Dict[str, Any]:
        """
        Get transcription result using the new consistent results endpoint.

        This method uses the new /sessions/{session_id}/results/transcription endpoint
        which follows the same pattern as the progress endpoint for consistency.

        Args:
            session_id: Session ID for result retrieval

        Returns:
            Dict containing:
                - session_id: Session identifier
                - available: Boolean indicating result availability
                - transcription: Transcription data (if available)
                - reason: Unavailability reason (if not available)
        """
        try:

            # Use new consistent results endpoint
            response = await self.api_client.get(
                f"{self.endpoint}/{session_id}/step/transcription/results"
            )

            if response.get("available", False) and response.get("result_data"):
                transcription_text = response["result_data"].get("transcription", "")

                return {
                    "session_id": session_id,
                    "available": True,
                    "transcription": {
                        "full_text": transcription_text,
                    },
                }
            else:
                # Return unavailable status with reason
                return {
                    "session_id": session_id,
                    "reason": response.get(
                        "error_message", "transcription_not_available"
                    ),
                }

        except Exception as e:
            error(f"Backend result unavailable: {e}")
            return {
                "session_id": session_id,
                "reason": response.get("error_message", "transcription_not_available"),
            }

    async def verify_transcription_complete(self, session_id: str) -> bool:
        """
        Verify transcription completion using progress endpoint.

        This method provides a simple boolean check for transcription completion
        by verifying both status and progress percentage.

        Args:
            session_id: Session ID for verification

        Returns:
            bool: True if transcription is complete (status == "completed" and progress >= 100)
        """
        try:
            progress_data = await self.get_transcription_progress_and_status(session_id)

            # Verify both status and progress for complete verification
            is_complete = (
                progress_data.get("status") == "completed"
                and progress_data.get("percentage", 0) >= 100
            )

            return is_complete

        except Exception as e:
            error(f"Error verifying transcription completion: {e}")
            return False
