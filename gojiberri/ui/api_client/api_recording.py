# api_client/api_recording.py
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from gojiberri.ui.utils.validators import validate_recording_request
from gojiberri.utils.logger import debug
from .api_client import APIClient


class RecordingAPI:
    """API client for recording-related operations - Updated for JSON-only requests."""

    def __init__(self, api_client: APIClient):
        """
        Initialize the recording API client.

        Args:
            api_client: Base API client instance
        """
        self.api_client = api_client
        self.endpoint = "/sessions"

    async def complete_recording(
        self,
        session_id: str,
        recording_data: Dict[str, Any],
        audio_data: Optional[bytes] = None,  # Not used for now - on hold
    ) -> Dict[str, Any]:
        """
        Complete a recording for a session using JSON data.
        Audio file uploading is on hold - just testing API functionality.

        Backend endpoint: POST /recordings/{session_id}/complete
        Request body: CompleteRecordingRequest (JSON)

        Args:
            session_id: Session ID
            recording_data: Recording metadata
            audio_data: Binary audio data (not used for now - on hold)

        Expected recording_data format:
            {
                "file_path": str,
                "duration_milliseconds": int (optional),
                "end_time": str (ISO format datetime)
            }

        Returns:
            Response with recording details
        """

        debug(f"Recording data to be submitted: {recording_data}")

        is_valid, error_msg = validate_recording_request(recording_data)

        if not is_valid:
            raise ValueError(f"Invalid recording data: {error_msg}")
        # Prepare form data

        form_data = {
            "duration_milliseconds": str(
                recording_data.get("duration_milliseconds", 0)
            ),
            "end_time": recording_data.get(
                "end_time", datetime.now(timezone.utc).isoformat()
            ),
        }
        debug(f"Form data being sent: {form_data}")

        # If you have actual audio data, send it as file
        if audio_data:
            filename = recording_data.get("file_path", f"recording_{session_id}.webm")
            response = await self.api_client.upload_binary(
                f"{self.endpoint}/{session_id}/recording",
                binary_data=audio_data,
                filename=filename,
                form_data=form_data,
            )

        return response
