# api_client/api_session.py
from typing import Dict, Any, Optional

from gojiberri.models.enums import WorkflowType
from gojiberri.ui.utils.date_time_utils import parse_session_datetime

from .api_client import APIClient


class SessionAPI:
    """API client for session-related operations - Updated to match backend."""

    def __init__(self, api_client: APIClient):
        """
        Initialize the session API client.

        Args:
            api_client: Base API client instance
        """
        self.api_client = api_client
        self.endpoint = "/sessions"

    async def create_session(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new recording session.

        Backend endpoint: POST /sessions
        Request body: CreateSessionRequest

        Args:
            session_data: Session creation data

        Expected format:
            {
                "patient_name": str,
                "patient_dob": str (YYYY-MM-DD format),
                "workflow_type": str (optional, defaults to "document_generation")
            }

        Returns:
            Response with session_id
            {
                "session_id": str
            }
        """
        # Transform frontend data to match backend CreateSessionRequest
        request_data = {
            "patient_name": session_data.get("patient_name", ""),
            "patient_dob": session_data.get("patient_dob", ""),
            "workflow_type": session_data.get(
                "workflow_type", WorkflowType.DOCUMENT_GENERATION.value
            ),
        }

        response = await self.api_client.post(self.endpoint, data=request_data)

        # Transform response to match frontend expectations
        return {
            "id": response.get("session_id"),
            "session_id": response.get("session_id"),
            **response,
        }

    async def get_session(self, session_id: str) -> Dict[str, Any]:
        """
        Get session data by ID.

        Backend endpoint: GET /sessions/{session_id}
        Response: GetSessionResponse (Session object)

        Args:
            session_id: Session ID

        Returns:
            Complete session data
        """
        response = await self.api_client.get(f"{self.endpoint}/{session_id}")

        session_date, session_time = parse_session_datetime(
            response.get("session_start", "")
        )

        # Transform backend response to match frontend expectations
        return {
            "id": response.get("session_id"),
            "session_id": response.get("session_id"),
            "patient_name": response.get("patient_name", ""),
            "patient_dob": response.get("patient_dob", ""),
            "status": response.get("session_status", "in_progress"),
            "current_step": self._extract_current_step(response),
            "session_date": session_date,
            "session_time": session_time,
            "workflow_type": response.get(
                "workflow_type", WorkflowType.DOCUMENT_GENERATION.value
            ),
            "recording_id": response.get("recording_id"),
            "last_updated": response.get("last_updated"),
            **response,
        }

    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """
        Get session status and current workflow step.

        Backend endpoint: GET /sessions/{session_id}/status
        Response: GetSessionStatusResponse

        Args:
            session_id: Session ID

        Returns:
            Session status data
            {
                "session_id": str,
                "status": str,
                "current_step": StepProgress
            }
        """
        response = await self.api_client.get(f"{self.endpoint}/{session_id}/status")
        return response

    async def list_sessions(
        self, status: Optional[str] = None, limit: int = 10, offset: int = 0
    ) -> Dict[str, Any]:
        """
        List recording sessions with optional filtering.

        Backend endpoint: GET /sessions
        Response: list[GetSessionResponse]

        Args:
            status: Filter by status (e.g., "in_progress", "completed")
            limit: Maximum number of sessions to return
            offset: Pagination offset

        Returns:
            List of session data objects
        """
        params = {"limit": limit, "offset": offset}
        if status:
            params["status"] = status

        response = await self.api_client.get(self.endpoint, None)

        # Backend returns list directly, transform to match frontend expectations
        if isinstance(response, list):
            sessions = []
            for session in response:
                transformed_session = {
                    "id": session.get("session_id"),
                    "session_id": session.get("session_id"),
                    "patient_name": session.get("patient_name", ""),
                    "patient_dob": session.get("patient_dob", ""),
                    "status": session.get("session_status", "in_progress"),
                    "current_step": self._extract_current_step(session),
                    "session_date": (
                        session.get("session_start", "").split("T")[0]
                        if session.get("session_start")
                        else ""
                    ),
                    "session_time": (
                        session.get("session_start", "").split("T")[1][:8]
                        if session.get("session_start")
                        else ""
                    ),
                    "workflow_type": session.get(
                        "workflow_type", WorkflowType.DOCUMENT_GENERATION.value
                    ),
                    "recording_id": session.get("recording_id"),
                    "last_updated": session.get("last_updated"),
                }
                sessions.append(transformed_session)

            return {"sessions": sessions, "total": len(sessions)}

        return {"sessions": [], "total": 0}

    def _extract_current_step(self, session_data: Dict[str, Any]) -> str:
        """
        Extract current step from session data.

        Args:
            session_data: Raw session data from backend

        Returns:
            Current step name
        """
        current_step = session_data.get("current_step")
        if isinstance(current_step, dict):
            return current_step.get("step", "recording")
        elif isinstance(current_step, str):
            return current_step
        return "recording"
