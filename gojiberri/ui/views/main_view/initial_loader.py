# views/main_view/initial_loader.py
"""
Initial content loading logic for application startup - IMPROVED ERROR HANDLING.
"""

from gojiberri.ui.config import config
from gojiberri.ui.utils.session_state import session_manager
from gojiberri.utils.logger import debug, error, log_exception, warning, info


async def load_initial_content():
    """Load initial content - either existing sessions or default workflow with better error handling."""
    try:

        # Try to load existing sessions
        sessions = await _safe_refresh_sessions()

        if sessions:
            info(f"Found {len(sessions)} existing sessions")
            await _load_latest_session(sessions)
        else:
            info("No existing sessions found, showing default workflow")
            await _show_default_workflow()

    except Exception as e:
        log_exception(e, "Critical error loading initial content")
        error(f"Failed to load initial content: {e}")


async def _safe_refresh_sessions():
    """Safely refresh sessions with timeout and error handling."""
    try:

        # Add timeout to prevent hanging
        import asyncio

        timeout = config.get("timeouts.session.refresh_all", 10.0)
        sessions = await asyncio.wait_for(
            session_manager.refresh_all_sessions(), timeout=timeout
        )

        debug(f"Successfully loaded {len(sessions)} sessions")
        return sessions

    except asyncio.TimeoutError:
        warning("Session refresh timed out after 10 seconds")
        return []
    except ConnectionError as e:
        warning(f"Connection error refreshing sessions: {e}")
        return []
    except Exception as e:
        log_exception(e, "Error refreshing sessions")
        warning(f"Failed to refresh sessions: {e}")
        return []


async def _load_latest_session(sessions):
    """Load the most recent session with error handling."""
    try:
        latest_session = _get_latest_session(sessions)

        if not latest_session:
            warning("No valid latest session found")
            await _show_default_workflow()
            return

        # Try to switch to the latest session
        await session_manager.switch_to_session(latest_session.session_id)

    except Exception as e:
        log_exception(e, "Error loading latest session")
        error(f"Failed to load latest session: {e}")

        # Fallback to default workflow
        warning("Falling back to default workflow")
        await _show_default_workflow()


async def _show_default_workflow():
    """Show default workflow with error handling."""
    try:
        await session_manager.show_default_workflow()
        info("Default workflow displayed successfully")

    except Exception as e:
        log_exception(e, "Error showing default workflow")
        error(f"Failed to show default workflow: {e}")


def _get_latest_session(sessions):
    """Get the most recent session from the list with error handling."""
    try:
        if not sessions:
            return None

        # Filter out sessions without required data
        valid_sessions = []
        for session in sessions:
            if hasattr(session, "session_date") and hasattr(session, "session_time"):
                if session.session_date and session.session_time:
                    valid_sessions.append(session)

        if not valid_sessions:
            debug("No sessions with valid date/time found")
            return None

        # Sort by date and time
        latest_session = max(
            valid_sessions, key=lambda s: f"{s.session_date} {s.session_time}"
        )

        return latest_session

    except Exception as e:
        log_exception(e, "Error finding latest session")
        error(f"Failed to identify latest session: {e}")
        return None
