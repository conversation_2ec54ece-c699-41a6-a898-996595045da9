# views/main_view/content_system.py
"""
Dynamic content system initialization and management - FIXED CONTENT CLEARING.
"""

from nicegui import ui
from gojiberri.ui.utils.session_state import session_manager, SessionState
from gojiberri.utils.logger import log_exception, error
from gojiberri.ui.components.loading_spinner import create_loading_spinner

from .callbacks import setup_sidebar_callbacks, setup_workflow_callbacks
from .step_content import create_step_content
from .initial_loader import load_initial_content


async def initialize_content_system(
    sidebar_container,
    workflow_container,
    content_container,
    content_loader,
):
    """Initialize the dynamic content system for desktop with proper content clearing."""

    # Content state management
    content_state = {
        "current_component": None,
        "is_loading": False,
        "current_step": None,
        "loading_spinner": None,
    }

    async def load_content_for_step(step_name: str, session_state: SessionState = None):
        """Load content component for workflow step with proper clearing and loading."""

        content_state["is_loading"] = True

        try:

            # Show loading spinner
            await _show_content_loading(content_container, content_state, step_name)

            # Wait a moment to ensure UI updates
            import asyncio

            await asyncio.sleep(0.1)

            # Clear existing content completely
            await _clear_content_safely(content_container, content_state)

            # Load new content
            await _load_new_content(
                content_container, content_state, step_name, session_state
            )

        except Exception as e:
            log_exception(e, "Error loading content")
            await _show_content_error(content_container, content_state, str(e))
        finally:
            content_state["is_loading"] = False
            # Hide the old loading overlay
            content_loader["hide"]()

    def update_workflow_display(workflow_steps: list, current_step: str):
        """Update the workflow display."""
        try:
            workflow_container.update_workflow(workflow_steps, current_step)
        except Exception as e:
            log_exception(e, "Error updating workflow display")

    # Set callbacks with session manager
    session_manager.set_callbacks(
        content_callback=load_content_for_step,
        workflow_callback=update_workflow_display,
    )

    # Initialize sidebar and workflow with callbacks
    await setup_sidebar_callbacks(sidebar_container)
    setup_workflow_callbacks(workflow_container)

    # Load sidebar data after callbacks are set up
    await _load_sidebar_data(sidebar_container)

    # Load initial content (sessions or default workflow)
    await load_initial_content()


async def _show_content_loading(content_container, content_state, step_name):
    """Show loading spinner for content transition."""
    try:
        # Clear container first
        content_container.clear()

        # Create loading spinner
        with content_container:
            with ui.column().classes(
                "w-full h-full flex items-center justify-center py-12"
            ) as loading_container:
                ui.label(f"Loading {step_name.replace('_', ' ').title()}...").classes(
                    "text-xl text-gray-600 mb-8"
                )

                # Create loading spinner
                spinner = create_loading_spinner(
                    size=120,
                    show_percentage=False,
                    animation_duration=1.0,
                    text_color="#374151",
                )

                content_state["loading_spinner"] = spinner
                content_state["current_component"] = loading_container

        # Force UI update
        ui.update()

    except Exception as e:
        log_exception(e, "Error showing content loading")


async def _clear_content_safely(content_container, content_state):
    """Safely clear all content from the container."""
    try:

        # Stop any existing loading spinner
        if content_state["loading_spinner"]:
            content_state["loading_spinner"].stop_polling()
            content_state["loading_spinner"] = None

        # Clear the container
        content_container.clear()

        # Reset state
        content_state["current_component"] = None
        content_state["current_step"] = None

        # Force UI update
        ui.update()

    except Exception as e:
        log_exception(e, "Error clearing content")
        # Force clear even if there's an error
        content_container.clear()


async def _load_new_content(content_container, content_state, step_name, session_state):
    """Load new content into the cleared container."""
    try:

        # Create new content
        with content_container:
            new_component = await create_step_content(step_name, session_state)
            content_state["current_component"] = new_component
            content_state["current_step"] = step_name

        # Force UI update
        ui.update()

    except Exception as e:
        log_exception(e, f"Error creating content for step {step_name}")
        raise


async def _show_content_error(content_container, content_state, error_message):
    """Show error message in content area."""
    try:
        content_container.clear()
        content_state["current_component"] = None

        with content_container:
            with ui.column().classes(
                "w-full h-full flex items-center justify-center p-8"
            ):
                ui.icon("error").classes("text-6xl text-red-400 mb-4")
                ui.label("Error Loading Content").classes(
                    "text-2xl font-bold text-red-600 mb-2"
                )
                ui.label(error_message).classes("text-gray-600 text-center mb-6")

                ui.button(
                    "Go to Recording",
                    icon="mic",
                    on_click=lambda: _safe_navigate_to_recording(),
                ).classes("bg-blue-500 text-white")

        ui.update()

    except Exception as e:
        log_exception(e, "Error showing content error")


def _safe_navigate_to_recording():
    """Safely navigate to recording step."""
    try:
        import asyncio

        asyncio.create_task(session_manager.show_default_workflow())
    except Exception as e:
        log_exception(e, "Error navigating to recording")


async def _load_sidebar_data(sidebar_container):
    """Load sidebar data with error handling."""
    if hasattr(sidebar_container, "_load_initial_data"):
        try:
            await sidebar_container._load_initial_data()
        except Exception as e:
            log_exception(e, "Error loading sidebar data")
            error(f"Failed to load sidebar data: {e}")
