# views/main_view/main_app.py
"""
Main application page orchestrator - IMPROVED INITIALIZATION AND ERROR HANDLING.
"""

from nicegui import ui
from gojiberri.ui.utils.session_state import session_manager
from gojiberri.ui.layout.sidebar import create_dynamic_sidebar
from gojiberri.ui.layout.workflow import create_dynamic_workflow_container
from gojiberri.ui.components.loading import create_loading_overlay
from gojiberri.ui.components.buttons import create_profile_button
from gojiberri.ui.events import handle_profile_click
from gojiberri.utils.logger import debug, error, log_exception, warning

from .content_system import initialize_content_system


async def create_main_app_page():
    """Create the main application page for desktop with improved error handling."""
    try:

        # Initialize session manager first
        await _initialize_session_manager()

        # Create main UI layout
        layout_components = _create_main_layout()

        if not layout_components:
            error("Failed to create main layout")
            _create_fallback_layout()
            return

        sidebar_container, workflow_container, content_container, content_loader = (
            layout_components
        )

        # Initialize the dynamic content system
        await initialize_content_system(
            sidebar_container,
            workflow_container,
            content_container,
            content_loader,
        )

    except Exception as e:
        log_exception(e, "Critical error creating main app page")
        error(f"Failed to create main app page: {e}")
        _create_emergency_fallback()


async def _initialize_session_manager():
    """Initialize the session manager with proper error handling."""
    try:

        # Check if already initialized
        if session_manager._initialized:
            debug("Session manager already initialized")
            return

        await session_manager.initialize()
        debug("Session manager initialized successfully")

    except Exception as e:
        log_exception(e, "Error initializing session manager")
        error(f"Session manager initialization failed: {e}")
        warning("App will continue with limited functionality")
        # Don't re-raise - allow app to continue with degraded functionality


def _create_main_layout():
    """Create the main UI layout structure with error handling."""
    try:

        with ui.column().classes("w-full h-screen flex flex-row overflow-hidden"):
            # Create dynamic sidebar
            sidebar_container = create_dynamic_sidebar()

            if not sidebar_container:
                error("Failed to create sidebar")
                return None

            # Create main content column
            with ui.column().classes("flex-grow h-full bg-white"):
                # Top section with profile and workflow
                with ui.row().classes("w-full p-4"):
                    create_profile_button(on_click=handle_profile_click)
                    workflow_container = create_dynamic_workflow_container()

                    if not workflow_container:
                        error("Failed to create workflow container")
                        return None

                # Dynamic content area with proper sizing
                content_container = (
                    ui.element("div")
                    .classes("w-full flex-grow overflow-auto bg-white")
                    .style("min-height: 0; flex: 1;")
                )

                # Loading overlay for content changes
                content_loader = create_loading_overlay(
                    container=content_container,
                    message="Loading content...",
                    full_screen=False,
                )

                if not content_loader:
                    error("Failed to create content loader")
                    return None

        return sidebar_container, workflow_container, content_container, content_loader

    except Exception as e:
        log_exception(e, "Error creating main layout")
        error(f"Failed to create main layout: {e}")
        return None


def _create_fallback_layout():
    """Create a fallback layout when main layout creation fails."""
    try:

        with ui.column().classes("w-full h-screen p-8 bg-gray-50"):
            # Header
            with ui.row().classes("w-full justify-between items-center mb-8"):
                ui.label("Neurology Tool").classes("text-3xl font-bold text-gray-800")
                create_profile_button(on_click=handle_profile_click)

            # Error message
            with ui.card().classes(
                "w-full max-w-2xl mx-auto p-6 border-l-4 border-orange-500"
            ):
                ui.label("Limited Functionality Mode").classes(
                    "text-xl font-bold text-orange-600 mb-2"
                )
                ui.label(
                    "The application is running in limited mode due to initialization issues. "
                    "Some features may not be available."
                ).classes("text-gray-600 mb-4")

                with ui.row().classes("gap-4"):
                    ui.button(
                        "Reload Application",
                        icon="refresh",
                        on_click=lambda: ui.open("/"),
                    ).classes("bg-blue-500 text-white")

                    ui.button(
                        "Start Recording",
                        icon="mic",
                        on_click=lambda: _safe_start_recording(),
                    ).classes("bg-green-500 text-white")

            # Simple content area
            with ui.column().classes("w-full flex-grow mt-8"):
                ui.label(
                    "Please reload the application or try starting a new recording session."
                ).classes("text-center text-gray-500")

    except Exception as e:
        log_exception(e, "Error creating fallback layout")
        _create_emergency_fallback()


def _create_emergency_fallback():
    """Create emergency fallback when everything else fails."""
    try:
        ui.label("Application Error").classes("text-2xl font-bold text-red-600 p-8")
        ui.label("Please refresh the page to restart the application.").classes(
            "text-gray-600 p-4"
        )
        ui.button("Refresh", on_click=lambda: ui.open("/")).classes(
            "m-4 bg-blue-500 text-white"
        )

    except Exception as e:
        log_exception(e, "Critical error in emergency fallback")
        # At this point, we can only hope the page shows something


def _safe_start_recording():
    """Safely start a recording session."""
    try:
        import asyncio

        async def start_recording():
            try:
                await session_manager.show_default_workflow()
            except Exception as e:
                log_exception(e, "Error starting recording from fallback")
                ui.open("/")  # Last resort - reload page

        asyncio.create_task(start_recording())

    except Exception as e:
        log_exception(e, "Error in safe_start_recording")
        ui.open("/")  # Fallback to reload
