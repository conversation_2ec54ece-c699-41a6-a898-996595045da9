# views/main_view/step_content.py
"""
Step content creation logic with optimized status-first approach.
"""

from nicegui import ui
from gojiberri.models.enums import StepType
from gojiberri.ui.utils.session_state import SessionState, session_manager
from gojiberri.utils.logger import debug, error, log_exception, warning
from gojiberri.ui.views.transcription import (
    create_transcription_content as create_transcription_view,
)


async def create_step_content(step_name: str, session_state: SessionState = None):
    """Create content component for any workflow step with optimized data loading."""
    try:
        debug(f"Creating content for step: {step_name}")

        # Normalize step name
        step_name = step_name.lower().strip()

        # Create content based on step name with appropriate data loading strategy
        if step_name == StepType.RECORDING.value:
            content = await create_recording_content_optimized(session_state)
        elif step_name == StepType.TRANSCRIPTION.value:
            content = await create_transcription_view(session_state)
        elif step_name in [
            StepType.DOCUMENT_UPLOAD_OCR.value,
            StepType.DEIDENTIFICATION.value,
            StepType.DOCUMENT_GENERATION.value,
        ]:
            content = create_coming_soon_content(step_name)
        else:
            warning(f"Unknown step name: {step_name}, showing coming soon")
            content = create_coming_soon_content(step_name)

        return content

    except Exception as e:
        log_exception(e, f"Error creating content for step {step_name}")
        error(f"Failed to create content for step {step_name}: {e}")
        # Return error content instead of raising
        return create_error_content(step_name, str(e))


async def create_recording_content_optimized(session_state: SessionState = None):
    """Create recording content with optimized data loading strategy."""
    try:
        from gojiberri.ui.views.recording import create_recording_content

        # For recording step, we need full session data for form prefilling
        # Check if we have minimal session data and need to load full data
        if session_state:
            # If we have no patient data, this was likely a status-only load
            if not session_state.patient_name and not session_state.patient_dob:
                debug("Recording step needs full session data, loading...")
                from gojiberri.ui.views.recording.recording_session import (
                    ensure_full_session_data_for_forms,
                )

                session_state = await ensure_full_session_data_for_forms(session_state)

        content = await create_recording_content(session_state)
        return content

    except Exception as e:
        log_exception(e, "Error creating recording content")
        error(f"Failed to create recording content: {e}")
        # Return fallback content
        return create_fallback_recording_content(session_state)


def create_coming_soon_content(step_name: str = ""):
    """Create coming soon content for unimplemented steps."""
    try:

        with ui.column().classes(
            "w-full h-full flex items-center justify-center p-6"
        ) as content:
            # Icon
            ui.icon("construction").classes("text-8xl text-gray-400 mb-6")

            # Title
            ui.label("Coming Soon").classes("text-4xl font-bold text-gray-600 mb-4")

            # Description
            if step_name:
                step_display = step_name.replace("_", " ").title()
                ui.label(
                    f"{step_display} functionality will be available soon"
                ).classes("text-lg text-gray-500 text-center mb-8")
            else:
                ui.label("This feature is under development").classes(
                    "text-lg text-gray-500 text-center mb-8"
                )

        return content

    except Exception as e:
        log_exception(e, "Error creating coming soon content")
        return create_simple_error_content(f"Error creating coming soon content: {e}")


def create_error_content(step_name: str, error_message: str):
    """Create error content when step content creation fails."""
    try:
        with ui.column().classes(
            "w-full h-full flex items-center justify-center p-6"
        ) as content:
            # Error icon
            ui.icon("error").classes("text-8xl text-red-400 mb-6")

            # Error title
            ui.label("Content Load Error").classes(
                "text-3xl font-bold text-red-600 mb-4"
            )

            # Error message
            ui.label(
                f"Failed to load {step_name.replace('_', ' ').title()} content"
            ).classes("text-lg text-gray-600 text-center mb-2")

            # Technical details (collapsed by default)
            with ui.expansion("Technical Details", icon="info").classes(
                "w-full max-w-md mb-6"
            ):
                ui.label(error_message).classes("text-sm text-gray-500 break-words")

            # Recovery actions
            with ui.row().classes("gap-4"):
                ui.button(
                    "Try Again",
                    icon="refresh",
                    on_click=lambda: _retry_load_content(step_name),
                ).classes("bg-blue-500 text-white")

                ui.button(
                    "Go to Recording",
                    icon="mic",
                    on_click=lambda: _navigate_to_recording(),
                ).classes("bg-green-500 text-white")

        return content

    except Exception as e:
        log_exception(e, "Error creating error content")
        return create_simple_error_content(f"Critical error: {e}")


def create_fallback_recording_content(session_state: SessionState = None):
    """Create fallback recording content when main creation fails."""
    try:
        with ui.column().classes(
            "w-full h-full flex items-center justify-center p-6"
        ) as content:
            # Title
            ui.label("Recording Session").classes("text-4xl font-bold text-center mb-6")

            # Status message
            ui.label("Recording interface is loading...").classes(
                "text-lg text-blue-600 text-center mb-8"
            )

            # Session info if available
            if session_state:
                with ui.card().classes("w-full max-w-md mb-6"):
                    ui.label(f"Session: {session_state.session_id[:8]}...").classes(
                        "font-semibold"
                    )
                    if session_state.patient_name:
                        ui.label(f"Patient: {session_state.patient_name}")
                    ui.label(f"Status: {session_state.status}")
                    ui.label(f"Step: {session_state.current_step}")

            # Actions
            with ui.row().classes("gap-4"):
                ui.button(
                    "Refresh",
                    icon="refresh",
                    on_click=lambda: _retry_load_content("recording"),
                ).classes("bg-blue-500 text-white")

                ui.button(
                    "New Session", icon="add", on_click=lambda: _navigate_to_recording()
                ).classes("bg-green-500 text-white")

        return content

    except Exception as e:
        log_exception(e, "Error creating fallback recording content")
        return create_simple_error_content(f"Critical error in fallback: {e}")


def create_simple_error_content(error_message: str):
    """Create very simple error content as last resort."""
    with ui.column().classes(
        "w-full h-full flex items-center justify-center p-6"
    ) as content:
        ui.label("Error").classes("text-2xl font-bold text-red-600 mb-4")
        ui.label(error_message).classes("text-gray-600 text-center")
        ui.button("Reload Page", on_click=lambda: ui.open("/")).classes(
            "mt-4 bg-blue-500 text-white"
        )

    return content


def _navigate_to_recording():
    """Navigate to recording step safely."""
    try:
        import asyncio

        asyncio.create_task(session_manager.show_default_workflow())
    except Exception as e:
        log_exception(e, "Error navigating to recording")
        ui.open("/")  # Fallback to page reload


def _retry_load_content(step_name: str):
    """Retry loading content for a step."""
    try:
        import asyncio

        async def retry():
            current_session = session_manager.get_current_session()
            if current_session:
                # Use status-first refresh for most steps
                if step_name == "recording":
                    # Recording needs full data refresh
                    await session_manager.refresh_session_for_form_data(
                        current_session.session_id
                    )
                else:
                    # Other steps can use status-only refresh
                    await session_manager.refresh_session(current_session.session_id)
            else:
                await session_manager.show_default_workflow()

        asyncio.create_task(retry())

    except Exception as e:
        log_exception(e, "Error retrying content load")
        ui.open("/")  # Fallback to page reload
