# views/main_view/callbacks.py
"""
Simplified callback setup using unified session management.
"""

from gojiberri.ui.utils.session_state import session_manager
from gojiberri.utils.logger import log_exception, error, debug


async def setup_sidebar_callbacks(sidebar_container):
    """Simplified sidebar callbacks using unified session management."""

    async def handle_session_click(session_id: str):
        """Handle session click with unified loading."""
        try:
            debug(f"Switching to session: {session_id}")

            # Single method call instead of multiple loading methods
            await session_manager.switch_to_session(session_id)

            # Explicitly refresh the sidebar to update highlighting immediately
            await sidebar_container.refresh_sessions()

            debug(f"Successfully switched to session: {session_id}")

        except Exception as e:
            log_exception(e, "Error switching to session")
            error(f"Failed to switch to session {session_id}: {e}")

    async def handle_new_session():
        """Handle new session with simplified workflow display."""
        try:
            debug("Starting new session workflow")

            # Single method call for default workflow
            await session_manager.show_default_workflow()

            debug("Successfully started new session workflow")

        except Exception as e:
            log_exception(e, "Error starting new session")
            error(f"Failed to start new session: {e}")

    sidebar_container.set_callbacks(
        on_session_click=handle_session_click, on_new_session=handle_new_session
    )


def setup_workflow_callbacks(workflow_container):
    """Simplified workflow callbacks using step progress."""

    async def handle_workflow_step_click(step_name: str):
        """Handle workflow step click with unified session refresh."""
        try:
            debug(f"Workflow step clicked: {step_name}")

            current_session = session_manager.get_current_session()

            if current_session:
                # Single refresh call instead of multiple different refresh methods
                await session_manager.refresh_current_session()
            else:
                # Show default workflow if no session
                await session_manager.show_default_workflow()

            debug(f"Successfully handled workflow step: {step_name}")

        except Exception as e:
            log_exception(e, "Error handling workflow step click")
            error(f"Failed to handle workflow step {step_name}: {e}")

    workflow_container.set_step_click_callback(handle_workflow_step_click)
