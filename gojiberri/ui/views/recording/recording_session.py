# views/recording/recording_session.py
"""
Simplified session data management using unified session loading.
"""

from typing import Dict, Any
from gojiberri.ui.utils.session_state import SessionState, session_manager
from gojiberri.utils.logger import debug, log_exception


async def prefill_session_data(components: Dict[str, Any], session_state: SessionState):
    """Pre-fill form with session data - uses unified loading approach."""
    try:
        # Check if we need full data for forms
        if not session_state.patient_name and not session_state.patient_dob:
            debug(
                f"Loading full session data for form prefill: {session_state.session_id}"
            )
            # Use unified loading method with force_full_data=True
            session_state = await session_manager.load_session(
                session_state.session_id, force_full_data=True
            )

        # Pre-fill form fields
        fill_patient_inputs(components, session_state)
        configure_recording_manager(components, session_state)

        debug(f"Pre-filled recording data for session {session_state.session_id}")

    except Exception as e:
        log_exception(e, "Error pre-filling session data")


def fill_patient_inputs(components: Dict[str, Any], session_state: SessionState):
    """Fill patient input fields with session data."""
    patient_inputs = components.get("patient_inputs", {})

    if session_state.patient_name and "name" in patient_inputs:
        patient_inputs["name"].value = session_state.patient_name

    if session_state.patient_dob and "dob" in patient_inputs:
        patient_inputs["dob"].value = session_state.patient_dob


def configure_recording_manager(
    components: Dict[str, Any], session_state: SessionState
):
    """Configure recording manager with session data."""
    recording_manager = components.get("recording_manager")
    if recording_manager:
        recording_manager.session_id = session_state.session_id
        recording_manager.session_date = session_state.session_date
        recording_manager.session_time = session_state.session_time
        recording_manager.session_created = True


# Simplified - no need for separate ensure_full_session_data_for_forms
# Just use session_manager.load_session(session_id, force_full_data=True)
