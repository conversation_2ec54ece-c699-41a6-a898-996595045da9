# views/recording/recording_data.py
"""
Data extraction functions for recording operations.
"""

from typing import Dict, Any
from gojiberri.utils.logger import error, debug, log_exception


def extract_patient_data(components: Dict[str, Any]) -> Dict[str, Any]:
    """Extract patient data from form components."""
    try:
        patient_inputs = components.get("patient_inputs", {})
        name = (
            patient_inputs.get("name", {}).value.strip()
            if "name" in patient_inputs
            else ""
        )
        dob = (
            patient_inputs.get("dob", {}).value.strip()
            if "dob" in patient_inputs
            else ""
        )

        return {
            "patient_name": name,
            "patient_dob": dob,
        }

    except Exception as e:
        log_exception(e, "Exception Error")
        error(f"Error extracting patient data: {e}")
        raise


def extract_recording_data(recording_manager) -> Dict[str, Any]:
    """Extract recording data for backend submission."""
    try:
        audio_data = recording_manager.get_audio_data()
        audio_metadata = recording_manager.get_audio_metadata()

        # Fallback to recorder if manager doesn't have data
        if not audio_data and recording_manager.audio_recorder:
            audio_data = recording_manager.audio_recorder.get_recording_data()
            audio_metadata = recording_manager.audio_recorder.get_recording_metadata()

        debug(f"Extracted recording data: {len(audio_data) if audio_data else 0} bytes")
        debug(f"===Audio Metadata During submission {audio_metadata}")


        return {
            "audio_data": audio_data,
            "audio_metadata": audio_metadata,
        }

    except Exception as e:
        log_exception(e, "Exception Error")
        error(f"Error extracting recording data: {e}")
        raise
