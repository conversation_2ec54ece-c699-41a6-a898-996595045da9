# views/recording/recording_handlers.py
"""
Simplified recording handlers using unified session management.
"""

from typing import Dict, Any, Optional
from gojiberri.ui.utils.session_state import SessionState, session_manager
from gojiberri.utils.logger import log_exception
from gojiberri.ui.utils import safe_show_notification
from gojiberri.ui.events import show_loader, hide_loader

from .recording_operations import (
    stop_active_recording,
    get_recording_manager,
    show_no_audio_error,
)
from .recording_validation import (
    validate_patient_info_for_skip,
    validate_recording_data,
)
from .recording_data import extract_patient_data, extract_recording_data
from .recording_utility import submit_recording, handle_connection_error


async def handle_end_recording(
    components: Dict[str, Any], session_state: Optional[SessionState]
):
    """Simplified recording handler using unified session management."""
    try:
        show_loader("Processing recording...")
        recording_manager = get_recording_manager(components)

        if not recording_manager:
            safe_show_notification("Recording manager not available", type="negative")
            return

        # Stop recording if active
        if recording_manager.is_recording:
            await stop_active_recording(recording_manager)

        # Ensure audio data is available
        if not await recording_manager.ensure_audio_data_available():
            show_no_audio_error()
            return

        # Get active session ID - simplified approach
        active_session_id = await get_active_session_id(
            session_state, recording_manager
        )
        if not active_session_id:
            safe_show_notification("No active recording session found", type="negative")
            return

        # Validate and submit
        if validate_recording_data(components, recording_manager):
            recording_data = extract_recording_data(recording_manager)
            await submit_recording(recording_data, active_session_id)

            # Single refresh call using unified method
            await session_manager.refresh_current_session()
            safe_show_notification("Recording saved successfully!", type="positive")

    except ConnectionError:
        handle_connection_error(session_manager.current_session_id)
    except Exception as e:
        log_exception(e, "Error ending recording")
        safe_show_notification(f"Error: {str(e)}", type="negative")
    finally:
        hide_loader()


async def handle_skip_to_document_upload(
    components: Dict[str, Any], session_state: Optional[SessionState]
):
    """Simplified skip handler using unified session management."""
    try:
        show_loader("Preparing to skip to document upload...")

        if not validate_patient_info_for_skip(components):
            return

        # Get or create session - simplified
        active_session_id = await ensure_active_session(session_state, components)
        if not active_session_id:
            safe_show_notification("Failed to prepare session", type="negative")
            return

        # Single refresh to get updated step progress
        await session_manager.refresh_current_session()
        safe_show_notification("Skipped to document upload step", type="positive")

    except Exception as e:
        log_exception(e, "Error skipping to document upload")
        safe_show_notification(f"Error: {str(e)}", type="negative")
    finally:
        hide_loader()


async def get_active_session_id(
    session_state: Optional[SessionState], recording_manager
) -> Optional[str]:
    """Get active session ID using simplified approach."""
    # Check session state first
    if session_state:
        return session_state.session_id

    # Check recording manager
    if (
        recording_manager
        and recording_manager.session_created
        and recording_manager.session_id
    ):
        return recording_manager.session_id

    # Check current session in manager
    current_session = session_manager.get_current_session()
    if current_session:
        return current_session.session_id

    return None


async def ensure_active_session(
    session_state: Optional[SessionState], components: Dict[str, Any]
) -> Optional[str]:
    """Ensure we have an active session, create if needed."""
    # Try to get existing session first
    active_session_id = await get_active_session_id(
        session_state, get_recording_manager(components)
    )

    if active_session_id:
        return active_session_id

    # Create new session if needed
    patient_data = extract_patient_data(components)
    try:
        return await session_manager.create_new_session(patient_data)
    except Exception as e:
        log_exception(e, "Failed to create session")
        return None
