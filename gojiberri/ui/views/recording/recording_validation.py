# views/recording/recording_validation.py
"""
Validation functions for recording operations.
"""

from typing import Dict, Any
from datetime import datetime
from gojiberri.utils.logger import error, debug, log_exception
from gojiberri.ui.utils import safe_show_notification
from gojiberri.ui.config.config_loader import config


def validate_patient_info_for_skip(components: Dict[str, Any]) -> bool:
    """Validate patient information before allowing skip."""
    try:
        patient_inputs = components.get("patient_inputs", {})
        name_component = patient_inputs.get("name")
        dob_component = patient_inputs.get("dob")

        if not name_component or not dob_component:
            safe_show_notification(
                "Patient information form not available", type="negative"
            )
            return False

        name = name_component.value.strip() if name_component.value else ""
        dob = dob_component.value.strip() if dob_component.value else ""

        return validate_patient_fields(name, dob)

    except Exception as e:
        log_exception(e, "Exception Error")
        error(f"Error validating patient info for skip: {e}")
        safe_show_notification(f"Validation error: {str(e)}", type="negative")
        return False


def validate_patient_fields(name: str, dob: str) -> bool:
    """Validate individual patient fields."""
    if not name:
        safe_show_notification(
            "Please enter patient name before proceeding", type="negative"
        )
        return False

    if not dob:
        safe_show_notification(
            "Please enter patient date of birth before proceeding", type="negative"
        )
        return False

    return validate_date_of_birth(dob)


def validate_date_of_birth(dob: str) -> bool:
    """Validate date of birth format and value."""
    try:
        dob_date = datetime.strptime(dob, "%Y-%m-%d")
        if dob_date > datetime.now():
            safe_show_notification(
                "Date of birth cannot be in the future", type="negative"
            )
            return False
        return True
    except ValueError:
        safe_show_notification(
            "Invalid date format. Please use YYYY-MM-DD.", type="negative"
        )
        return False


def validate_recording_data(components: Dict[str, Any], recording_manager) -> bool:
    """Validate recording data using configuration values."""
    try:
        if not validate_patient_info_exists(components):
            return False

        if not validate_recording_started(recording_manager):
            return False

        if not validate_audio_data_exists(recording_manager):
            return False

        return validate_audio_quality(recording_manager)

    except Exception as e:
        log_exception(e, "Exception Error")
        error(f"Error validating recording: {e}")
        safe_show_notification(f"Validation error: {str(e)}", type="negative")
        return False


def validate_patient_info_exists(components: Dict[str, Any]) -> bool:
    """Validate that patient information is present."""
    patient_inputs = components.get("patient_inputs", {})
    name = patient_inputs.get("name", {}).value if "name" in patient_inputs else ""
    dob = patient_inputs.get("dob", {}).value if "dob" in patient_inputs else ""

    if not name.strip():
        safe_show_notification("Please enter patient name", type="negative")
        return False

    if not dob.strip():
        safe_show_notification("Please enter patient date of birth", type="negative")
        return False

    return True


def validate_recording_started(recording_manager) -> bool:
    """Validate that recording was started."""
    if not recording_manager or not recording_manager.recording_started:
        safe_show_notification(
            "Please start recording before proceeding", type="negative"
        )
        return False
    return True


def validate_audio_data_exists(recording_manager) -> bool:
    """Validate that audio data exists."""
    has_audio = recording_manager.has_audio_data()

    if not has_audio:
        has_audio = try_get_audio_from_recorder(recording_manager)

    if not has_audio:
        show_no_audio_validation_error()
        return False

    return True


def try_get_audio_from_recorder(recording_manager) -> bool:
    """Try to get audio data directly from the audio recorder."""
    if not recording_manager.audio_recorder:
        return False

    if not recording_manager.audio_recorder.has_recording():
        return False

    try:
        recording_manager.audio_data = (
            recording_manager.audio_recorder.get_recording_data()
        )
        recording_manager.recording_metadata = (
            recording_manager.audio_recorder.get_recording_metadata()
        )
        debug("Successfully copied audio data from recorder to manager")
        return True
    except Exception as e:
        log_exception(e, "Exception Error")
        error(f"Error copying audio data from recorder: {e}")
        return False


def show_no_audio_validation_error():
    """Show detailed error message for missing audio data."""
    safe_show_notification(
        "No audio data available. Please make sure you recorded some audio.\n\n"
        "Tips:\n"
        "- Speak into your microphone while recording\n"
        "- Make sure your microphone is working\n"
        "- Try recording for at least a few seconds\n"
        "- Check that you didn't just click record and immediately stop",
        type="negative",
        duration=10.0,
    )


def validate_audio_quality(recording_manager) -> bool:
    """Validate audio data quality using configuration."""
    audio_validation = recording_manager.validate_audio_data()

    if not audio_validation.get("valid", False):
        error_msg = audio_validation.get("error", "Invalid audio data")
        safe_show_notification(f"Audio validation failed: {error_msg}", type="negative")
        return False

    return validate_audio_duration_and_size(audio_validation)


def validate_audio_duration_and_size(audio_validation: Dict[str, Any]) -> bool:
    """Validate audio duration and file size."""
    # Check minimum duration
    min_duration_ms = config.get("recording.min_duration_ms", 500)
    duration_ms = audio_validation.get("duration", 0)

    if duration_ms > 0 and duration_ms < min_duration_ms:
        safe_show_notification(
            f"Recording too short: {duration_ms}ms. Please record at least {min_duration_ms}ms of audio.",
            type="negative",
        )
        return False

    # Check minimum file size
    min_file_size = config.get("recording.min_file_size_bytes", 512)
    file_size = audio_validation.get("size", 0)

    if file_size > 0 and file_size < min_file_size:
        safe_show_notification(
            f"Recording file too small: {file_size} bytes. Minimum size: {min_file_size} bytes.",
            type="negative",
        )
        return False

    return True
