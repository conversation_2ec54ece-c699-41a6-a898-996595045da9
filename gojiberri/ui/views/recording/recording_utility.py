# views/recording/recording_utility.py
"""
Utility functions for recording operations - Updated for new API structure.
"""

from datetime import timezone
from typing import Any, Dict, Optional
from gojiberri.ui.api_client import api
from gojiberri.ui.events import show_loader
from gojiberri.utils.logger import debug, error, log_exception, warning
from gojiberri.ui.utils import safe_show_notification
from gojiberri.ui.utils.session_state import session_manager


async def submit_recording(recording_data: Dict[str, Any], session_id: str):
    """Submit recording data to backend using the new API structure."""
    try:
        debug(f"Submitting recording to backend for session {session_id}")

        audio_metadata = recording_data.get("audio_metadata", {})

        # Extract actual file format from metadata (single source of truth)
        file_format = _extract_file_format_from_metadata(audio_metadata)

        backend_recording_data = {
            "file_path": f"recording_{session_id}.{file_format}",
            "duration_milliseconds": None,
            "end_time": None,
        }

        # Extract duration from metadata
        audio_metadata = recording_data.get("audio_metadata", {})
        if audio_metadata.get("duration"):
            # Convert from milliseconds to seconds
            backend_recording_data["duration_milliseconds"] = audio_metadata["duration"]

        # Set end time to now if not provided
        if not backend_recording_data["end_time"]:
            from datetime import datetime

            backend_recording_data["end_time"] = datetime.now(timezone.utc).isoformat()

        show_loader("Submitting recording...")

        # Use the new complete_recording method
        response = await api.recordings.complete_recording(
            session_id=session_id,
            recording_data=backend_recording_data,
            audio_data=recording_data.get("audio_data"),
        )

        debug(f"Recording submitted successfully for session {session_id}")
        return response

    except Exception as e:
        log_exception(e, "Exception Error")
        raise


def handle_connection_error(active_session_id: Optional[str]):
    """Handle connection errors during recording submission."""
    safe_show_notification(
        "Recording saved locally. Will sync when connection restored.",
        type="warning",
        duration=10.0,
    )

    if active_session_id:
        # Use asyncio to handle the async call in sync context
        import asyncio

        try:
            asyncio.create_task(session_manager.refresh_session(active_session_id))
        except Exception as e:
            log_exception(e, "Exception Error")
            error(f"Error refreshing session after connection error: {e}")


async def handle_button_click(button, processing_text: str, action_func):
    """Handle button clicks with proper state management."""
    if button:
        button.disable()
        original_text = button.text
        button.text = processing_text

    try:
        await action_func()
    finally:
        if button:
            button.enable()
            button.text = original_text


def has_patient_info(components: Dict[str, Any]) -> bool:
    """Check if patient information is available and valid."""
    try:
        patient_inputs = components.get("patient_inputs", {})
        name_component = patient_inputs.get("name")
        dob_component = patient_inputs.get("dob")

        if not name_component or not dob_component:
            return False

        name = name_component.value.strip() if name_component.value else ""
        dob = dob_component.value.strip() if dob_component.value else ""

        return bool(name and dob)
    except Exception as e:
        log_exception(e, "Exception Error")
        debug(f"Error checking patient info: {e}")
        return False


def _extract_file_format_from_metadata(audio_metadata: Dict[str, Any]) -> str:
    """
    Extract file format from audio metadata MIME type.

    Uses the actual MIME type detected by JavaScript AudioRecorder as the
    authoritative source of truth for the recording format.

    Args:
        audio_metadata: Recording metadata containing MIME type

    Returns:
        str: File extension (e.g., "webm") extracted from MIME type

    Examples:
        "audio/webm;codecs=opus" → "webm"
        "audio/webm;codecs=vorbis" → "webm"
        "audio/webm" → "webm"
        "audio/mp3" → "mp3"
    """
    try:
        mime_type = audio_metadata.get("mime_type", "")

        if not mime_type:
            warning("No MIME type in metadata")
            return ""

        debug(f"Extracting format from MIME type: {mime_type}")

        # Extract base type from MIME type (remove codec info)
        # "audio/webm;codecs=opus" → "audio/webm"
        base_mime = mime_type.split(";")[0].strip()

        # Extract format from MIME type
        # "audio/webm" → "webm"
        if base_mime.startswith("audio/"):
            extracted_format = base_mime.replace("audio/", "")
            debug(f"Extracted format from metadata: {extracted_format}")
            return extracted_format
        else:
            warning(f"Unexpected MIME type format: {base_mime}")
            return ""

    except Exception as e:
        log_exception(e, "Error extracting format from metadata")
        return ""
