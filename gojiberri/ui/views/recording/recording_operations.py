# views/recording/recording_operations.py
"""
Recording operations and management functions.
"""

from typing import Dict, Any
from gojiberri.utils.logger import error, log_exception
from gojiberri.ui.utils import safe_show_notification
from gojiberri.ui.events import show_loader


async def stop_active_recording(recording_manager):
    """Stop active recording."""
    show_loader("Stopping recording...")
    try:
        await recording_manager.finalize_recording_async()
        safe_show_notification("Recording stopped", type="positive")
    except Exception as e:
        log_exception(e, "Exception Error")
        error(f"Error finalizing recording: {e}")
        safe_show_notification(f"Error stopping recording: {str(e)}", type="negative")
        raise


def get_recording_manager(components: Dict[str, Any]):
    """Get recording manager from components."""
    return components.get("recording_manager")


def show_no_audio_error():
    """Show error message for missing audio data."""
    safe_show_notification(
        "No audio data available. Please make sure you recorded some audio and try again.",
        type="negative",
        duration=8.0,
    )
