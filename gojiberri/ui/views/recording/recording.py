# views/recording/recording.py
"""
Clean recording view for desktop application - Modularized version.
"""

from nicegui import ui
from typing import Dict, Any, Optional

from gojiberri.ui.utils.session_state import SessionState
from gojiberri.ui.components import create_record_session_card, create_styled_button


# Import from local modules
from .recording_handlers import handle_end_recording, handle_skip_to_document_upload
from .recording_session import prefill_session_data
from .recording_utility import handle_button_click


async def create_recording_content(
    session_state: Optional[SessionState] = None,
) -> ui.column:
    """Create the main recording content."""
    with ui.column().classes(
        "w-full flex flex-col items-center justify-center h-[500px]"
    ) as content:
        create_title()
        components = create_record_session_card(ui.element())

        if session_state:
            await prefill_session_data(components, session_state)

        create_recording_controls(components, session_state)

    return content


def create_title():
    """Create the page title."""
    ui.label("Record Session").classes("text-5xl font-bold text-center test-title mb-6")


def create_recording_controls(
    components: Dict[str, Any], session_state: Optional[SessionState]
):
    """Create the recording control buttons."""
    with ui.element().classes(
        "fixed bottom-4 right-4 flex flex-col items-end space-y-2 z-10"
    ):
        create_end_recording_button(components, session_state)
        create_skip_link(components, session_state)


def create_end_recording_button(
    components: Dict[str, Any], session_state: Optional[SessionState]
):
    """Create the end recording button with proper state management."""

    async def end_recording_handler(event_data=None):
        button = components.get("end_recording_button")
        await handle_button_click(
            button,
            "Processing...",
            lambda: handle_end_recording(components, session_state),
        )

    components["end_recording_button"] = create_styled_button(
        button_text="End Recording",
        width_class="w-40",
        on_click_callback=end_recording_handler,
        additional_classes="bg-red-500 text-white font-bold test-btn",
        additional_styles="border-radius: 8px;",
    )


def create_skip_link(components: Dict[str, Any], session_state: Optional[SessionState]):
    """Create the skip to document upload link."""

    async def skip_handler(event_data=None):
        link = components.get("skip_link")
        original_text = link.text if link else "Skip to Document Upload"

        if link:
            link.text = "Processing..."

        try:
            await handle_skip_to_document_upload(components, session_state)
        finally:
            if link:
                link.text = original_text

    skip_link = ui.link(text="Skip to Document Upload").classes(
        "text-blue-500 underline test-link"
    )
    skip_link.on("click", skip_handler)
    components["skip_link"] = skip_link
