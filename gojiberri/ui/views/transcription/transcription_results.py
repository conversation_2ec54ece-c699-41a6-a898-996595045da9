# views/transcription/transcription_results.py
"""
Results management module for transcription functionality.

This module handles fetching, processing, and caching of transcription results,
"""

from typing import Dict, Any, Optional

from gojiberri.ui.api_client import api
from gojiberri.utils.logger import debug, warning, log_exception


class TranscriptionResultsManager:
    """
    Manages transcription results with caching and validation.

    This class handles all operations related to transcription results,
    including fetching, validation, caching, and processing.
    """

    def __init__(self):
        """Initialize the results manager."""
        self._result_cache: Dict[str, Dict[str, Any]] = {}

    async def fetch_transcription_results(
        self, session_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Fetch transcription results

        Args:
            session_id: Session ID for result retrieval

        Returns:
            Dict containing transcription results
        """
        try:
            debug(f"Fetching transcription results for session {session_id}")

            # Check cache first
            cached_result = self._get_cached_result(session_id)
            if cached_result:
                debug("Using cached transcription results")
                return cached_result

            # Try to fetch from API
            result_data = await api.transcription.get_transcription_result(session_id)

            # Validate result data
            if self._validate_result_data(result_data):
                # Process and cache successful API result
                processed_result = self._process_result_data(result_data, session_id)
                self._cache_result(session_id, processed_result)
                debug("API transcription results fetched and cached successfully")
                return processed_result
            else:
                debug("API result validation failed")
                return {}

        except Exception as e:
            log_exception(e, "Error fetching transcription results from API")
            warning(f"API fetch failed for session {session_id}: {e}")
            return {}

    def _validate_result_data(self, result_data: Dict[str, Any]) -> bool:
        """
        Validate transcription result data structure.

        Args:
            result_data: Raw result data from API

        Returns:
            bool: True if data is valid
        """
        try:
            # Check if results are available
            if not result_data.get("available", False):
                debug("Results marked as not available")
                return False

            # Check for transcription data
            transcription = result_data.get("transcription", {})
            if not transcription:
                debug("No transcription data found")
                return False

            # Validate required fields
            required_fields = ["full_text"]
            for field in required_fields:
                if field not in transcription:
                    debug(f"Missing required field: {field}")
                    return False

            # Check that full_text is not empty
            full_text = transcription.get("full_text", "").strip()
            if not full_text:
                debug("Transcription full_text is empty")
                return False

            debug("Result data validation passed")
            return True

        except Exception as e:
            log_exception(e, "Error validating result data")
            return False

    def _process_result_data(
        self, result_data: Dict[str, Any], session_id: str
    ) -> Dict[str, Any]:
        """
        Process result data - simplified to only include full text.

        Args:
            result_data: Raw result data from API
            session_id: Session ID for processing

        Returns:
            Dict: Simplified result data with only full text
        """
        try:
            # Extract transcription data
            transcription = result_data.get("transcription", {})

            # Get and clean the full text
            full_text = transcription.get("full_text", "").strip()
            if not full_text:
                full_text = "No transcription text available."

            # Build simplified result - only full text
            processed_result = {
                "session_id": session_id,
                "available": True,
                "transcription": {
                    "full_text": full_text,
                },
            }

            debug("Result data processed successfully (text only)")
            return processed_result

        except Exception as e:
            log_exception(e, "Error processing result data")
            return {}

    def _get_cached_result(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get cached result if available and not expired.

        Args:
            session_id: Session ID for cache lookup

        Returns:
            Dict: Cached result or None if not available/expired
        """
        if session_id not in self._result_cache:
            return None

        cached_data = self._result_cache[session_id]
        return cached_data

    def _cache_result(self, session_id: str, result_data: Dict[str, Any]) -> None:
        """
        Cache result data.

        Args:
            session_id: Session ID for caching
            result_data: Result data to cache
        """
        self._result_cache[session_id] = result_data
        debug(f"Results cached for session {session_id}")
