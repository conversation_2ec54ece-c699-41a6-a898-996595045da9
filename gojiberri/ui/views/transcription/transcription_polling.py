# views/transcription/transcription_polling.py
"""
Polling management module for transcription progress tracking.

This module handles all polling operations for transcription progress,
addressing the main issue where 100% progress doesn't automatically trigger completion.
Designed for long-running transcription tasks without timeout limitations.
"""

import asyncio
from typing import Any, Callable, Optional
from dataclasses import dataclass

from gojiberri.ui.config import config
from gojiberri.ui.api_client import api
from gojiberri.utils.logger import debug, error, log_exception


@dataclass
class PollingState:
    """Represents the current state of polling operation."""

    percentage: int = 0
    status: str = "not_started"
    is_complete: bool = False
    error_message: Optional[str] = None
    consecutive_errors: int = 0
    polling_duration: float = 0.0


class TranscriptionPollingManager:
    """
    Manages transcription progress polling with enhanced completion detection.

    This class addresses the main issue where transcription progress at 100%
    doesn't automatically display results by implementing robust completion
    detection with multiple validation layers.

    Designed for long-running transcription tasks (4-10+ hours) without timeout constraints.
    """

    def __init__(self):
        """Initialize the polling manager."""
        self._current_task: Optional[asyncio.Task] = None
        self._polling_state = PollingState()
        self._max_consecutive_errors = config.get(
            "transcription.max_consecutive_errors", 5
        )
        self._completion_threshold = 100

    async def start_polling(
        self,
        session_id: str,
        spinner: object,
        completion_callback: Callable,
        interval: float = config.get("transcription.interval", 2.0),
    ) -> None:
        """
        Start enhanced polling with automatic completion detection for long-running transcription.

        Args:
            session_id: Session ID for transcription tracking
            spinner: UI spinner instance for progress display
            completion_callback: Function to call when transcription completes
            interval: Polling interval in seconds (default 2.0 for long tasks)
        """
        try:
            debug(
                f"Starting long-running transcription polling for session {session_id}"
            )

            # Cancel any existing polling task
            await self._cancel_existing_polling()

            # Reset polling state
            self._polling_state = PollingState()

            # Initialize spinner
            if spinner:
                spinner.reset()

            # Create and start polling task
            self._current_task = asyncio.create_task(
                self._enhanced_polling_loop(
                    session_id, spinner, completion_callback, interval
                )
            )

            await self._current_task

        except asyncio.CancelledError:
            debug("Polling cancelled by user")
            raise
        except Exception as e:
            log_exception(e, "Error starting polling")
            error(f"Polling failed to start: {e}")
            raise

    async def stop_polling(self) -> None:
        """Stop current polling operation."""
        await self._cancel_existing_polling()

    async def _enhanced_polling_loop(
        self,
        session_id: str,
        spinner: object,
        completion_callback: Callable,
        interval: float,
    ) -> None:
        """
        Enhanced polling loop with robust completion detection for long-running tasks.

        """
        start_time = asyncio.get_event_loop().time()

        try:
            while True:
                # Update polling duration for monitoring
                current_time = asyncio.get_event_loop().time()
                self._polling_state.polling_duration = current_time - start_time

                # Log progress periodically for long-running tasks
                if (
                    int(self._polling_state.polling_duration) % 300 == 0
                    and int(self._polling_state.polling_duration) > 0
                ):
                    debug(
                        f"Long-running transcription - Duration: {self._polling_state.polling_duration:.0f}s, Progress: {self._polling_state.percentage}%"
                    )

                # Poll progress with error handling
                await self._poll_progress_with_validation(session_id, spinner)

                # Check for completion with enhanced validation
                if await self._check_completion_with_validation(session_id):
                    debug("Enhanced completion validation passed - triggering callback")

                    try:
                        await completion_callback()
                        debug("Completion callback executed successfully")
                        break
                    except Exception as e:
                        log_exception(e, "Error in completion callback")
                        # Continue polling in case of callback error

                # Check for error threshold
                if (
                    self._polling_state.consecutive_errors
                    >= self._max_consecutive_errors
                ):
                    error(
                        f"Maximum consecutive errors reached for session {session_id} after {self._polling_state.polling_duration:.0f}s"
                    )
                    break

                # Adaptive interval for very long tasks
                current_interval = self._get_adaptive_interval(interval)
                await asyncio.sleep(current_interval)

        except asyncio.CancelledError:
            debug(
                f"Polling loop cancelled after {self._polling_state.polling_duration:.0f}s"
            )
            raise
        except Exception as e:
            log_exception(e, "Error in polling loop")
            error(f"Polling loop failed: {e}")
            raise

    def _get_adaptive_interval(self, base_interval: float) -> float:
        """
        Get tiered adaptive polling interval optimized for long-running transcription tasks.


        Polling Strategy:
        - 0-5 minutes: Base interval (2s) - Initial responsiveness phase
        - 5-15 minutes: 5s - Early progress monitoring
        - 15-30 minutes: 10s - Established progress phase
        - 30+ minutes: 25s - Long-running efficiency mode

        Args:
            base_interval: Base polling interval (typically 2.0s)
        Returns:
            float: Optimized polling interval in seconds
        """
        duration_minutes = self._polling_state.polling_duration / 60

        # Tiered adaptive intervals for optimal long-running task efficiency
        if duration_minutes >= 30:
            # Long-running efficiency mode - significantly reduce server load
            optimized_interval = 25.0

        elif duration_minutes >= 15:
            # Established progress phase - moderate polling
            optimized_interval = 10.0
        elif duration_minutes >= 5:
            # Early progress monitoring - balanced approach
            optimized_interval = 5.0
        else:
            # Initial responsiveness phase - use base interval
            optimized_interval = base_interval

        return optimized_interval

    async def _poll_progress_with_validation(
        self, session_id: str, spinner: object
    ) -> None:
        """
        Poll progress with comprehensive validation and error handling.

        Args:
            session_id: Session ID for progress tracking
            spinner: UI spinner for progress display
        """
        try:
            # Get progress from API
            progress_data = (
                await api.transcription.get_transcription_progress_and_status(
                    session_id
                )
            )

            # Extract and validate data
            percentage = self._validate_percentage(progress_data.get("percentage", 0))
            status = progress_data.get("status", "not_started")

            # Update polling state
            self._polling_state.percentage = percentage
            self._polling_state.status = status
            self._polling_state.consecutive_errors = 0  # Reset error count on success

            # Update spinner display
            if spinner and hasattr(spinner, "set_percentage"):
                spinner.set_percentage(percentage)

            debug(
                f"Progress update - {percentage}% ({status}) - Duration: {self._polling_state.polling_duration:.0f}s"
            )

        except Exception as e:
            self._polling_state.consecutive_errors += 1
            self._polling_state.error_message = str(e)
            debug(
                f"Polling error (attempt {self._polling_state.consecutive_errors}) after {self._polling_state.polling_duration:.0f}s: {e}"
            )
            raise

    async def _check_completion_with_validation(self, session_id: str) -> bool:
        """
        Enhanced completion check with multiple validation layers.

        This method implements the fix for the main issue by using
        comprehensive validation before declaring completion.

        Args:
            session_id: Session ID for validation

        Returns:
            bool: True if transcription is definitively complete
        """
        try:
            # Primary validation: Check current polling state
            percentage_complete = (
                self._polling_state.percentage >= self._completion_threshold
            )
            status_complete = self._polling_state.status == "completed"

            if not (percentage_complete and status_complete):
                return False

            debug(
                f"Primary completion validation passed after {self._polling_state.polling_duration:.0f}s - performing secondary validation"
            )

            # Secondary validation: Re-verify with fresh API call
            fresh_data = await api.transcription.get_transcription_progress_and_status(
                session_id
            )

            fresh_percentage = fresh_data.get("percentage", 0)
            fresh_status = fresh_data.get("status", "not_started")

            secondary_valid = (
                fresh_percentage >= self._completion_threshold
                and fresh_status == "completed"
            )

            if not secondary_valid:
                debug(
                    "Secondary validation failed - transcription not actually complete"
                )
                return False

            debug(
                "Secondary completion validation passed - performing result availability check"
            )

            # Tertiary validation: Check if results are actually available
            result_available = await self._verify_results_available(session_id)

            if not result_available:
                debug("Results not yet available - continuing polling")
                return False

            debug(
                f"All completion validations passed after {self._polling_state.polling_duration:.0f}s - transcription is complete"
            )
            self._polling_state.is_complete = True
            return True

        except Exception as e:
            log_exception(e, "Error in completion validation")
            return False

    async def _verify_results_available(self, session_id: str) -> bool:
        """
        Verify that transcription results are actually available.

        This adds an extra layer of validation to ensure results
        can be fetched before declaring completion.

        Args:
            session_id: Session ID for result verification

        Returns:
            bool: True if results are available
        """
        try:
            # Use the transcription API to verify result availability
            verification_result = await api.transcription.verify_transcription_complete(
                session_id
            )

            debug(f"Result availability verification: {verification_result}")
            return verification_result

        except Exception as e:
            debug(f"Result verification failed: {e}")
            # If verification fails, assume results might be available
            # to prevent indefinite polling
            return True

    def _validate_percentage(self, percentage: Any) -> int:
        """
        Validate and normalize percentage value.

        Args:
            percentage: Raw percentage value from API

        Returns:
            int: Validated percentage (0-100)
        """
        try:
            if percentage is None:
                return 0

            # Convert to int and clamp to valid range
            validated = int(float(percentage))
            return max(0, min(100, validated))

        except (ValueError, TypeError):
            debug(f"Invalid percentage value: {percentage}")
            return 0

    async def _cancel_existing_polling(self) -> None:
        """Cancel any existing polling task."""
        if self._current_task and not self._current_task.done():
            debug("Cancelling existing polling task")
            self._current_task.cancel()
            try:
                await self._current_task
            except asyncio.CancelledError:
                debug("Existing polling task cancelled successfully")
                pass
