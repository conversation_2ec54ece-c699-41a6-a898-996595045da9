# views/transcription/transcription.py
"""
Main transcription view module with enhanced modular structure.

This module serves as the main entry point for transcription functionality,
following Gojiberri guidelines for modularity and consistent naming.
"""

from typing import Optional
from nicegui import ui

from gojiberri.ui.api_client import api
from gojiberri.ui.utils.error_fallback import create_error_fallback
from gojiberri.ui.utils.session_state import SessionState, session_manager
from gojiberri.utils.logger import debug, log_exception
from .transcription_ui import TranscriptionUIManager
from .transcription_polling import TranscriptionPollingManager
from .transcription_results import TranscriptionResultsManager


async def create_transcription_content(
    session_state: Optional[SessionState] = None,
) -> ui.column:
    """
    Create the main transcription page content with modular architecture.

    Args:
        session_state: Current session state containing session information

    Returns:
        ui.column: Main transcription content container
    """
    try:
        debug("Creating transcription content")

        # Initialize managers
        ui_manager = TranscriptionUIManager()
        polling_manager = TranscriptionPollingManager()
        results_manager = TranscriptionResultsManager()

        # Store polling manager reference in session state
        if session_state:
            session_state.polling_manager = polling_manager

        # Create main content container
        content = ui_manager.create_main_container()

        # Validate session state
        if not session_state or not session_state.session_id:
            ui_manager.create_no_session_error(content)
            return content

        # Check if transcription is already complete
        status = await api.transcription.get_transcription_progress_and_status(
            session_state.session_id
        )

        if status.get("percentage", 0) >= 100 and status.get("status") == "completed":
            debug("Transcription already complete, displaying results")
            # Display results immediately
            document_section = await ui_manager.create_document_section(content)

            transcription_data = await results_manager.fetch_transcription_results(
                session_state.session_id
            )

            if transcription_data and transcription_data.get("available", False):
                ui_manager.update_document_content(document_section, transcription_data)
                # Show the document section since results are ready
                document_section.style("display: block;")
            else:
                # Handle case where results aren't available
                ui_manager.create_no_session_error(content)

            return content

        # Create UI sections for in-progress transcription
        loading_section = ui_manager.create_loading_section(content)
        document_section = await ui_manager.create_document_section(content)

        # Start transcription workflow
        await _start_transcription_workflow(
            session_state.session_id,
            ui_manager,
            polling_manager,
            results_manager,
            loading_section,
            document_section,
            content,
        )

        return content

    except Exception as e:
        log_exception(e, "Error creating transcription content")
        # Clear polling manager reference on error
        if session_state:
            session_state.polling_manager = None

        return create_error_fallback(
            error_message=f"Failed to initialize transcription interface: {str(e)}",
            title="Initialization Error",
            session_id=session_state.session_id if session_state else None,
            show_session_info=True if session_state else False,
        )


async def _start_transcription_workflow(
    session_id: str,
    ui_manager: TranscriptionUIManager,
    polling_manager: TranscriptionPollingManager,
    results_manager: TranscriptionResultsManager,
    loading_section: ui.element,
    document_section: ui.element,
    main_container: ui.element,
) -> None:
    """
    Initiates the transcription workflow with proper resource management and cleanup.

    This function orchestrates the complete transcription process from polling start
    to completion handling, ensuring proper cleanup of polling resources to prevent
    conflicts when users switch between sessions.

    Args:
        session_id: Unique session identifier for transcription tracking
        ui_manager: Handles UI transitions and content updates
        polling_manager: Manages progress polling and completion detection
        results_manager: Fetches and processes transcription results
        loading_section: UI section displaying progress spinner
        document_section: UI section for displaying final results
        main_container: Main container for error fallback scenarios

    Key Behaviors:
        - Stores polling manager reference in session state for cleanup during session switches
        - Creates completion callback that handles both success and cleanup
        - Ensures polling manager reference is cleared on both success and error paths
        - Falls back to error UI if workflow initialization fails
    """
    try:
        debug(f"Starting transcription workflow for session {session_id}")

        # Store session state reference to enable polling cleanup when user switches sessions
        # This prevents polling conflicts and resource leaks across session boundaries
        session_state = session_manager.get_current_session()

        async def on_completion_callback():
            """
            Handles transcription completion and ensures proper resource cleanup.

            This callback is triggered when polling detects 100% completion and
            verified result availability. It coordinates the UI transition from
            loading to results display while cleaning up polling resources.
            """
            await _handle_transcription_completion(
                session_id,
                ui_manager,
                results_manager,
                loading_section,
                document_section,
                main_container,
            )

            # Clear polling manager reference to prevent cleanup conflicts
            # when user switches to different sessions after completion
            if session_state:
                session_state.polling_manager = None
                debug("Polling manager reference cleared on completion")

        # Start polling with enhanced completion detection for long-running transcription
        # Uses adaptive intervals and multiple validation layers to ensure reliable completion
        await polling_manager.start_polling(
            session_id=session_id,
            spinner=ui_manager.get_spinner_from_section(loading_section),
            completion_callback=on_completion_callback,
            interval=0.8,
        )

    except Exception as e:
        log_exception(e, "Error in transcription workflow")

        # Critical: Clear polling manager reference on error to prevent
        # orphaned polling tasks from interfering with future sessions
        if session_state:
            session_state.polling_manager = None

        # Replace entire content area with error UI instead of showing broken workflow
        create_error_fallback(
            error_message="Transcription processing error occurred.",
            title="Processing Error",
            session_id=session_id,
            show_session_info=True,
            container=main_container,  # Clears and replaces main container content
        )


async def _handle_transcription_completion(
    session_id: str,
    ui_manager: TranscriptionUIManager,
    results_manager: TranscriptionResultsManager,
    loading_section: ui.element,
    document_section: ui.element,
    main_container: ui.element,  # Add main container parameter
) -> None:
    """
    Handle transcription completion with automatic UI transition and error handling.

    Args:
        session_id: Session ID for result fetching
        ui_manager: UI management instance
        results_manager: Results management instance
        loading_section: Loading UI section to hide
        document_section: Document section to show
        main_container: Main container for error handling
    """
    try:
        debug("Handling transcription completion with automatic transition")

        # Fetch and validate results
        results = await results_manager.fetch_transcription_results(session_id)

        if results and results.get("available", False):
            debug(
                "Transcription results available - updating document and switching UI"
            )

            # Update document content with results
            ui_manager.update_document_content(document_section, results)

            # Perform UI transition
            ui_manager.transition_to_results(loading_section, document_section)

        else:
            debug("Transcription results not available - showing error screen")

            # Clear everything and show error
            create_error_fallback(
                error_message="Transcription unavailable",
                title="Feature Not Available",
                session_id=session_id,
                show_session_info=True,
                container=main_container,  # Clear and use main container
            )

    except Exception as e:
        log_exception(e, "Error handling transcription completion")

        # Clear everything and show error
        create_error_fallback(
            error_message=f"Failed to complete transcription processing: {str(e)}",
            title="Processing Error",
            session_id=session_id,
            show_session_info=True,
            container=main_container,  # Clear and use main container
        )
