# views/transcription/transcription_ui.py
"""
UI Management module for transcription functionality.

This module handles all UI creation and management operations for the transcription
"""

from nicegui import ui

from gojiberri.ui.components.buttons import create_styled_button
from gojiberri.ui.components.loading_spinner import create_loading_spinner
from gojiberri.utils.logger import debug, log_exception
from gojiberri.ui.utils.ui_helpers import safe_show_notification


class TranscriptionUIManager:
    """
    Manages all UI operations for transcription functionality.

    This class centralizes UI creation, updates, and transitions,
    providing a clean interface for other modules.
    """

    def __init__(self):
        """Initialize the UI manager."""
        self._current_spinner = None

    def create_main_container(self) -> ui.column:
        """
        Create the main container for transcription content.

        Returns:
            ui.column: Main content container with proper styling
        """
        return ui.column().classes("w-full h-full p-6")

    def create_loading_section(self, parent_container: ui.element) -> ui.element:
        """
        Create the loading section with progress spinner.

        Args:
            parent_container: Parent container to add loading section to

        Returns:
            ui.element: Loading section container
        """
        with parent_container:
            with ui.column().classes("w-full max-w-[1400px] mx-auto"):
                with ui.column().classes(
                    "w-full flex items-center justify-center py-12"
                ) as loading_container:

                    # Create title
                    ui.label("Transcription In Progress").classes(
                        "text-6xl font-bold text-center mb-8"
                    )

                    # Create spinner
                    spinner = create_loading_spinner(
                        size=300,
                        show_percentage=True,
                        animation_duration=1.0,
                        text_color="#000000",
                    )

                    # Store spinner reference
                    loading_container._spinner = spinner
                    self._current_spinner = spinner

        return loading_container

    async def create_document_section(self, parent_container: ui.element) -> ui.element:
        """
        Create the document review section.

        Args:
            parent_container: Parent container to add document section to
            session_state: Current session state

        Returns:
            ui.element: Document section container
        """
        with parent_container:
            with ui.column().classes(
                "w-full max-w-[1400px] mx-auto"
            ) as document_container:

                # Initially hidden
                document_container.style("display: none;")

                # Create document header
                ui.label("Review Document").classes(
                    "text-6xl font-bold text-gray-800 mb-4 text-center"
                )

                # Placeholder for dynamic content
                document_container._content_area = ui.column().classes("w-full")

        return document_container

    def create_no_session_error(self, parent_container: ui.element) -> None:
        """
        Create error content when no session is available.

        Args:
            parent_container: Parent container to add error content to
        """
        from gojiberri.ui.components.buttons import create_styled_button

        def handle_go_to_recording(event_data=None):
            """Navigate to recording step using session manager."""
            try:
                import asyncio
                from gojiberri.ui.utils.session_state import session_manager

                asyncio.create_task(session_manager.show_default_workflow())
            except Exception as e:
                from gojiberri.utils.logger import log_exception

                log_exception(e, "Error navigating to recording from no session error")
                # Fallback to page reload if session manager fails
                ui.navigate.reload()

        with parent_container:
            with ui.column().classes("w-full h-full flex items-center justify-center"):
                # Error icon with Gojiberri theme colors
                ui.icon("error").classes("text-6xl text-red-400 mb-4")

                # Title with Gojiberri theme colors
                ui.label("No Session Available").classes(
                    "text-2xl font-bold text-red-600 mb-2"
                )

                # Description with Gojiberri theme colors
                ui.label("Please start a recording session first.").classes(
                    "text-gray-600 mb-6"
                )

                # Use Gojiberri styled button component
                create_styled_button(
                    button_text="Go to Recording",
                    icon="mic",
                    width_class="w-auto",
                    on_click_callback=handle_go_to_recording,
                    additional_classes="mt-2",
                    additional_styles="padding: 8px 16px; border-radius: 6px;",
                )

    def get_spinner_from_section(self, loading_section: ui.element) -> object:
        """
        Get spinner instance from loading section.

        Args:
            loading_section: Loading section containing spinner

        Returns:
            object: Spinner instance or None
        """
        return getattr(loading_section, "_spinner", self._current_spinner)

    def transition_to_results(
        self, loading_section: ui.element, document_section: ui.element
    ) -> None:
        """
        Transition from loading to results display.

        This method addresses the main issue where results weren't
        automatically displayed at 100% progress.

        Args:
            loading_section: Loading section to hide
            document_section: Document section to show
        """
        try:
            debug("Performing UI transition from loading to results")

            # Hide loading section
            loading_section.style("display: none;")

            # Show document section
            document_section.style("display: block;")

            # Cancel any ongoing polling
            if self._current_spinner and hasattr(self._current_spinner, "polling_task"):
                if self._current_spinner.polling_task:
                    self._current_spinner.polling_task.cancel()

            debug("UI transition completed successfully")

        except Exception as e:
            log_exception(e, "Error during UI transition")

    def show_error_notification(self, message: str) -> None:
        """
        Show error notification to user.

        Args:
            message: Error message to display
        """
        safe_show_notification(message, type="negative")

    def update_document_content(
        self, document_section: ui.element, transcription_data: dict
    ) -> None:
        """
        Update document section with transcription data (text only).

        Args:
            document_section: Document section to update
            transcription_data: Transcription data to display
        """
        try:
            debug("Updating document content with transcription text")
            content_area = getattr(document_section, "_content_area", None)
            if not content_area:
                debug("No content area found in document section")
                return

            # Clear existing content
            content_area.clear()

            with content_area:
                self._create_transcript_display(transcription_data)
                self._create_action_buttons(transcription_data.get("session_id"))

            debug("Document content updated successfully")

        except Exception as e:
            log_exception(e, "Error updating document content")

    def _create_transcript_display(self, transcription_data: dict) -> None:
        """
        Create simplified transcript display area (text only).

        Args:
            transcription_data: Transcription data to display
        """
        transcription = transcription_data.get("transcription", {})
        full_text = transcription.get("full_text", "No transcription available.")

        # Create main transcript card - simplified design

        # Create editable textarea for the full text
        with ui.column().classes("w-full max-w-[1400px] mx-auto "):
            ui.label("Please review the transcription and correct any errors").classes(
                "text-sm font-medium "
            )
            # Create the textarea with basic styling
            textarea = ui.textarea(
                label="Patient Interview Transcript",
                value=full_text,
                placeholder="Transcript content will appear here...",
            )

            # Apply classes separately to avoid method chaining issues
            textarea.classes("w-full  mb-2")

            # Apply basic styling
            textarea.style(
                """
                min-height: 400px;
                font-family: 'Courier New', monospace;
                font-size: 14px;
                line-height: 1.6;
                background-color: #fafafa;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                padding: 16px;
            """
            )

            ui.add_head_html(
                """
                <style>
                    .q-textarea.q-field--labeled .q-field__native {
                        min-height: 560px !important;
                        max-height: 100% !important;
                        height: 100% !important;
                        flex-grow: 1;
                        padding-top: 1px;
                    }
                    .q-field__control {
                        color: rgb(0, 0, 0);
                        height: 56px;
                        max-width: 100%;
                        outline: none;
                    }
                </style>
            """
            )

        # Store reference for potential editing
        textarea._session_id = transcription_data.get("session_id")

        debug("Transcript display created (text only)")

    def _create_action_buttons(self, session_id: str) -> None:
        """
        Create simplified action buttons for transcript operations.

        Args:
            session_id: Current session ID
        """

        async def accept_handler(event_data=None):
            """Handle accept transcription button click."""
            debug(f"Accept transcription clicked for session {session_id}")
            safe_show_notification(
                "Accept transcription functionality coming soon!",
                type="info",
                duration=4.0,
            )

        with ui.row().classes("w-full justify-end mt-2 mb-2"):
            create_styled_button(
                button_text="Accept Transcription",
                icon="check_circle",
                width_class="w-auto",  # Allow button to size based on content
                on_click_callback=accept_handler,
            )
