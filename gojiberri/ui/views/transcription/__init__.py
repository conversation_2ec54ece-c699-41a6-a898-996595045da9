# views/transcription/__init__.py
"""
Modularized transcription module with enhanced architecture.

This module provides a clean interface for transcription functionality
while maintaining backward compatibility with existing code.
"""

from .transcription import create_transcription_content
from .transcription_ui import TranscriptionUIManager
from .transcription_polling import TranscriptionPollingManager, PollingState
from .transcription_results import TranscriptionResultsManager

# Maintain backward compatibility
__all__ = [
    "create_transcription_content",
    "TranscriptionUIManager",
    "TranscriptionPollingManager",
    "TranscriptionResultsManager",
    "PollingState",
]
