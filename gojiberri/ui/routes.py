"""
Updated route definitions for Single Page Application architecture.
"""

from nicegui import ui, app
from gojiberri.ui.config import settings
from gojiberri.ui.views.main_view import create_main_app_page


def setup_routes():
    """
    Set up single page route for the application.
    """
    pass


@ui.page("/")
async def serve_main_app():
    """Serve the main application page."""
    configure_ui()
    await create_main_app_page()


def configure_ui():
    """Configure UI static files and HTML headers."""
    app.add_static_files("/static", "static")
    ui.add_head_html(settings.HEAD_HTML)
