"""
Clean debug utilities with proper caller tracking and minimal noise.
Provides colored terminal output and file logging without debug utility clutter.
"""

import inspect
import datetime
import logging
import logging.handlers
import os

from pathlib import Path
from typing import Any, Literal, Optional, Dict, Union
import traceback

from gojiberri.config.config_loader import config


# ANSI color codes for terminal output
COLORS = {
    "reset": "\033[0m",
    "bold": "\033[1m",
    "red": "\033[31m",
    "green": "\033[32m",
    "yellow": "\033[33m",
    "blue": "\033[34m",
    "magenta": "\033[35m",
    "cyan": "\033[36m",
    "white": "\033[37m",
    "bg_red": "\033[41m",
    "bg_white": "\033[47m",
}

# Type definitions
LogLevel = Literal["debug", "info", "warning", "error"]

# Message type styles for terminal output
TYPE_STYLES: Dict[str, Dict[str, Union[str, bool]]] = {
    "debug": {"prefix": "DEBUG", "color": "cyan"},
    "info": {"prefix": "INFO", "color": "blue"},
    "warning": {"prefix": "WARNING", "color": "yellow"},
    "error": {"prefix": "ERROR", "color": "red"},
    "critical": {
        "prefix": "CRITICAL",
        "color": "bg_red",
        "text_color": "white",
        "bold": True,
    },
}


# Logging setup
_logger = None
_log_dir = None
_current_log_file = None


class CustomFormatter(logging.Formatter):
    """Custom formatter that uses caller info from extra data instead of actual call site."""

    def format(self, record):
        # Replace default logging location info with our custom caller info
        # This makes log files show your application code location, not logger.py
        if hasattr(record, "caller_file"):
            record.filename = record.caller_file
        if hasattr(record, "caller_function"):
            record.funcName = record.caller_function
        if hasattr(record, "caller_line"):
            record.lineno = record.caller_line

        return super().format(record)


def _check_file_size_and_rotate():
    """Check file size and manually rotate if needed."""
    global _logger

    if not _logger or not _logger.handlers:
        return

    max_bytes = config.get("logging.max_log_file_size_mb", 10) * 1024 * 1024

    handler = _logger.handlers[0]  # Get the time-based handler

    # Check if file exists and get its size
    if (
        hasattr(handler, "stream")
        and handler.stream
        and hasattr(handler.stream, "name")
    ):
        try:
            current_size = os.path.getsize(handler.stream.name)
            if current_size >= max_bytes:
                # Force rotation due to size limit
                handler.doRollover()
        except (OSError, AttributeError):
            # If we can't check size, continue normally
            pass


def _setup_logging():
    """Initialize clean logging system with time-based rotation and size limit."""
    global _logger, _log_dir, _current_log_file

    # Return existing logger if already initialized (singleton pattern)
    if _logger is not None:
        return _logger

    # Create logs directory if it doesn't exist
    log_dir = "logs"
    _log_dir = Path(log_dir)
    _log_dir.mkdir(exist_ok=True)

    # Create main logger instance and configure it
    _logger = logging.getLogger("gojiberri")

    # Convert config string to logging level constant (e.g., "DEBUG" -> logging.DEBUG)
    log_level = getattr(
        logging, config.get("logging.log_level", "DEBUG").upper(), logging.DEBUG
    )
    _logger.setLevel(log_level)
    _logger.handlers.clear()  # Remove any existing handlers to start fresh

    # Set up file logging with time-based rotation and size checking
    if config.get("logging.logger") == "file" and config.get(
        "logging.file_logging_enabled", True
    ):

        # Generate current date for filename
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        log_filename = f"gojiberri-{current_date}.log"

        # Time-based rotation handler with size safety
        time_handler = logging.handlers.TimedRotatingFileHandler(
            _log_dir / log_filename,
            when=config.get("logging.rotation_when", "midnight"),
            interval=config.get("logging.rotation_interval", 1),
            backupCount=config.get("logging.time_backup_count", 7),  # Keep 7 days
            encoding="utf-8",
        )
        time_handler.setLevel(log_level)

        # Custom formatter that shows our application caller info
        time_formatter = CustomFormatter(
            fmt="%(asctime)s.%(msecs)03d | %(levelname)-8s | %(filename)s:%(funcName)s:%(lineno)d | %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        time_handler.setFormatter(time_formatter)
        _logger.addHandler(time_handler)

    # Set up console/stdout logging if configured (alternative to file logging)
    if config.get("logging.logger") == "stdout":
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        # Simpler format for console output (no milliseconds)
        console_formatter = CustomFormatter(
            fmt="%(asctime)s | %(levelname)-8s | %(filename)s:%(funcName)s:%(lineno)d | %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        console_handler.setFormatter(console_formatter)
        _logger.addHandler(console_handler)

    return _logger


def _get_application_caller_info() -> Dict[str, Any]:
    """
    Get caller information from actual application code, skipping debug utility frames.
    This is like a detective that traces back through function calls to find
    where the log message originally came from in your application code,
    not from the logger itself.
    """
    try:
        # Get the complete call stack (like a tower of function calls)
        stack = inspect.stack()

        # Walk through the call stack starting from frame 3
        # Frame 0: _get_application_caller_info (this function)
        # Frame 1: debug_print (our logging function)
        # Frame 2: debug/info/error/etc (convenience functions)
        # Frame 3+: your actual application code (what we want!)
        for frame_index in range(3, len(stack)):
            frame_info = stack[frame_index]
            filename = os.path.basename(frame_info.filename)

            # Skip any files that are part of the logging system
            # We want to find the first file that's actually your application code
            if filename != "logger.py" and not filename.startswith("debug_"):
                return {
                    "filename": filename,
                    "function": frame_info.function,
                    "line_number": frame_info.lineno,
                    "caller_info": f"{filename}:{frame_info.function}:{frame_info.lineno}",
                }

        # Safety fallback: if we can't find application code, use frame 3
        frame_info = stack[3] if len(stack) > 3 else stack[-1]
        return {
            "filename": os.path.basename(frame_info.filename),
            "function": frame_info.function,
            "line_number": frame_info.lineno,
            "caller_info": f"{os.path.basename(frame_info.filename)}:{frame_info.function}:{frame_info.lineno}",
        }

    except Exception as e:
        return {"error": f"Error getting caller info: {str(e)}"}


def _get_clean_stack_trace() -> str:
    """Get clean stack trace for errors, excluding debug utility frames.
    This creates a readable stack trace that shows where the error occurred
    in your application code, filtering out the logging system internals
    so you can focus on debugging your actual code."""
    try:
        # Get the call stack but remove the last 3 frames (logger internals)
        stack = traceback.extract_stack()[:-3]  # Remove debug utility frames

        if not stack:
            return ""

        # Show only the last 3 application frames to keep output manageable
        # This gives you the most relevant context without too much noise
        trace_lines = []
        for frame in stack[-3:]:  # Show last 3 application frames
            # Skip any frames that are still from the logger system
            if not frame.filename.endswith("logger.py"):
                trace_lines.append(
                    f"  File {frame.filename}:{frame.lineno} in {frame.name}"
                )
                # Include the actual line of code if available
                if frame.line:
                    trace_lines.append(f"    {frame.line.strip()}")

        return "\n" + "\n".join(trace_lines) if trace_lines else ""

    except Exception:
        return ""


def _log_level_to_logging_level(level: LogLevel) -> int:
    """Convert debug level to Python logging level.
    This maps our simple level names to Python's logging constants
    so the built-in logging system knows how to handle them."""
    level_mapping = {
        "debug": logging.DEBUG,
        "info": logging.INFO,
        "warning": logging.WARNING,
        "error": logging.ERROR,
        "success": logging.INFO,
        "critical": logging.CRITICAL,
    }
    return level_mapping.get(level, logging.INFO)


def _should_log_level(message_level: LogLevel) -> bool:
    """Check if message should be logged based on current debug level.
    This implements log level filtering - if your config is set to 'WARNING',
    only WARNING, ERROR, and CRITICAL messages will be shown.
    DEBUG and INFO messages will be filtered out to reduce noise."""

    current_level = config.get("logging.log_level", "DEBUG").lower()

    # Define the hierarchy: debug < info < warning < error < critical < success
    # Messages at or above the current level will be shown
    level_order = ["debug", "info", "warning", "error", "critical", "success"]
    try:
        message_index = level_order.index(message_level)
        current_index = level_order.index(current_level)
        # Return True if message level is >= current config level
        return message_index >= current_index
    except ValueError:
        # If level not found, default to showing the message
        return True


def debug_print(
    message: Any,
    type: LogLevel = "debug",
    title: Optional[str] = None,
    show_caller: bool = True,
    show_time: bool = True,
    include_stack_trace: bool = False,
):
    """
    Print debug message with clean formatting and proper caller tracking.
    This is the main function that handles both console output (with colors)
    and file logging. It automatically finds where the message came from
    in your code and formats everything nicely.

    Args:
        message: What to log (gets converted to string)
        type: Log level (debug, info, warning, error, success, critical)
        title: Optional title shown in brackets [TITLE]
        show_caller: Whether to show file:function:line info
        show_time: Whether to show timestamp
        include_stack_trace: Whether to show stack trace (useful for errors)
    """

    # Check if this message level should be logged based on config
    if not _should_log_level(type):
        return

    # Check file size and rotate if needed (size safety limit)
    _check_file_size_and_rotate()

    # Initialize the logging system if not already done
    logger = _setup_logging()

    # Find out where this log message actually came from in your code
    caller_details = _get_application_caller_info()

    # Prepare all the message components
    message_str = str(message)
    time_str = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3] if show_time else ""
    title_str = f"[{title}] " if title else ""

    # Extract caller info for console display
    console_caller_info = ""
    if show_caller and "caller_info" in caller_details:
        console_caller_info = caller_details["caller_info"]

    # Generate colored console output if enabled
    if config.get("logging.console_enabled", True):
        # Get the styling information for this log level
        style = TYPE_STYLES.get(type, TYPE_STYLES["debug"])

        # Build the colored prefix parts: [time] | [LEVEL] | [caller]
        prefix_parts = []
        if time_str:
            prefix_parts.append(f"{COLORS['white']}{time_str}{COLORS['reset']}")

        # Apply color and formatting to the log level indicator
        style_color = COLORS.get(style.get("color", "white"), COLORS["white"])
        bold = COLORS["bold"] if style.get("bold", False) else ""
        style_prefix = style.get("prefix", "LOG")
        prefix_parts.append(f"{style_color}{bold}{style_prefix}{COLORS['reset']}")

        # Add caller information if available and requested
        if console_caller_info:
            prefix_parts.append(
                f"{COLORS['white']}{console_caller_info}{COLORS['reset']}"
            )

        # Combine all prefix parts with pipe separators
        console_prefix = " | ".join(prefix_parts)
        console_message = f"{title_str}{message_str}"

        # Special handling for critical messages (add emphasis with separators)
        if type == "critical":
            separator = f"{style_color}{bold}{'=' * 60}{COLORS['reset']}"
            print(separator)
            print(f"{console_prefix} | {console_message}")
            print(separator)
        else:
            print(f"{console_prefix} | {console_message}")

        # Add stack trace for errors if requested
        if include_stack_trace and type in ["error", "critical"]:
            stack_info = _get_clean_stack_trace()
            if stack_info:
                print(f"{style_color}Stack trace:{COLORS['reset']}{stack_info}")

    # Write to log files with proper formatting and caller information
    if logger:
        # Convert our log level to Python's logging level constants
        logging_level = _log_level_to_logging_level(type)
        file_message = f"{title_str}{message_str}"

        # Add stack trace to file logs for errors (helps with debugging)
        if include_stack_trace and type in ["error", "critical"]:
            stack_info = _get_clean_stack_trace()
            if stack_info:
                file_message += f" | Stack trace:{stack_info}"

        # Send to file logger with custom caller info that CustomFormatter will use
        # This ensures the log files show your application location, not logger.py
        logger.log(
            logging_level,
            file_message,
            extra={
                "caller_file": caller_details.get("filename", "unknown"),
                "caller_function": caller_details.get("function", "unknown"),
                "caller_line": caller_details.get("line_number", 0),
            },
        )


# Clean convenience functions with proper caller tracking
# These are the main functions you'll use in your code - they're simple
# wrappers around debug_print() that set the appropriate log level


def debug(message: Any, title: Optional[str] = None, **kwargs):
    """Log debug message from application code.
    Use for detailed diagnostic information that's only useful when debugging."""
    debug_print(message, type="debug", title=title, **kwargs)


def info(message: Any, title: Optional[str] = None, **kwargs):
    """Log info message from application code.
    Use for general information about what your program is doing."""
    debug_print(message, type="info", title=title, **kwargs)


def warning(message: Any, title: Optional[str] = None, **kwargs):
    """Log warning message from application code.
    Use when something unexpected happened but the program can still continue."""
    debug_print(message, type="warning", title=title, **kwargs)


def error(message: Any, title: Optional[str] = None, **kwargs):
    """Log error message from application code.
    Use when a serious problem occurred that affected functionality."""
    debug_print(message, type="error", title=title, **kwargs)


def log_exception(exc_or_message, context: str = ""):
    """
    Log exception with clean context and stack trace.
    This function can handle both Exception objects and string messages.
    It automatically includes stack traces to help you debug the problem.

    Args:
        exc_or_message: Either an Exception object or a string message
        context: Additional context about where/why the exception occurred
    """
    if isinstance(exc_or_message, Exception):
        # Handle actual Exception objects - extract type and message
        exc = exc_or_message
        context_str = f" in {context}" if context else ""
        debug_print(
            f"Exception occurred{context_str}: {type(exc).__name__}: {str(exc)}",
            type="error",
            title="EXCEPTION",
            include_stack_trace=True,
        )
    else:
        # Handle string messages (backward compatibility)
        message = str(exc_or_message)
        debug_print(
            message,
            type="error",
            title="EXCEPTION",
            include_stack_trace=True,
        )


def critical(message: Any, title: Optional[str] = None, **kwargs):
    """Log critical message from application code.
    Use for very serious errors that might cause the program to abort."""
    debug_print(message, type="critical", title=title, **kwargs)


# Initialize logging system when this module is imported
# This ensures the logger is ready to use as soon as you import the module
_setup_logging()
