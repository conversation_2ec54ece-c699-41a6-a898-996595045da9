# This file sets up a pytest fixture to configure Chrome WebDriver options for headless UI testing with NiceGUI and Selenium.
import pytest  # type: ignore
from selenium import webdriver  # type: ignore

pytest_plugins = ["nicegui.testing.plugin"]


@pytest.fixture
def chrome_options() -> webdriver.ChromeOptions:
    """Create and return Chrome options for testing."""
    options = webdriver.ChromeOptions()
    options.add_argument("--headless")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    return options
